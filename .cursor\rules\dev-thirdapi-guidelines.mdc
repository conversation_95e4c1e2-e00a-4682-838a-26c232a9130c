---
description: 第三方服务集成模拟返回数据服务开发规范
globs: 
  - "php/thirdapi/**"
alwaysApply: true
---

# 第三方服务集成模拟返回数据服务开发规范

## 🚨 架构边界规范 【基于index-new.mdc架构规范】

### 核心原则

**第三方服务集成模拟返回数据服务** 是一个专门的模拟服务，用于在本地开发环境中模拟真实第三方平台的API响应。

#### ✅ 模拟服务职责
- **仅进行模拟**：不会向真实第三方平台发起任何网络请求
- **参数验证**：严格按照真实第三方平台API文档验证参数格式
- **响应格式**：返回符合真实第三方平台API格式的模拟响应
- **状态模拟**：模拟成功率、延迟、错误状态等真实场景
- **日志记录**：记录详细的调用日志用于调试

#### ❌ 模拟服务不会做的事情
- 不会向真实的第三方平台发起任何网络请求
- 不会产生任何真实的费用
- 不会获取真实的用户数据
- 不会执行真实的业务操作
- **不包含环境切换逻辑**（环境切换在工具API接口服务中实现）

### 环境切换机制

```
本地开发: 工具API → 第三方模拟服务 → 模拟响应 (无真实调用)
生产环境: 工具API → 真实第三方平台 → 真实响应 (真实调用)
```

### 🚨 重要说明：模拟服务边界

**模拟服务的工作原理**：
1. **接收请求**：模拟服务接收来自工具API的请求
2. **参数验证**：按照真实第三方平台的要求验证参数
3. **内部模拟**：在模拟服务内部生成符合真实API格式的响应
4. **返回结果**：将模拟结果返回给工具API

**关键价值**：
- ✅ **开发效率**：本地开发无需依赖真实第三方平台
- ✅ **成本控制**：避免开发阶段产生任何真实费用
- ✅ **测试完整性**：可以模拟各种边界情况和异常状态
- ✅ **完全兼容**：确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**：工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**：模拟环境与真实环境完全隔离，无数据泄露风险

## 📋 项目概述

第三方服务集成模拟返回数据服务是专门为本地开发设计的模拟服务，它严格按照真实第三方服务的API接口文档要求，接收和模拟返回处理结果，支持"工具API接口服务"的本地开发工作。

### 🎯 核心职责

1. **完全兼容**: 严格按照真实第三方服务API文档实现
2. **智能模拟**: 支持成功、失败、超时等多种状态模拟
3. **参数验证**: 完整的参数格式和业务规则验证
4. **性能监控**: 内置性能监控和日志记录
5. **无缝切换**: 生产环境可通过配置直接切换到真实服务

### 🏗️ 支持的第三方服务

- **微信服务**: OAuth登录、微信支付
- **支付宝支付**: 统一收单、退款查询
- **短信服务**: 阿里云短信、腾讯云短信
- **邮件服务**: SMTP、SendCloud、模板邮件

## 🔧 技术架构

### 目录结构
```
php/thirdapi/
├── index.php              # 入口文件和路由分发
├── config/
│   ├── config.php         # 配置文件
│   └── routes.php         # 路由配置
├── controllers/
│   ├── WechatController.php    # 微信服务控制器
│   ├── AlipayController.php    # 支付宝控制器
│   ├── SmsController.php       # 短信服务控制器
│   ├── EmailController.php     # 邮件服务控制器
│   └── SystemController.php    # 系统管理控制器
├── utils/
│   ├── HttpHelper.php          # HTTP工具类
│   ├── Logger.php              # 日志记录类
│   ├── ErrorHandler.php        # 错误处理类
│   └── PerformanceMonitor.php  # 性能监控类
├── logs/                       # 日志目录
├── cache/                      # 缓存目录
└── README.md                   # API文档
```

### 核心组件

#### 1. 路由系统
- 基于数组配置的路由系统
- 支持GET和POST请求
- 自动控制器和方法映射

#### 2. 控制器层
- 每个第三方服务对应一个控制器
- 统一的错误处理和日志记录
- 参数验证和格式检查

#### 3. 工具类
- **HttpHelper**: HTTP请求处理、响应格式化
- **Logger**: 统一日志记录
- **ErrorHandler**: 异常处理
- **PerformanceMonitor**: 性能监控

## 🚨 开发规范

### 1. 控制器开发规范

#### 基础结构
```php
class ServiceController
{
    private $logger;
    private $config;
    private $mockConfig;
    private $service = 'service_name';
    
    public function __construct()
    {
        global $thirdPartyConfig, $mockResponseConfig;
        $this->logger = new Logger();
        $this->config = $thirdPartyConfig[$this->service];
        $this->mockConfig = $mockResponseConfig;
    }
}
```

#### 方法开发模式
```php
public function apiMethod()
{
    $startTime = microtime(true);
    
    try {
        // 1. 获取请求数据
        $data = HttpHelper::getRequestBody();
        
        // 2. 验证必需参数
        $requiredParams = ['param1', 'param2'];
        HttpHelper::validateRequiredParams($data, $requiredParams);
        
        // 3. 验证参数格式
        HttpHelper::validateParamFormat($data['email'], 'email', 'email');
        
        // 4. 模拟网络延迟
        HttpHelper::simulateDelay($this->service);
        
        // 5. 检查成功率
        if (!HttpHelper::simulateSuccessRate($this->service)) {
            return $this->generateErrorResponse('ERROR_CODE', '错误信息');
        }
        
        // 6. 生成模拟响应
        $response = $this->generateMockResponse($data);
        
        // 7. 记录日志
        $duration = microtime(true) - $startTime;
        $this->logger->logThirdPartyCall($this->service, 'method_name', $data, $response, $duration);
        
        return HttpHelper::successResponse($response, '成功信息');
        
    } catch (Exception $e) {
        $this->logger->error("方法异常: " . $e->getMessage());
        return HttpHelper::errorResponse('ERROR_CODE', $e->getMessage());
    }
}
```

### 2. 参数验证规范

#### 必需参数验证
```php
// 验证必需参数
HttpHelper::validateRequiredParams($data, ['param1', 'param2', 'param3']);
```

#### 格式验证
```php
// 邮箱格式验证
HttpHelper::validateParamFormat($data['email'], 'email', 'email');

// 手机号格式验证
HttpHelper::validateParamFormat($data['phone'], 'phone', 'phone');

// URL格式验证
HttpHelper::validateParamFormat($data['url'], 'url', 'callback_url');

// 数字验证
HttpHelper::validateParamFormat($data['amount'], 'numeric', 'amount');
```

### 3. 响应格式规范

#### 成功响应
```php
return HttpHelper::successResponse($data, '操作成功');
```

#### 错误响应
```php
return HttpHelper::errorResponse('ERROR_CODE', '错误信息', $additionalData);
```

#### XML响应（微信支付）
```php
header('Content-Type: application/xml; charset=utf-8');
return HttpHelper::arrayToXml($responseData);
```

### 4. 模拟配置规范

#### 服务配置
```php
'service_name' => [
    'enabled' => true,
    'success_rate' => 95,           // 成功率百分比
    'delay_range' => [100, 500],    // 延迟范围（毫秒）
    'app_id' => 'mock_app_id',
    'app_secret' => 'mock_secret'
]
```

#### 模拟数据配置
```php
'mock_data' => [
    'user_info' => [
        'names' => ['张三', '李四', '王五'],
        'cities' => ['北京', '上海', '广州']
    ]
]
```

### 5. 日志记录规范

#### API调用日志
```php
$this->logger->logThirdPartyCall(
    $this->service,     // 服务名称
    'method_name',      // 方法名称
    $requestData,       // 请求参数
    $responseData,      // 响应数据
    $duration          // 执行时间
);
```

#### 错误日志
```php
$this->logger->error("错误描述: " . $e->getMessage(), [
    'service' => $this->service,
    'method' => 'method_name',
    'params' => $requestData
]);
```

## 🔐 安全规范

### 1. 参数安全
- 所有输入参数必须验证
- 敏感信息在日志中自动脱敏
- 防止SQL注入和XSS攻击

### 2. 签名验证
```php
// 简化的签名验证（模拟环境）
private function verifySignature($data)
{
    // 在模拟环境中，简化签名验证
    return isset($data['sign']) && !empty($data['sign']);
}
```

### 3. 错误信息
- 不暴露系统内部信息
- 统一错误码和错误信息
- 详细错误信息仅记录在日志中

## 📊 性能规范

### 1. 响应时间
- 模拟真实网络延迟
- 支持可配置的延迟范围
- 监控慢请求

### 2. 内存使用
- 避免内存泄漏
- 及时释放大对象
- 监控内存使用情况

### 3. 并发处理
- 支持多用户同时访问
- 无状态设计
- 线程安全

## 🧪 测试规范

### 1. 单元测试
- 每个控制器方法都要有测试
- 覆盖成功和失败场景
- 参数验证测试

### 2. 集成测试
- 完整的API调用流程测试
- 错误处理测试
- 性能测试

### 3. 兼容性测试
- 与真实API的兼容性验证
- 不同参数组合测试
- 边界条件测试

## 🔄 部署规范

### 1. 环境配置
- 开发环境：`https://thirdapi.tiptop.cn/`
- 生产环境：直接切换到真实第三方服务

### 2. 配置管理
- 敏感配置使用环境变量
- 支持动态配置更新
- 配置版本控制

### 3. 监控告警
- 服务可用性监控
- 错误率监控
- 性能指标监控

## 📚 API文档规范

### 1. 接口文档
- 每个接口都要有完整文档
- 包含请求示例和响应示例
- 错误码说明

### 2. 更新维护
- 与真实API文档保持同步
- 版本变更记录
- 兼容性说明

## 🔗 对接说明 【基于index-new.mdc架构规范】

### 工具API接口服务对接

**🚨 重要说明：环境切换机制在工具API接口服务中实现**

根据 `index-new.mdc` 架构规范，本项目包含五大核心组件：
1. Python用户终端工具
2. WEB网页工具
3. 管理后台
4. **工具API接口服务** ← 环境切换机制在此实现
5. 第三方服务集成模拟返回数据服务 ← 仅负责模拟，不包含环境切换

**对接方式**：
```php
// 🚨 工具API接口服务中的调用示例
// 环境切换配置在 php/api/config/ai.php 中的 third_party_config 部分
// 使用 ThirdPartyServiceClient 实现自动环境切换

use App\Services\ThirdPartyServiceClient;

// 调用微信服务（自动根据环境切换）
$response = ThirdPartyServiceClient::call('wechat', [
    'appid' => 'mock_app_id',
    'redirect_uri' => 'https://yourdomain.com/callback',
    'response_type' => 'code',
    'scope' => 'snsapi_userinfo'
]);

// 调用支付宝服务（自动根据环境切换）
$response = ThirdPartyServiceClient::call('alipay', [
    'out_trade_no' => 'ORDER_' . time(),
    'total_amount' => '0.01',
    'subject' => '测试商品'
]);

// 检查当前服务模式
$mode = ThirdPartyServiceClient::getServiceMode(); // 'mock' 或 'real'
$isMock = ThirdPartyServiceClient::isMockMode(); // true 或 false

// 验证平台配置
$validation = ThirdPartyServiceClient::validatePlatformConfig('wechat');
if (!$validation['valid']) {
    throw new Exception($validation['error']);
}
```

**环境切换配置**：
```php
// 🚨 环境切换在工具API接口服务中配置
// php/api/config/ai.php 中的 third_party_config 部分

'third_party_config' => [
    'service_mode' => env('THIRD_PARTY_MODE', 'mock'), // mock | real
    'mock_service' => [
        'base_url' => env('THIRD_PARTY_MOCK_URL', 'https://thirdapi.tiptop.cn'),
        'timeout' => env('THIRD_PARTY_MOCK_TIMEOUT', 30),
    ],
    'platforms' => [
        'wechat' => [
            'mock_endpoint' => '/wechat/oauth/authorize',
            'real_api' => [
                'app_id' => env('WECHAT_APP_ID', ''),
                'app_secret' => env('WECHAT_APP_SECRET', ''),
            ],
        ],
        'alipay' => [
            'mock_endpoint' => '/alipay/trade/page/pay',
            'real_api' => [
                'app_id' => env('ALIPAY_APP_ID', ''),
                'private_key' => env('ALIPAY_PRIVATE_KEY', ''),
            ],
        ],
        // ... 其他平台配置
    ],
],
```

**环境变量配置**：
```bash
# .env 文件配置
# 🚨 第三方服务环境切换配置
THIRD_PARTY_MODE=mock  # mock | real

# 第三方模拟服务配置（开发环境）
THIRD_PARTY_MOCK_URL=https://thirdapi.tiptop.cn
THIRD_PARTY_MOCK_TIMEOUT=30

# 真实服务配置（生产环境）
WECHAT_APP_ID=your_real_app_id
WECHAT_APP_SECRET=your_real_app_secret
ALIPAY_APP_ID=your_real_app_id
ALIPAY_PRIVATE_KEY=your_real_private_key
```

### 模拟数据配置

**自定义模拟响应**：
```php
// 在 thirdapi/config/config.php 中添加自定义模拟数据
$mockResponseData['wechat_userinfo']['custom_users'] = [
    [
        'openid' => 'mock_openid_001',
        'nickname' => '测试用户1',
        'headimgurl' => 'https://mock-avatar.com/001.jpg'
    ]
];
```

**错误模拟配置**：
```php
// 自定义错误响应
$thirdPartyErrorCodes['CUSTOM_ERROR'] = [
    'code' => 9001,
    'message' => '自定义错误信息'
];
```

### 架构边界验证

**验证模拟服务边界**：
```php
// 验证是否真的不发起真实请求
function validateMockBoundary() {
    // 1. 检查网络监控 - 确认无外部请求
    // 2. 检查日志记录 - 确认仅有模拟日志
    // 3. 检查响应数据 - 确认为模拟数据
    // 4. 检查费用产生 - 确认无真实费用
    // 5. 检查业务操作 - 确认无真实业务执行
}
```

## ⚠️ 注意事项

1. **严格遵守架构边界**：绝不向真实第三方平台发起请求
2. **保持API兼容性**：确保响应格式与真实API完全一致
3. **合理设置成功率**：模拟真实环境的成功率
4. **详细记录日志**：便于调试和问题排查
5. **定期更新配置**：跟随真实第三方平台的API变化
6. **环境隔离**：确保模拟环境与生产环境完全隔离
7. **数据安全**：模拟数据不包含真实敏感信息
8. **业务边界**：确保不执行任何真实的业务操作
