---
description: AI视频创作工具系统架构规范
globs: 
alwaysApply: true
---

# AI视频创作工具系统架构规范

## 📋 项目概述

### 🎯 项目定位
本项目是一个完整的AI视频创作工具生态系统，包含Python用户终端工具、WEB网页工具、管理后台、工具API接口服务和AI服务集成模拟返回数据服务五大核心组件。

### 🏗️ 系统架构设计原则
- **职责边界清晰**：每个组件职责明确，避免功能重叠
- **服务解耦**：组件间通过标准API接口通信，降低耦合度
- **资源本地化**：用户创作资源由Python工具直接从AI平台下载到本地
- **可选发布机制**：作品发布为增值服务，用户可选择是否发布到广场

## 🔧 开发环境配置

### 当前开发环境
- **操作系统**: Windows 11
- **Python**: 3.12
- **Web服务器**: Nginx 1.26.2
- **PHP**: 8.1.29
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2

### 生产环境规划
- **管理后台**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **工具API接口服务**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **WEB网页工具**: CentOS 8 Stream + Nginx（静态部署）
- **Python用户终端工具**: Windows 和 Mac（客户端应用）
- **AI服务集成模拟返回数据服务**: 本地开发专用，生产环境直连真实AI平台

## 📁 项目目录结构

```
项目根目录/
├── php/
│   ├── backend/          # 管理后台
│   ├── api/              # 工具API接口服务
│   ├── web/              # WEB网页工具
│   ├── aiapi/            # AI服务集成模拟返回数据服务
│   └── thirdapi/         # 第三方服务集成模拟返回数据服务
├── python/               # Python用户终端工具
└── .cursor/
    └── rules/            # 开发规范文档
        ├── index-new.mdc                    # 本文档
        ├── dev-aiapi-guidelines.mdc         # AI服务集成开发规范
        ├── dev-thirdapi-guidelines.mdc      # 第三方服务集成开发规范
        ├── dev-api-guidelines-pyapi.mdc     # Python工具API接口规范
        ├── dev-api-guidelines-webapi.mdc    # WEB工具API接口规范
        └── dev-api-guidelines-adminapi.mdc  # 管理后台API接口规范
```

## 🎯 核心组件职责定义

### 1. AI服务集成模拟返回数据服务 (@php/aiapi/)

**核心职责**：
- 在保持不同AI平台API接口特性前提下集成在一起
- 根据第三方AI的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的AI平台API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包

**作用期限**：
- 作用于支持"Python用户终端工具"本地开发阶段完成
- "Python用户终端工具"上线后通过配置修改AI平台配置直接绕过本服务
- 请求真实线上AI平台的API接口

**开发规范文档**: `@.cursor/rules/dev-aiapi-guidelines.mdc`

### 2. 第三方服务集成模拟返回数据服务 (@php/thirdapi/)

**核心职责**：
- 在保持不同第三方服务API接口特性前提下集成在一起
- 根据第三方服务的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的第三方服务API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持微信登录/支付、支付宝支付、短信服务、邮件服务

**作用期限**：
- 作用于支持"Python用户终端工具"本地开发阶段完成
- "Python用户终端工具"上线后通过配置修改第三方服务配置直接绕过本服务
- 请求真实线上第三方服务的API接口

**支持的第三方服务**：
- **微信服务**: OAuth登录、微信支付（统一下单、查询、退款等）
- **支付宝支付**: 统一收单、交易查询、退款处理
- **短信服务**: 阿里云短信、腾讯云短信、验证码验证
- **邮件服务**: SMTP发送、SendCloud、模板邮件

**服务地址**: `https://thirdapi.tiptop.cn/`

**开发规范文档**: `@.cursor/rules/dev-thirdapi-guidelines.mdc`

#### AI服务集成模拟机制架构图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Python用户终端工具] --> B[工具API接口服务]
        B --> E[AI服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[DeepSeek API格式模拟<br/>剧情生成/角色生成]
        E -.->|仅模拟，不真实调用| F2[LiblibAI API格式模拟<br/>图像生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F3[KlingAI API格式模拟<br/>图像生成/视频生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F4[MiniMax API格式模拟<br/>全业务支持]
        E -.->|仅模拟，不真实调用| F5[火山引擎豆包 API格式模拟<br/>语音合成/音效生成/音色生成]

        B --> T[第三方服务集成模拟返回数据服务]
        T -.->|仅模拟，不真实调用| G1[微信服务API格式模拟<br/>OAuth登录/微信支付]
        T -.->|仅模拟，不真实调用| G2[支付宝API格式模拟<br/>统一收单/退款查询]
        T -.->|仅模拟，不真实调用| G3[短信服务API格式模拟<br/>阿里云/腾讯云短信]
        T -.->|仅模拟，不真实调用| G4[邮件服务API格式模拟<br/>SMTP/SendCloud]
    end

    subgraph "生产环境"
        A2[Python用户终端工具] --> B2[工具API接口服务]
        B2 --> F6[真实第三方AI平台<br/>DeepSeek/LiblibAI/KlingAI<br/>MiniMax/火山引擎豆包]
        B2 --> G5[真实第三方服务平台<br/>微信/支付宝/阿里云/腾讯云]
    end

    style E fill:#fce4ec,stroke:#e91e63
    style T fill:#fff8e1,stroke:#ff9800
    style F1 fill:#ffebee
    style F2 fill:#ffebee
    style F3 fill:#ffebee
    style F4 fill:#ffebee
    style F5 fill:#ffebee
    style G1 fill:#ffebee
    style G2 fill:#ffebee
    style G3 fill:#ffebee
    style G4 fill:#ffebee
    style F6 fill:#e8f5e8,stroke:#4caf50
    style G5 fill:#e8f5e8,stroke:#4caf50
```

#### AI服务调用流程对比图

```mermaid
sequenceDiagram
    participant P as Python工具
    participant API as 工具API接口服务
    participant Mock as AI模拟服务
    participant Real as 真实AI平台

    Note over P,Mock: 本地开发阶段
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Mock: 真实调用AI平台格式接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Note over Mock: ✅ 唯一模拟职责<br/>1. 按对应AI平台要求验证参数<br/>2. 模拟对应平台响应状态
    alt 参数验证失败
        Mock->>API: 返回对应AI平台格式参数错误
    else 参数验证通过
        Mock->>API: 模拟成功/失败/超时状态
    end
    API->>P: 透明传递模拟结果

    Note over P,Real: 生产环境
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Real: 真实调用对应AI平台<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Real->>API: 返回真实结果
    API->>P: 透明传递真实结果
```

#### 核心机制说明

**AI服务集成模拟返回数据服务** 的核心作用机制：

1. **接收标准请求**：
   - 接收来自"工具API接口服务"的请求
   - 请求格式完全按照真实第三方AI平台的API文档要求
   - 包含相同的参数结构、认证方式、数据格式

2. **数据验证与模拟响应**：
   - 按照真实AI平台（DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包）的要求对提交数据进行严格验证
   - **验证不通过**：模拟真实AI平台的参数错误结果返回
   - **验证通过**：模拟成功、失败、超时等其中一种状态返回
   - 返回符合真实API规范的数据结构

3. **支持本地开发**：
   - 让开发者在本地环境就能完整测试所有AI功能
   - 无需真实调用第三方AI平台（避免费用、网络依赖）
   - 可以模拟各种异常情况进行充分测试

4. **环境切换机制**：
   - 本地开发：工具API → 模拟服务 → 模拟响应（**不发生真实第三方调用**）
   - 生产环境：工具API → 真实第三方平台 → 真实响应
   - 通过配置文件轻松切换，无需修改业务代码

#### 🚨 重要说明：模拟服务边界

**模拟服务的工作原理**：
1. **接收请求**：模拟服务接收来自工具API的请求
2. **参数验证**：按照真实第三方平台的要求验证参数
3. **内部模拟**：在模拟服务内部生成符合真实API格式的响应
4. **返回结果**：将模拟结果返回给工具API

**❌ 模拟服务不会做的事情**：
- 不会向真实的第三方平台发起任何网络请求
- 不会产生任何真实的费用
- 不会获取真实的用户数据
- 不会执行真实的业务操作

**✅ 模拟服务会做的事情**：
- 验证请求参数的格式和完整性
- 模拟各种响应状态（成功、失败、超时等）
- 返回符合真实API格式的模拟数据
- 记录详细的调用日志

#### 🚨 关键架构边界规范

**模拟行为边界铁律**：

1. **AI服务集成模拟返回数据服务**：
   - ✅ **唯一模拟职责**：仅在接收数据验证后进行模拟行为
   - ✅ **模拟范围**：数据验证、状态返回、响应格式
   - ✅ **模拟时机**：仅在数据验证完成后执行模拟逻辑

2. **工具API接口服务**：
   - ❌ **严禁模拟行为**：不允许在程序代码中进行任何模拟行为
   - ✅ **真实调用职责**：必须真实调用AI服务（模拟服务或真实服务）
   - ✅ **透明传递**：请求和响应数据必须透明传递，不得修改

3. **架构违规检查**：
   - ❌ 工具API接口服务中出现模拟逻辑代码
   - ❌ 工具API接口服务中硬编码返回模拟数据
   - ❌ 工具API接口服务中包含假数据生成逻辑
   - ❌ 绕过AI服务调用直接返回结果

**关键价值**：
- ✅ **开发效率**：本地开发无需依赖真实第三方平台
- ✅ **成本控制**：避免开发阶段产生任何真实费用
- ✅ **测试完整性**：可以模拟各种边界情况和异常状态
- ✅ **完全兼容**：确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**：工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**：模拟环境与真实环境完全隔离，无数据泄露风险

### 3. 工具API接口服务 (@php/api/)

**核心职责**：
- 本地开发阶段依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"
- 集成多家AI平台的API接口提供对应AI平台的API接口服务
- 集成多种第三方服务的API接口（微信、支付宝、短信、邮件等）
- 开发支持"Python用户终端工具"的AI视频创作功能
- 支持"WEB网页工具"作品广场和"管理后台"功能实现

#### 3.1 Python用户终端工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/PyApi`
**业务层目录**: `@php/api/app/Services/PyApi`

**业务逻辑职责**：
- 创建AI创作视频任务
- AI任务调度（含文生文、图生图、图生视频、生成语音、生成音效、生成音乐等所有需要AI的功能）
- 数据处理
- 作品发布
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**WebSocket服务职责**：
- 仅为Python工具提供实时通信（AI生成进度推送）

**不包含职责**：
- 不储存且不中转用户创作过程中AI生成的资源
- 视频编辑处理
- 客户端UI逻辑
- 本地文件操作

#### 3.2 WEB网页工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/WebApi`
**业务层目录**: `@php/api/app/Services/WebApi`

**业务逻辑职责**：
- 功能介绍查询
- 价格方案查询
- 作品数据查询（支持分类筛选、搜索查看、作品详情展示）
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**响应式设计**：
- 支持PC端、移动端、Python工具嵌入（1200px/800px窗口）

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建

#### 3.3 支持"管理后台"的API接口

**控制器目录**: `@php/api/app/Http/Controllers/AdminApi`
**业务层目录**: `@php/api/app/Services/AdminApi`

**业务逻辑职责**：
- 系统配置管理（AI平台配置、系统参数设置）
- 用户管理（用户信息、权限管理、账户状态）
- 内容管理（作品审核、内容监控、违规处理）
- 数据统计（用户统计、收入统计、使用情况分析）
- 积分系统管理（积分规则、充值记录、消费明细）
- 代理系统管理（代理审核、佣金结算、推广数据）
- 素材库管理（音色库、音效库、音乐库、风格库、角色库）
- 系统监控（性能监控、错误日志、API调用统计）
- 财务管理（收入报表、退款处理、财务对账）

### 4. Python用户终端工具 (@python/)

**技术栈**: Python + PySide6 + PyInstaller + WebSocket客户端

**核心创作职责**（调用"Python用户终端工具的API接口"支持）：
- 选风格+写剧情
- 绑角色
- 生成图像
- 视频编辑
- 本地导出

**可选发布职责**（调用"Python用户终端工具的API接口"支持）：
- 作品发布到广场（用户自主选择）

**客户端处理职责**（调用"Python用户终端工具的API接口"支持）：
- 资源本地化
- 视频时间轴编辑
- 本地素材合成
- UI交互逻辑
- 作品导出

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**实时通信职责**（调用"Python用户终端工具的API接口"支持）：
- 通过WebSocket接收AI生成进度推送

### 5. WEB网页工具 (@php/web/)

**展示职责**（调用"WEB网页工具的API接口"支持）：
- 首页工具展示
- 功能介绍
- 价格方案
- 作品展示

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**作品广场职责**：
- 作品展示浏览
- 分类筛选
- 搜索查看
- 作品详情展示

**响应式设计**：
- 支持PC端、移动端

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建

### 6. 管理后台 (@php/backend/)

**基于Laravel 10开发的管理后台**

**数据管理职责**：
- AI引擎配置
- 音色库、音效库、音乐库管理
- 风格库、角色库管理
- 作品库、会员库管理
- 积分明细管理

**配置管理职责**：
- 第三方AI的API接口地址和密钥管理
- 系统参数配置
- 业务规则配置

**系统管理职责**：
- 用户权限管理
- 系统监控
- 数据统计分析
- 内容审核管理

## 🔄 项目依赖关系

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Python用户终端工具] --> B[工具API接口服务]
        C[WEB网页工具] --> B
        D[管理后台] --> B
        B --> E[AI服务集成模拟返回数据服务]
        B --> T[第三方服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[AI平台API格式模拟]
        T -.->|仅模拟，不真实调用| G1[第三方服务API格式模拟]
    end

    subgraph "生产环境"
        A2[Python用户终端工具] --> B2[工具API接口服务]
        C2[WEB网页工具] --> B2
        D2[管理后台] --> B2
        B2 --> F2[真实第三方AI平台]
        B2 --> G2[真实第三方服务平台]
    end

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style T fill:#fff8e1
    style F1 fill:#ffebee
    style G1 fill:#ffebee
    style F2 fill:#e8f5e8
    style G2 fill:#e8f5e8
```

**依赖说明**：
- **工具API接口服务**: 依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"提供API接口支持本地开发
- **Python用户终端工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **WEB网页工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **管理后台**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发

## 🤖 AI模型配置信息

### 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 业务模型配置矩阵

#### 图像生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务
**可选平台**: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务
**可选平台**: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务
**可选平台**: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务
**可选平台**: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

## 🔐 Token认证机制规范

### AuthService认证机制
工具API接口服务使用统一的AuthService认证机制，支持两种Token传递方式：

#### 支持的认证方式
1. **Bearer Token方式** (推荐)：
   ```
   Authorization: Bearer {token}
   ```
   - 标准HTTP Bearer Token格式
   - 符合RFC 6750规范
   - 适用于所有API接口

2. **URL参数方式** (兼容性)：
   ```
   ?token={token}
   ```
   - 通过URL参数传递Token
   - 便于快速测试和调试
   - 与Bearer Token方式等效

#### 不支持的认证方式
- **无Bearer前缀的Authorization头**: `Authorization: {token}` ❌ 失败
- **无认证访问**: 直接访问受保护接口 ❌ 失败

#### AuthService.extractToken()处理逻辑
```php
// 优先级1: 从请求参数中获取token参数
$token = $request->input('token');

// 优先级2: 从Authorization头中提取Bearer Token
if (empty($token)) {
    $header = $request->header('Authorization', '');
    $position = strrpos($header, 'Bearer ');
    if ($position !== false) {
        $header = substr($header, $position + 7);
        $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
    }
}
```

#### 安全特性
- Token存储在Redis中，格式：`user:token:{user_id}`
- Token加密存储，使用ApiTokenHelper::encryptToken()
- Token有效期：30-35天随机TTL
- 支持Token失效检查和用户信息验证

## 🚨 关键架构原则

### 1. 资源下载架构铁律
**核心原则**：
1. **资源下载铁律**: 所有基于用户产生的资源文件（视频、风格、角色、音乐、音效等）都必须由"Python用户终端工具"直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等附件信息，绝不进行资源文件的中转下载
3. **架构禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

**开发约束规则**：
1. **控制器设计约束**: 资源相关控制器只能提供URL和状态管理，禁止文件操作
2. **服务层设计约束**: 资源相关服务只能进行元数据管理，禁止文件生成和处理逻辑
3. **存储架构约束**: 服务器存储只保存资源元数据，禁止保存实际资源文件
4. **下载流程约束**: Python工具 → API获取URL → 直接从AI平台下载，禁止服务器中转

### 2. WebSocket使用边界
- ✅ **仅Python工具使用**：AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**：避免不必要的连接和资源消耗
- 🔒 **安全传输**：密钥加密传输，不持久化存储

### 3. 避免循环依赖
- WebSocket服务只负责推送，不参与业务逻辑
- 积分变动通知改为异步事件驱动
- 使用事件总线模式解耦组件间依赖

### 4. 性能优化策略
- **并发支持**：设计支持1000用户同时使用
- **缓存策略**：MySQL主存储 + Redis缓存层
- **超时管理**：图像5分钟、视频30分钟、文本1分钟、语音2分钟

## 🎨 作品发布完整规则

### 可发布作品类型
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场
4. **发布时机**: 任何时间都可以提交发布申请

### 作品发布流程
1. **资源上传要求**: 发布任何作品都必须上传相关的资源文件
2. **资源重命名机制**: 上传的资源名称会被系统自动重命名
3. **资源地址保护**: 重命名后的资源地址不返回给用户，仅供系统内部使用
4. **审核机制**: 提交后进入审核流程，审核是否通过由系统决定

### 发布安全规则
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理

## 📊 API接口业务状态码定义规范

1. 业务状态码和HTTP状态码相同的会映射到HTTP状态码，业务状态码和HTTP状态码不同的HTTP状态码将被设置为200。
2. 所有的业务状态码和状态码说明必须在 `php/api/app/Enums/ApiCodeEnum.php` 中设置。

## 📋 开发文档应用规则

### 控制器层 ↔ 服务层架构规范

**工具API接口服务采用分层架构模式，明确控制器层与服务层的职责分离：**

#### 目录结构与职责分工
```
php/api/
├── app/Http/Controllers/
│   ├── PyApi/              # Python工具API控制器
│   ├── WebApi/             # WEB工具API控制器
│   └── AdminApi/           # 管理后台API控制器
├── app/Services/
│   ├── PyApi/              # Python工具业务服务
│   ├── WebApi/             # WEB工具业务服务
│   └── AdminApi/           # 管理后台业务服务
└── app/WebSocket/          # WebSocket服务层
    ├── WebSocketService.php      # WebSocket业务逻辑处理
    ├── MessageHandler.php        # 消息处理与分发
    └── ConnectionManager.php     # 连接管理与状态维护
```

#### 职责分离原则

**控制器层职责**：
- HTTP请求接收与路由处理
- 请求参数验证与格式化
- 响应数据格式化与返回
- 异常处理与错误响应
- WebSocket连接建立与消息路由

**服务层职责**：
- 具体业务逻辑实现
- 数据库操作与事务管理
- 外部API调用与集成
- 复杂算法与数据处理

**WebSocket服务层职责**：
- WebSocket消息处理与分发
- 实时通信业务逻辑
- 连接状态管理与维护
- AI生成进度推送服务

#### 调用流程

**HTTP API调用流程**：
```
HTTP请求 → 控制器层 → 服务层 → 数据库/外部服务 → 服务层 → 控制器层 → HTTP响应
```

**WebSocket通信流程**：
```
WebSocket连接 → WebSocketController → WebSocketService → 业务逻辑处理 → 消息推送 → 客户端
```

**混合调用流程** (AI生成场景)：
```
HTTP请求 → 控制器层 → 服务层 → AI服务调用 → WebSocket推送进度 → HTTP响应结果
```

## 🔄 核心业务流程

### 主要业务流程
**核心创作流程**：选风格+写剧情 → 绑角色 → 生成图像 → 视频编辑 → 本地导出
**可选扩展流程**：本地导出 → [用户选择] → 作品发布到广场

**职责分工**：
- **服务端负责**：风格管理、剧情AI生成、角色管理、图像AI生成、素材存储管理
- **客户端负责**：视频时间轴编辑、本地素材合成、UI交互、作品导出

### AI生成业务流程（成功场景）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant AI as AI平台/虚拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>W: 返回加密AI平台密钥
    W->>AI: 调用AI平台(密钥解密使用)
    AI->>W: 返回成功结果
    W->>P: 推送成功结果(实时通信)
    A->>DB: 更新日志状态(成功)
    A->>R: 更新缓存状态
    Note over W: 清理临时密钥(安全)
```

### 资源下载完成流程
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant AI as AI平台
    participant DB as MySQL数据库

    P->>A: 请求资源下载信息(resource_id)
    A->>DB: 查询资源信息和AI平台URL
    DB->>A: 返回resource_url和元数据
    A->>P: 返回AI平台URL和文件信息
    P->>AI: 直接从AI平台下载资源
    AI->>P: 下载完成
    P->>A: 确认下载完成(local_path)
    A->>DB: 更新下载状态和本地路径
    Note over P: 创作完成，资源已保存到本地
```

## 📊 数据库设计概述

### 核心数据表结构

#### 基础业务表
- **p_users**: 用户表（用户信息、认证、偏好设置）
- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）
- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）
- **p_memberships**: 会员表（会员等级、权限、使用记录）

#### AI生成相关表
- **p_ai_music**: 音乐库表（AI生成音乐存储、MiniMax平台）
- **p_ai_sound**: 音效库表（AI生成音效存储、火山引擎豆包平台）
- **p_ai_timbre**: 音色库表（AI生成音色存储、双平台支持）
- **p_ai_style**: 风格库表（剧情风格管理、AI生成配置）
- **p_ai_story**: 故事库表（AI生成故事内容、项目关联）
- **p_ai_character**: 角色库表（AI生成角色信息、特征描述）

#### 核心资源管理表
- **p_ai_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）
- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）

#### 任务管理表
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）
- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）

#### 可选作品发布表
- **p_work_plaza**: 作品广场表（可选发布、审核管理、互动统计）
- **p_user_works**: 用户作品表（作品信息、发布状态、互动数据）
- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）
- **p_work_interactions**: 作品互动表（点赞、评论、分享记录）

## ⚡ 性能期望与优化目标

- **响应延迟**：≤30000ms（30秒）
- **并发支持**：1000用户同时使用
- **系统可用性**：99.9%
- **API响应时间**：平均200ms
- **AI生成时间**：文本15-30秒，图像30-60秒
- **WebSocket连接**：支持长连接，自动重连
- **数据一致性**：MySQL+Redis双重保障
- **安全性**：密钥加密传输，权限二次验证

## 🌐 第三方AI平台API接口文档

1. **剧情生成及分镜API**：
   - DeepSeek API文档：https://api-docs.deepseek.com/zh-cn/

2. **分镜剧情生成图像API**：
   - LiblibAI的API接口文档：https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
   - 可灵API文档：https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration
   - 海螺API文档：https://platform.minimaxi.com/document/%E5%AF%B9%E8%AF%9D

3. **分镜图像生成视频API**：
   - LiblibAI的API接口文档：https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
   - 可灵API文档：https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration
   - 海螺API文档：https://platform.minimaxi.com/document/%E5%AF%B9%E8%AF%9D

## 📚 开发规范文档索引

- **AI服务集成开发规范**: `@.cursor/rules/dev-aiapi-guidelines.mdc`
- **Python工具API接口规范**: `@.cursor/rules/dev-api-guidelines-pyapi.mdc`
- **WEB工具API接口规范**: `@.cursor/rules/dev-api-guidelines-webapi.mdc`
- **管理后台API接口规范**: `@.cursor/rules/dev-api-guidelines-adminapi.mdc`

## 🔧 技术栈总结

### 后端技术栈
- **管理后台**: Laravel 10
- **工具API接口服务**: Lumen 10
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2
- **Web服务器**: Nginx 1.26.2
- **PHP版本**: 8.1.29

### 前端技术栈
- **WEB网页工具**: 响应式UI布局（支持PC和移动），全静态前端调用工具API接口服务
- **Python用户终端工具**: Python + PySide6 + PyInstaller + WebSocket客户端

### 开发工具
- **操作系统**: Windows 11（开发环境）
- **生产环境**: CentOS 8 Stream
- **版本控制**: Git
- **API文档**: 基于OpenAPI规范

---

## 📝 文档维护说明

本文档是AI视频创作工具系统的核心架构规范，所有开发工作都应严格遵循本文档的规定。如需修改架构设计，必须先更新本文档并经过团队评审。

**最后更新**: 2025-08-03
**文档版本**: v2.0
**维护人员**: 开发团队
