---
description: AI视频创作工具系统架构规范
globs: 
alwaysApply: true
---

# AI视频创作工具系统架构规范

## 📋 项目概述

### 🎯 项目定位
本项目是一个完整的AI视频创作工具生态系统，包含Python用户终端工具、WEB网页工具、管理后台、工具API接口服务和AI服务集成模拟返回数据服务五大核心组件。

### 🏗️ 系统架构设计原则
- **职责边界清晰**：每个组件职责明确，避免功能重叠
- **服务解耦**：组件间通过标准API接口通信，降低耦合度
- **资源本地化**：用户创作资源由Python工具直接从AI平台下载到本地
- **可选发布机制**：作品发布为增值服务，用户可选择是否发布到广场

## 🔧 开发环境配置

### 当前开发环境
- **操作系统**: Windows 11
- **Python**: 3.12
- **Web服务器**: Nginx 1.26.2
- **PHP**: 8.1.29
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2

### 生产环境规划
- **管理后台**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **工具API接口服务**: CentOS 8 Stream + Nginx + PHP + MySQL + Redis
- **WEB网页工具**: CentOS 8 Stream + Nginx（静态部署）
- **Python用户终端工具**: Windows 和 Mac（客户端应用）
- **AI服务集成模拟返回数据服务**: 本地开发专用，生产环境直连真实AI平台

## 📁 项目目录结构

```
项目根目录/
├── php/
│   ├── backend/          # 管理后台
│   ├── api/              # 工具API接口服务
│   ├── web/              # WEB网页工具
│   ├── aiapi/            # AI服务集成模拟返回数据服务
│   └── thirdapi/         # 第三方服务集成模拟返回数据服务
├── python/               # Python用户终端工具
└── .cursor/
    └── rules/            # 开发规范文档
        ├── index-new.mdc                    # 本文档
        ├── dev-aiapi-guidelines.mdc         # AI服务集成开发规范
        ├── dev-thirdapi-guidelines.mdc      # 第三方服务集成开发规范
        ├── dev-api-guidelines-pyapi.mdc     # Python工具API接口规范
        ├── dev-api-guidelines-webapi.mdc    # WEB工具API接口规范
        └── dev-api-guidelines-adminapi.mdc  # 管理后台API接口规范
```

## 🎯 核心组件职责定义

### 1. AI服务集成模拟返回数据服务 (@php/aiapi/)

**核心职责**：
- 在保持不同AI平台API接口特性前提下集成在一起
- 根据第三方AI的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的AI平台API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包

**作用期限**：
- 作用于支持"Python用户终端工具"本地开发阶段完成
- "Python用户终端工具"上线后通过配置修改AI平台配置直接绕过本服务
- 请求真实线上AI平台的API接口

**开发规范文档**: `@.cursor/rules/dev-aiapi-guidelines.mdc`

### 2. 第三方服务集成模拟返回数据服务 (@php/thirdapi/)

**核心职责**：
- 在保持不同第三方服务API接口特性前提下集成在一起
- 根据第三方服务的API接口文档接收和模拟返回数据
- 支持"工具API接口服务"的本地开发

**项目功能**：
- 根据"工具API接口服务"请求不同的第三方服务API接口
- 验证接收的数据和模拟各种状态返回处理结果
- 支持微信登录/支付、支付宝支付、短信服务、邮件服务

**作用期限**：
- 作用于支持"Python用户终端工具"本地开发阶段完成
- "Python用户终端工具"上线后通过配置修改第三方服务配置直接绕过本服务
- 请求真实线上第三方服务的API接口

**支持的第三方服务**：
- **微信服务**: OAuth登录、微信支付（统一下单、查询、退款等）
- **支付宝支付**: 统一收单、交易查询、退款处理
- **短信服务**: 阿里云短信、腾讯云短信、验证码验证
- **邮件服务**: SMTP发送、SendCloud、模板邮件

**服务地址**: `https://thirdapi.tiptop.cn/`

**开发规范文档**: `@.cursor/rules/dev-thirdapi-guidelines.mdc`

## 📊 **项目架构图**

### **完整系统架构（环境切换优化版）**
```mermaid
graph TB
    subgraph "用户层 - 视频创作工具"
        A[Python用户终端工具<br/>完整创作功能<br/>客户端视频编辑]
        B[WEB网页工具<br/>✅展示职责：首页工具展示、功能介绍、价格方案<br/>✅用户中心：注册登录、充值积分、积分明细、代理推广、代理结算<br/>✅作品广场：作品展示浏览、分类筛选、搜索查看、作品详情展示<br/>✅响应式设计：PC端、移动端、Python工具嵌入<br/>❌禁止：视频创作、AI生成、WebSocket通信、作品发布创建]
    end

    subgraph "业务服务层"
        C[工具API接口服务<br/>@php/api/<br/>基于Lumen 10.x<br/>统一API接口<br/>🚨环境切换机制实现层]
        C1[WebSocket服务<br/>swoole-cli artisan websocket:serve<br/>仅为Python工具提供实时通信]
        C2[AI资源管理服务<br/>资源生成+版本控制+本地导出<br/>可选：作品发布+审核系统]
    end

    subgraph "环境切换服务客户端层"
        SC1[AiServiceClient<br/>AI服务环境切换<br/>mock/real模式自动切换]
        SC2[ThirdPartyServiceClient<br/>第三方服务环境切换<br/>mock/real模式自动切换]
    end

    subgraph "开发支持层（模拟服务）"
        D[AI服务模拟<br/>@php/aiapi/<br/>本地开发模拟真实AI平台<br/>仅负责模拟，不包含环境切换]
        D2[第三方服务模拟<br/>@php/thirdapi/<br/>模拟微信、支付宝等<br/>仅负责模拟，不包含环境切换]
    end

    subgraph "数据存储层"
        E[MySQL数据库<br/>ai_tool<br/>主存储+事务保证<br/>+AI资源表+版本表+作品广场表]
        F[Redis缓存<br/>WebSocket会话管理<br/>快速查询+状态同步<br/>+资源状态缓存]
    end

    subgraph "真实AI服务（生产环境）"
        G[DeepSeek API<br/>剧情生成]
        H[LiblibAI API<br/>图像生成]
        I[KlingAI API<br/>视频生成]
        J[MiniMax API<br/>语音处理]
        K[火山引擎豆包 API<br/>专业语音AI]
    end

    subgraph "真实第三方服务（生产环境）"
        TP1[微信 API<br/>OAuth认证]
        TP2[支付宝 API<br/>支付服务]
        TP3[短信服务 API<br/>验证码发送]
    end

    %% HTTP API 连接 (蓝色虚线) - 两个工具都使用
    A -.->|🔵 HTTP API<br/>创作功能调用| C
    B -.->|🔵 HTTP API<br/>展示功能+用户中心功能+作品广场功能<br/>❌禁用WebSocket| C

    %% WebSocket 连接 (绿色粗线) - 仅Python工具使用
    A ==>|🟢 WebSocket实时通信<br/>AI生成进度推送<br/>任务状态通知| C1

    %% 服务间调用 (红色线) - 避免循环依赖
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC1
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC2
    C -->|🔴 资源管理调用<br/>版本控制+审核| C2
    C1 -->|🔴 获取API密钥<br/>安全传输| C
    C2 -->|🔴 AI生成调用<br/>资源创建| SC1

    %% 环境切换调用 (紫色线) - 核心机制
    SC1 -->|🟣 开发环境<br/>mock模式| D
    SC1 -->|🟣 生产环境<br/>real模式| G
    SC1 -->|🟣 生产环境<br/>real模式| H
    SC1 -->|🟣 生产环境<br/>real模式| I
    SC1 -->|🟣 生产环境<br/>real模式| J
    SC1 -->|🟣 生产环境<br/>real模式| K
    SC2 -->|🟣 开发环境<br/>mock模式| D2
    SC2 -->|🟣 生产环境<br/>real模式| TP1
    SC2 -->|🟣 生产环境<br/>real模式| TP2
    SC2 -->|🟣 生产环境<br/>real模式| TP3

    %% 数据库连接 (橙色线) - 双重保障
    C -->|🟠 数据存储<br/>事务保证| E
    C -->|🟠 缓存操作<br/>快速查询| F
    C1 -->|🟠 会话管理<br/>状态同步| F
    C2 -->|🟠 资源数据存储<br/>版本管理| E
    C2 -->|🟠 资源状态缓存<br/>快速查询| F

    %% 节点样式 - 高对比度清晰字体
    classDef userLayer fill:#FFFFFF,stroke:#1976D2,stroke-width:3px,color:#000000
    classDef serviceLayer fill:#FFFFFF,stroke:#7B1FA2,stroke-width:3px,color:#000000
    classDef clientLayer fill:#FFFFFF,stroke:#E91E63,stroke-width:3px,color:#000000
    classDef mockLayer fill:#FFFFFF,stroke:#4CAF50,stroke-width:3px,color:#000000
    classDef dataLayer fill:#FFFFFF,stroke:#F57C00,stroke-width:3px,color:#000000
    classDef realAiLayer fill:#FFFFFF,stroke:#388E3C,stroke-width:3px,color:#000000
    classDef realThirdLayer fill:#FFFFFF,stroke:#795548,stroke-width:3px,color:#000000

    class A,B userLayer
    class C,C1,C2 serviceLayer
    class SC1,SC2 clientLayer
    class D,D2 mockLayer
    class E,F dataLayer
    class G,H,I,J,K realAiLayer
    class TP1,TP2,TP3 realThirdLayer
```

### **架构优化说明**

**连接类型说明**:
- **🔵 蓝色虚线**: HTTP API调用 (REST接口，两个工具都使用，职责明确)
- **🟢 绿色粗线**: WebSocket实时通信 (仅Python工具使用，边界清晰)
- **🔴 红色线**: 服务间调用 (异步事件驱动，避免循环依赖)
- **🟠 橙色线**: 数据库和缓存操作 (双重保障，性能优化)
- **🟣 紫色线**: 环境切换调用 (核心机制，自动路由)

**关键优化特性**:
1. **职责边界清晰**: 每个组件的职责明确定义，避免功能重叠
2. **环境切换机制**: 通过服务客户端实现开发/生产环境无缝切换
3. **避免循环依赖**: 使用异步事件驱动架构，解耦组件间依赖
4. **WebSocket边界明确**: 仅为Python工具提供实时通信，WEB工具不使用
5. **性能优化设计**: 支持1000并发用户，MySQL+Redis双重保障
6. **安全架构升级**: 密钥安全传输，不持久化存储，权限二次验证
7. **功能模块整合**: 相关功能统一管理，避免分散和重复
8. **🎯 AI资源管理**: 完整的资源生成、版本控制、审核发布体系
9. **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据管理

## 🔄 **完整业务流程图（环境切换优化版）**

### **核心业务流程**

#### 业务流程1: 处理成功的业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>DB: 写入业务日志(状态:冻结)
    A->>R: 缓存业务日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)
    AI->>SC: 返回结果(包含mode标识)
    SC->>A: 返回结果+环境模式信息
    A->>W: 返回成功结果
    W->>P: 推送成功结果(实时通信)
    W->>E: 发布任务完成事件(异步)
    E->>A: 处理任务完成事件
    A->>DB: 更新日志状态(成功)
    A->>R: 更新缓存状态
    Note over SC: 环境切换完成，模式透明
```

#### 业务流程2: 积分不足业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具API接口服务

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>A: 检查用户积分(快速验证)
    Note over A: 积分 < 所需积分
    A->>W: 返回积分不足详细信息
    W->>P: 推送积分不足消息(包含充值建议)
    Note over A: 无扣费操作，保护用户资金
```

#### 业务流程3: 处理失败的业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)
    AI->>SC: 返回失败结果
    SC->>A: 返回失败结果+环境模式信息
    A->>W: 返回失败结果
    W->>P: 推送失败结果(详细错误信息)
    W->>E: 发布任务失败事件(异步)
    E->>A: 处理任务失败事件
    A->>DB: 更新日志状态(失败) + 返还等额积分(事务保证)
    A->>R: 更新缓存状态
    Note over SC: 环境切换完成，积分安全返还
```

#### 业务流程4: 超时/中断处理业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant T as 超时监控
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    W->>T: 启动超时监控(业务类型自适应)
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)

    alt 超时或连接中断
        T->>E: 检测到超时/中断(发布事件)
        E->>A: 处理中断事件
        A->>DB: 更新日志状态(失败) + 返还等额积分
        A->>R: 更新缓存状态
        E->>W: 通知WebSocket服务
        W->>P: 推送中断消息(包含积分返还确认)
        Note over SC: 环境切换机制保证服务稳定性
    end
```

### **业务流程优化说明**

**环境切换机制优化重点**:
- **🔄 自动环境切换**: AiServiceClient根据配置自动路由到模拟/真实服务
- **🔒 安全性增强**: 密钥加密传输，使用后立即清理，不持久化存储
- **⚡ 性能优化**: 事务锁定机制，缓存同步策略，快速积分验证
- **🔄 异步解耦**: 使用事件总线避免循环依赖，提高系统响应性
- **💰 资金安全**: 积分冻结机制，失败自动返还，事务一致性保证
- **📊 监控完善**: 超时检测自适应，连接中断处理，状态追踪完整
- **🎯 资源管理**: AI资源生成、版本控制、审核发布一体化管理
- **🎯 差异化存储**: 图像/音频下载处理，视频仅元数据，优化存储成本

**积分安全机制升级**:
- ✅ **事务锁定**: 使用数据库事务锁定，确保并发安全
- ✅ **状态流转**: frozen → success/failed，状态机模式
- ✅ **自动返还**: 失败或中断时自动返还，用户资金安全
- ✅ **一致性保证**: MySQL+Redis双重保障，数据一致性

**超时检测机制优化**:
- 🕐 **图像生成**: 5分钟超时（可配置）
- 🕐 **视频生成**: 30分钟超时（可配置）
- 🕐 **文本生成**: 1分钟超时（可配置）
- 🕐 **语音合成**: 2分钟超时（可配置）

### **扩展业务流程**

#### 🎯 业务流程5: AI资源生成与版本管理流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant RM as AI资源管理服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 发起AI资源生成请求(含module_id)
    A->>RM: 创建资源记录
    RM->>DB: 创建p_ai_resources记录
    RM->>DB: 自动创建v1.0版本记录
    RM->>A: 返回资源UUID
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务生成资源
    AI->>SC: 返回资源URL和元数据
    SC->>A: 返回结果+环境模式信息
    A->>RM: 更新版本信息
    RM->>DB: 更新resource_url、file_size等
    RM->>RM: 执行自动内容审核
    RM->>DB: 更新review_status
    RM->>R: 缓存资源状态
    A->>P: 返回资源信息
    P->>AI: 直接下载资源到本地
    P->>A: 确认下载完成
    A->>RM: 更新下载状态
    RM->>DB: 更新downloaded_by_python=true
```

#### 🎯 业务流程6: 资源下载完成流程（核心流程）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库

    P->>A: 请求资源下载信息(resource_id)
    A->>DB: 查询资源信息和AI平台URL
    DB->>A: 返回resource_url和元数据
    A->>P: 返回AI平台URL和文件信息
    Note over SC: 🚨环境切换：资源URL根据环境<br/>指向模拟服务或真实AI平台
    P->>AI: 直接从AI平台下载资源
    AI->>P: 下载完成
    P->>A: 确认下载完成(local_path)
    A->>DB: 更新下载状态和本地路径
    Note over P: 创作完成，资源已保存到本地
```

#### 🎯 业务流程7: 可选作品发布流程（增值服务）
```mermaid
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant WPS as 作品发布权限服务
    participant WP as 作品广场
    participant DB as MySQL数据库

    Note over P: 用户已完成本地导出
    P->>A: [可选] 请求发布作品(module_type, module_id)
    A->>WPS: 检查发布权限
    WPS->>DB: 查询模块相关资源的review_status

    alt 用户选择发布
        DB->>WPS: review_status = 'approved'/'auto_approved'
        WPS->>A: 返回允许发布
        A->>WP: 创建作品广场记录
        WP->>DB: 保存到p_work_plaza表
        WP->>A: 返回发布成功
        A->>P: 通知发布成功
    else 用户选择不发布
        P->>A: 跳过发布，仅本地保存
        A->>P: 确认完成，无需发布
    end
```

#### 🎯 业务流程8: 环境切换机制流程（核心机制）
```mermaid
sequenceDiagram
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant Config as 配置系统
    participant Mock as 模拟服务
    participant Real as 真实AI服务

    A->>SC: 请求AI服务调用
    SC->>Config: 读取AI_SERVICE_MODE配置

    alt 开发环境 (mock模式)
        Config->>SC: 返回mode=mock
        SC->>Mock: 调用模拟服务
        Mock->>SC: 返回模拟结果+mode=mock
        SC->>A: 返回结果{success:true, mode:'mock', data:...}
        Note over SC: 开发环境：无真实费用，快速响应
    else 生产环境 (real模式)
        Config->>SC: 返回mode=real
        SC->>Real: 调用真实AI服务
        Real->>SC: 返回真实结果
        SC->>A: 返回结果{success:true, mode:'real', data:...}
        Note over SC: 生产环境：真实调用，产生费用
    end

    Note over SC: 环境切换对业务层透明<br/>统一的调用接口和响应格式
```

#### AI服务集成模拟机制架构图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Python用户终端工具] --> B[工具API接口服务]
        B --> E[AI服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[DeepSeek API格式模拟<br/>剧情生成/角色生成]
        E -.->|仅模拟，不真实调用| F2[LiblibAI API格式模拟<br/>图像生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F3[KlingAI API格式模拟<br/>图像生成/视频生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F4[MiniMax API格式模拟<br/>全业务支持]
        E -.->|仅模拟，不真实调用| F5[火山引擎豆包 API格式模拟<br/>语音合成/音效生成/音色生成]

        B --> T[第三方服务集成模拟返回数据服务]
        T -.->|仅模拟，不真实调用| G1[微信服务API格式模拟<br/>OAuth登录/微信支付]
        T -.->|仅模拟，不真实调用| G2[支付宝API格式模拟<br/>统一收单/退款查询]
        T -.->|仅模拟，不真实调用| G3[短信服务API格式模拟<br/>阿里云/腾讯云短信]
        T -.->|仅模拟，不真实调用| G4[邮件服务API格式模拟<br/>SMTP/SendCloud]
    end

    subgraph "生产环境"
        A2[Python用户终端工具] --> B2[工具API接口服务]
        B2 --> F6[真实第三方AI平台<br/>DeepSeek/LiblibAI/KlingAI<br/>MiniMax/火山引擎豆包]
        B2 --> G5[真实第三方服务平台<br/>微信/支付宝/阿里云/腾讯云]
    end

    style E fill:#fce4ec,stroke:#e91e63
    style T fill:#fff8e1,stroke:#ff9800
    style F1 fill:#ffebee
    style F2 fill:#ffebee
    style F3 fill:#ffebee
    style F4 fill:#ffebee
    style F5 fill:#ffebee
    style G1 fill:#ffebee
    style G2 fill:#ffebee
    style G3 fill:#ffebee
    style G4 fill:#ffebee
    style F6 fill:#e8f5e8,stroke:#4caf50
    style G5 fill:#e8f5e8,stroke:#4caf50
```

#### AI服务调用流程对比图

```mermaid
sequenceDiagram
    participant P as Python工具
    participant API as 工具API接口服务
    participant Mock as AI模拟服务
    participant Real as 真实AI平台

    Note over P,Mock: 本地开发阶段
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Mock: 真实调用AI平台格式接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Note over Mock: ✅ 唯一模拟职责<br/>1. 按对应AI平台要求验证参数<br/>2. 模拟对应平台响应状态
    alt 参数验证失败
        Mock->>API: 返回对应AI平台格式参数错误
    else 参数验证通过
        Mock->>API: 模拟成功/失败/超时状态
    end
    API->>P: 透明传递模拟结果

    Note over P,Real: 生产环境
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Real: 真实调用对应AI平台<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Real->>API: 返回真实结果
    API->>P: 透明传递真实结果
```

#### 核心机制说明

**AI服务集成模拟返回数据服务** 的核心作用机制：

1. **接收标准请求**：
   - 接收来自"工具API接口服务"的请求
   - 请求格式完全按照真实第三方AI平台的API文档要求
   - 包含相同的参数结构、认证方式、数据格式

2. **数据验证与模拟响应**：
   - 按照真实AI平台（DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包）的要求对提交数据进行严格验证
   - **验证不通过**：模拟真实AI平台的参数错误结果返回
   - **验证通过**：模拟成功、失败、超时等其中一种状态返回
   - 返回符合真实API规范的数据结构

3. **支持本地开发**：
   - 让开发者在本地环境就能完整测试所有AI功能
   - 无需真实调用第三方AI平台（避免费用、网络依赖）
   - 可以模拟各种异常情况进行充分测试

4. **环境切换机制**：
   - 本地开发：工具API → 模拟服务 → 模拟响应（**不发生真实第三方调用**）
   - 生产环境：工具API → 真实第三方平台 → 真实响应
   - 通过配置文件轻松切换，无需修改业务代码

#### 🚨 重要说明：模拟服务边界

**模拟服务的工作原理**：
1. **接收请求**：模拟服务接收来自工具API的请求
2. **参数验证**：按照真实第三方平台的要求验证参数
3. **内部模拟**：在模拟服务内部生成符合真实API格式的响应
4. **返回结果**：将模拟结果返回给工具API

**❌ 模拟服务不会做的事情**：
- 不会向真实的第三方平台发起任何网络请求
- 不会产生任何真实的费用
- 不会获取真实的用户数据
- 不会执行真实的业务操作

**✅ 模拟服务会做的事情**：
- 验证请求参数的格式和完整性
- 模拟各种响应状态（成功、失败、超时等）
- 返回符合真实API格式的模拟数据
- 记录详细的调用日志

#### 🚨 关键架构边界规范

**模拟行为边界铁律**：

1. **AI服务集成模拟返回数据服务**：
   - ✅ **唯一模拟职责**：仅在接收数据验证后进行模拟行为
   - ✅ **模拟范围**：数据验证、状态返回、响应格式
   - ✅ **模拟时机**：仅在数据验证完成后执行模拟逻辑

2. **工具API接口服务**：
   - ❌ **严禁模拟行为**：不允许在程序代码中进行任何模拟行为
   - ✅ **真实调用职责**：必须真实调用AI服务（模拟服务或真实服务）
   - ✅ **透明传递**：请求和响应数据必须透明传递，不得修改

3. **架构违规检查**：
   - ❌ 工具API接口服务中出现模拟逻辑代码
   - ❌ 工具API接口服务中硬编码返回模拟数据
   - ❌ 工具API接口服务中包含假数据生成逻辑
   - ❌ 绕过AI服务调用直接返回结果

**关键价值**：
- ✅ **开发效率**：本地开发无需依赖真实第三方平台
- ✅ **成本控制**：避免开发阶段产生任何真实费用
- ✅ **测试完整性**：可以模拟各种边界情况和异常状态
- ✅ **完全兼容**：确保与真实第三方平台API的100%兼容性
- ✅ **架构纯净**：工具API接口服务保持业务逻辑纯净，无模拟污染
- ✅ **安全隔离**：模拟环境与真实环境完全隔离，无数据泄露风险

### 3. 工具API接口服务 (@php/api/)

**核心职责**：
- 本地开发阶段依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"
- 集成多家AI平台的API接口提供对应AI平台的API接口服务
- 集成多种第三方服务的API接口（微信、支付宝、短信、邮件等）
- 开发支持"Python用户终端工具"的AI视频创作功能
- 支持"WEB网页工具"作品广场和"管理后台"功能实现

#### 3.1 Python用户终端工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/PyApi`
**业务层目录**: `@php/api/app/Services/PyApi`

**业务逻辑职责**：
- 创建AI创作视频任务
- AI任务调度（含文生文、图生图、图生视频、生成语音、生成音效、生成音乐等所有需要AI的功能）
- 数据处理
- 作品发布
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**WebSocket服务职责**：
- 仅为Python工具提供实时通信（AI生成进度推送）

**不包含职责**：
- 不储存且不中转用户创作过程中AI生成的资源
- 视频编辑处理
- 客户端UI逻辑
- 本地文件操作

#### 3.2 WEB网页工具的API接口

**控制器目录**: `@php/api/app/Http/Controllers/WebApi`
**业务层目录**: `@php/api/app/Services/WebApi`

**业务逻辑职责**：
- 功能介绍查询
- 价格方案查询
- 作品数据查询（支持分类筛选、搜索查看、作品详情展示）
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**响应式设计**：
- 支持PC端、移动端、Python工具嵌入（1200px/800px窗口）

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建

#### 3.3 支持"管理后台"的API接口

**控制器目录**: `@php/api/app/Http/Controllers/AdminApi`
**业务层目录**: `@php/api/app/Services/AdminApi`

**业务逻辑职责**：
- 系统配置管理（AI平台配置、系统参数设置）
- 用户管理（用户信息、权限管理、账户状态）
- 内容管理（作品审核、内容监控、违规处理）
- 数据统计（用户统计、收入统计、使用情况分析）
- 积分系统管理（积分规则、充值记录、消费明细）
- 代理系统管理（代理审核、佣金结算、推广数据）
- 素材库管理（音色库、音效库、音乐库、风格库、角色库）
- 系统监控（性能监控、错误日志、API调用统计）
- 财务管理（收入报表、退款处理、财务对账）

### 4. Python用户终端工具 (@python/)

**技术栈**: Python + PySide6 + PyInstaller + WebSocket客户端

**核心创作职责**（调用"Python用户终端工具的API接口"支持）：
- 选风格+写剧情
- 绑角色
- 生成图像
- 视频编辑
- 本地导出

**可选发布职责**（调用"Python用户终端工具的API接口"支持）：
- 作品发布到广场（用户自主选择）

**客户端处理职责**（调用"Python用户终端工具的API接口"支持）：
- 资源本地化
- 视频时间轴编辑
- 本地素材合成
- UI交互逻辑
- 作品导出

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**实时通信职责**（调用"Python用户终端工具的API接口"支持）：
- 通过WebSocket接收AI生成进度推送

### 5. WEB网页工具 (@php/web/)

**展示职责**（调用"WEB网页工具的API接口"支持）：
- 首页工具展示
- 功能介绍
- 价格方案
- 作品展示

**用户中心职责**（调用"用户API接口"支持）：
- 用户注册登录
- 用户资料修改与密码找回
- 用户认证
- 充值积分
- 积分管理
- 积分明细
- 代理推广
- 代理结算

**作品广场职责**：
- 作品展示浏览
- 分类筛选
- 搜索查看
- 作品详情展示

**响应式设计**：
- 支持PC端、移动端

**不包含职责**：
- 视频创作功能
- AI生成功能
- WebSocket实时通信
- 作品发布创建

### 6. 管理后台 (@php/backend/)

**基于Laravel 10开发的管理后台**

**数据管理职责**：
- AI引擎配置
- 音色库、音效库、音乐库管理
- 风格库、角色库管理
- 作品库、会员库管理
- 积分明细管理

**配置管理职责**：
- 第三方AI的API接口地址和密钥管理
- 系统参数配置
- 业务规则配置

**系统管理职责**：
- 用户权限管理
- 系统监控
- 数据统计分析
- 内容审核管理

## 🔄 项目依赖关系

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Python用户终端工具] --> B[工具API接口服务]
        C[WEB网页工具] --> B
        D[管理后台] --> B
        B --> E[AI服务集成模拟返回数据服务]
        B --> T[第三方服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[AI平台API格式模拟]
        T -.->|仅模拟，不真实调用| G1[第三方服务API格式模拟]
    end

    subgraph "生产环境"
        A2[Python用户终端工具] --> B2[工具API接口服务]
        C2[WEB网页工具] --> B2
        D2[管理后台] --> B2
        B2 --> F2[真实第三方AI平台]
        B2 --> G2[真实第三方服务平台]
    end

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style T fill:#fff8e1
    style F1 fill:#ffebee
    style G1 fill:#ffebee
    style F2 fill:#e8f5e8
    style G2 fill:#e8f5e8
```

**依赖说明**：
- **工具API接口服务**: 依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"提供API接口支持本地开发
- **Python用户终端工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **WEB网页工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **管理后台**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发

## 🤖 AI模型配置信息

### 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 业务模型配置矩阵

#### 图像生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务
**可选平台**: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务
**可选平台**: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务
**可选平台**: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务
**可选平台**: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务
**可选平台**: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

#### 语音处理业务
**可选平台**: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业语音合成、音效生成、音色生成
- **MiniMax**: 多模态语音处理、语音理解

## 🔐 Token认证机制规范

### AuthService认证机制
工具API接口服务使用统一的AuthService认证机制，支持两种Token传递方式：

#### 支持的认证方式
1. **Bearer Token方式** (推荐)：
   ```
   Authorization: Bearer {token}
   ```
   - 标准HTTP Bearer Token格式
   - 符合RFC 6750规范
   - 适用于所有API接口

2. **URL参数方式** (兼容性)：
   ```
   ?token={token}
   ```
   - 通过URL参数传递Token
   - 便于快速测试和调试
   - 与Bearer Token方式等效

#### 不支持的认证方式
- **无Bearer前缀的Authorization头**: `Authorization: {token}` ❌ 失败
- **无认证访问**: 直接访问受保护接口 ❌ 失败

#### AuthService.extractToken()处理逻辑
```php
// 优先级1: 从请求参数中获取token参数
$token = $request->input('token');

// 优先级2: 从Authorization头中提取Bearer Token
if (empty($token)) {
    $header = $request->header('Authorization', '');
    $position = strrpos($header, 'Bearer ');
    if ($position !== false) {
        $header = substr($header, $position + 7);
        $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
    }
}
```

#### 安全特性
- Token存储在Redis中，格式：`user:token:{user_id}`
- Token加密存储，使用ApiTokenHelper::encryptToken()
- Token有效期：30-35天随机TTL
- 支持Token失效检查和用户信息验证

## 🚨 关键架构原则

### 1. 资源下载架构铁律
**核心原则**：
1. **资源下载铁律**: 所有基于用户产生的资源文件（视频、风格、角色、音乐、音效等）都必须由"Python用户终端工具"直接从AI平台下载到本地
2. **服务器职责边界**: API服务器只负责管理资源的URL、状态、元数据等附件信息，绝不进行资源文件的中转下载
3. **架构禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

**开发约束规则**：
1. **控制器设计约束**: 资源相关控制器只能提供URL和状态管理，禁止文件操作
2. **服务层设计约束**: 资源相关服务只能进行元数据管理，禁止文件生成和处理逻辑
3. **存储架构约束**: 服务器存储只保存资源元数据，禁止保存实际资源文件
4. **下载流程约束**: Python工具 → API获取URL → 直接从AI平台下载，禁止服务器中转

### 2. WebSocket使用边界
- ✅ **仅Python工具使用**：AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**：避免不必要的连接和资源消耗
- 🔒 **安全传输**：密钥加密传输，不持久化存储

### 3. 避免循环依赖
- WebSocket服务只负责推送，不参与业务逻辑
- 积分变动通知改为异步事件驱动
- 使用事件总线模式解耦组件间依赖

### 4. 性能优化策略
- **并发支持**：设计支持1000用户同时使用
- **缓存策略**：MySQL主存储 + Redis缓存层
- **超时管理**：图像5分钟、视频30分钟、文本1分钟、语音2分钟

## 🎨 作品发布完整规则

### 可发布作品类型
1. **风格作品**: 用户创建的剧情风格可发布到风格广场
2. **角色作品**: 用户创建的角色可发布到角色广场
3. **视频作品**: 用户创作完成的视频可发布到作品广场
4. **发布时机**: 任何时间都可以提交发布申请

### 作品发布流程
1. **资源上传要求**: 发布任何作品都必须上传相关的资源文件
2. **资源重命名机制**: 上传的资源名称会被系统自动重命名
3. **资源地址保护**: 重命名后的资源地址不返回给用户，仅供系统内部使用
4. **审核机制**: 提交后进入审核流程，审核是否通过由系统决定

### 发布安全规则
1. **资源隔离**: 发布资源与用户创作资源完全隔离
2. **地址保护**: 发布资源地址不暴露给用户
3. **权限控制**: 仅审核通过的作品可在广场展示
4. **版权保护**: 发布资源受系统版权保护机制管理

## 📊 API接口业务状态码定义规范

1. 业务状态码和HTTP状态码相同的会映射到HTTP状态码，业务状态码和HTTP状态码不同的HTTP状态码将被设置为200。
2. 所有的业务状态码和状态码说明必须在 `php/api/app/Enums/ApiCodeEnum.php` 中设置。

## 📋 开发文档应用规则

### 控制器层 ↔ 服务层架构规范

**工具API接口服务采用分层架构模式，明确控制器层与服务层的职责分离：**

#### 目录结构与职责分工
```
php/api/
├── app/Http/Controllers/
│   ├── PyApi/              # Python工具API控制器
│   ├── WebApi/             # WEB工具API控制器
│   └── AdminApi/           # 管理后台API控制器
├── app/Services/
│   ├── PyApi/              # Python工具业务服务
│   ├── WebApi/             # WEB工具业务服务
│   └── AdminApi/           # 管理后台业务服务
└── app/WebSocket/          # WebSocket服务层
    ├── WebSocketService.php      # WebSocket业务逻辑处理
    ├── MessageHandler.php        # 消息处理与分发
    └── ConnectionManager.php     # 连接管理与状态维护
```

#### 职责分离原则

**控制器层职责**：
- HTTP请求接收与路由处理
- 请求参数验证与格式化
- 响应数据格式化与返回
- 异常处理与错误响应
- WebSocket连接建立与消息路由

**服务层职责**：
- 具体业务逻辑实现
- 数据库操作与事务管理
- 外部API调用与集成
- 复杂算法与数据处理

**WebSocket服务层职责**：
- WebSocket消息处理与分发
- 实时通信业务逻辑
- 连接状态管理与维护
- AI生成进度推送服务

#### 调用流程

**HTTP API调用流程**：
```
HTTP请求 → 控制器层 → 服务层 → 数据库/外部服务 → 服务层 → 控制器层 → HTTP响应
```

**WebSocket通信流程**：
```
WebSocket连接 → WebSocketController → WebSocketService → 业务逻辑处理 → 消息推送 → 客户端
```

**混合调用流程** (AI生成场景)：
```
HTTP请求 → 控制器层 → 服务层 → AI服务调用 → WebSocket推送进度 → HTTP响应结果
```

## 🔄 核心业务流程

### 主要业务流程
**核心创作流程**：选风格+写剧情 → 绑角色 → 生成图像 → 视频编辑 → 本地导出
**可选扩展流程**：本地导出 → [用户选择] → 作品发布到广场

**职责分工**：
- **服务端负责**：风格管理、剧情AI生成、角色管理、图像AI生成、素材存储管理
- **客户端负责**：视频时间轴编辑、本地素材合成、UI交互、作品导出

### 详细业务流程参考
完整的业务流程图请参考 **"🔄 完整业务流程图（环境切换优化版）"** 章节，包含：
- **业务流程1**: 处理成功的业务流程（环境切换优化版）
- **业务流程2**: 积分不足业务流程（优化版）
- **业务流程3**: 处理失败的业务流程（环境切换优化版）
- **业务流程4**: 超时/中断处理业务流程（环境切换优化版）
- **业务流程5**: AI资源生成与版本管理流程（环境切换优化版）
- **业务流程6**: 资源下载完成流程（核心流程）
- **业务流程7**: 可选作品发布流程（增值服务）
- **业务流程8**: 环境切换机制流程（核心机制）

该章节包含了完整的Mermaid序列图，详细描述了环境切换机制、积分安全、错误处理等所有关键业务场景。

## 📊 数据库设计概述

### 核心数据表结构

#### 基础业务表
- **p_users**: 用户表（用户信息、认证、偏好设置）
- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）
- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）
- **p_memberships**: 会员表（会员等级、权限、使用记录）

#### AI生成相关表
- **p_ai_music**: 音乐库表（AI生成音乐存储、MiniMax平台）
- **p_ai_sound**: 音效库表（AI生成音效存储、火山引擎豆包平台）
- **p_ai_timbre**: 音色库表（AI生成音色存储、双平台支持）
- **p_ai_style**: 风格库表（剧情风格管理、AI生成配置）
- **p_ai_story**: 故事库表（AI生成故事内容、项目关联）
- **p_ai_character**: 角色库表（AI生成角色信息、特征描述）

#### 核心资源管理表
- **p_ai_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）
- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）

#### 任务管理表
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）
- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）

#### 可选作品发布表
- **p_work_plaza**: 作品广场表（可选发布、审核管理、互动统计）
- **p_user_works**: 用户作品表（作品信息、发布状态、互动数据）
- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）
- **p_work_interactions**: 作品互动表（点赞、评论、分享记录）

## ⚡ 性能期望与技术要求

### **系统性能指标**
- **响应延迟**：≤30000ms（30秒）
- **并发支持**：1000用户同时使用
- **系统可用性**：99.9%
- **API响应时间**：平均200ms
- **AI生成时间**：文本15-30秒，图像30-60秒
- **WebSocket连接**：支持长连接，自动重连
- **数据一致性**：MySQL+Redis双重保障
- **安全性**：密钥加密传输，权限二次验证

### **AI生成超时配置**
- 🕐 **图像生成**: 5分钟超时（可配置）
- 🕐 **视频生成**: 30分钟超时（可配置）
- 🕐 **文本生成**: 1分钟超时（可配置）
- 🕐 **语音合成**: 2分钟超时（可配置）

### **环境切换性能对比**
| 环境模式 | 响应时间 | 费用产生 | 数据真实性 | 适用场景 |
|---------|---------|---------|-----------|---------|
| **mock模式** | 100-500ms | 无费用 | 模拟数据 | 开发测试 |
| **real模式** | 1-30秒 | 真实费用 | 真实数据 | 生产环境 |

## 🌐 第三方AI平台API接口文档

1. **剧情生成及分镜API**：
   - DeepSeek API文档：https://api-docs.deepseek.com/zh-cn/

2. **分镜剧情生成图像API**：
   - LiblibAI的API接口文档：https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
   - 可灵API文档：https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration
   - 海螺API文档：https://platform.minimaxi.com/document/%E5%AF%B9%E8%AF%9D

3. **分镜图像生成视频API**：
   - LiblibAI的API接口文档：https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
   - 可灵API文档：https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration
   - 海螺API文档：https://platform.minimaxi.com/document/%E5%AF%B9%E8%AF%9D


## 📚 开发文档使用指南

### 🎯 **分类文档应用场景明确定义**

**针对 @php/api/ 目录的"工具API接口服务"开发，必须严格按照以下规则使用分类文档：**

#### **📚 六大核心文档体系**

1. **🆕 新功能开发文档**：`@.cursor/rules/dev-api-guidelines-add.mdc`
2. **🔧 问题修复文档**：`@.cursor/rules/dev-api-guidelines-edit.mdc`
3. **🤖 【工具API接口服务】环境切换AI服务文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`
4. **🤖 【Python用户终端工具】对接【工具API接口服务】文档**：`@.cursor/rules/dev-api-guidelines-pyapi.mdc`
5. **🤖 【WEB网页工具】对接【工具API接口服务】文档**：`@.cursor/rules/dev-api-guidelines-webapi.mdc`
6. **🤖 【管理后台】对接【工具API接口服务】文档**：`@.cursor/rules/dev-api-guidelines-adminapi.mdc`

#### **🆕 新功能开发场景**
**主要文档**：`@.cursor/rules/dev-api-guidelines-add.mdc`
**辅助文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`（当涉及AI功能时）

**适用情况**：
- ✅ **新增API接口开发**：创建全新的控制器、服务层、中间件
- ✅ **新增数据库表设计**：设计新的数据表结构和迁移程序
- ✅ **新增业务模块开发**：实现全新的业务功能模块
- ✅ **新增WebSocket处理器**：开发Python工具专用的WebSocket服务
- ✅ **新增AI服务环境切换功能**：集成新的AI生成服务和功能（必须配合dev-aiapi-guidelines.mdc）
- ✅ **扩展性架构设计**：设计可扩展的系统架构
- ✅ **高级功能扩展**：用户成长路径、个性化推荐、AI模型管理等

#### **🔧 问题修复场景**
**主要文档**：`@.cursor/rules/dev-api-guidelines-edit.mdc`
**辅助文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`（当修复AI相关问题时）

**适用情况**：
- ✅ **架构违规修复**：修复WebSocket边界违规、职责边界不清等问题
- ✅ **性能指标修正**：统一并发用户数为1000、响应时间优化等
- ✅ **安全机制强化**：积分系统安全加固、认证机制完善
- ✅ **接口规范修正**：移除违规接口、修正API设计
- ✅ **数据库结构修复**：修正表字段、索引优化、关系调整
- ✅ **代码质量提升**：重构现有代码、优化业务逻辑
- ✅ **AI服务调用问题修复**：修复AI API调用失败、超时、格式错误等问题
- ✅ **环境切换机制修复**：修复 mock/real 模式切换问题

#### **🤖 【工具API接口服务】环境切换AI服务场景**
**主要文档**：`@.cursor/rules/dev-aiapi-guidelines.mdc`
**配合文档**：根据具体需求选择 `@.cursor/rules/dev-api-guidelines-add.mdc` 或 `@.cursor/rules/dev-api-guidelines-edit.mdc` 文档

**适用情况**：
- ✅ **AI API接口调用**：通过 AiServiceClient::call() 调用DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包等AI服务
- ✅ **AI服务环境切换开发**：实现 mock/real 模式的无缝切换，确保开发和生产环境的一致性
- ✅ **AI服务错误处理**：处理AI API调用失败、超时、格式错误等问题
- ✅ **AI服务性能优化**：优化AI API调用性能、实现负载均衡、服务降级
- ✅ **AI服务监控**：监控AI API调用状态、成功率、响应时间等指标
- ✅ **环境切换配置**：配置 AI_SERVICE_MODE 环境变量和相关参数

**包含内容**：
- 🤖 **5个AI平台完整接口规范**：详见 **"🤖 AI模型配置信息"** 章节
- 🤖 **87个AI API接口**：文本生成、图像生成、语音合成、视频生成、音效处理、音频混合等
- 🤖 **环境切换机制**：AiServiceClient 和 ThirdPartyServiceClient 的使用规范
- 🤖 **配置管理**：php/api/config/ai.php 的配置说明和最佳实践
- 🤖 **分平台功能支持详情**：详见 **"🤖 AI模型配置信息"** 章节的业务模型配置矩阵

#### **🎯 第三方服务环境切换场景**
**主要文档**：`@.cursor/rules/dev-thirdapi-guidelines.mdc`
**配合文档**：根据具体需求选择 `@.cursor/rules/dev-api-guidelines-add.mdc` 或 `@.cursor/rules/dev-api-guidelines-edit.mdc` 文档

**适用情况**：
- ✅ **第三方API接口调用**：通过 ThirdPartyServiceClient::call() 调用微信、支付宝、短信等第三方服务
- ✅ **第三方服务环境切换**：实现 mock/real 模式的无缝切换
- ✅ **第三方服务错误处理**：处理第三方API调用失败、超时等问题
- ✅ **第三方服务监控**：监控第三方API调用状态和性能指标

#### **📊 文档使用优先级规则**

1. **🔍 问题诊断优先级**：
   - **AI服务环境切换相关问题** → 优先使用 `dev-aiapi-guidelines.mdc` + 对应的add/edit文档
   - **第三方服务环境切换相关问题** → 优先使用 `dev-thirdapi-guidelines.mdc` + 对应的add/edit文档
   - **现有功能问题修复** → 使用 `dev-api-guidelines-edit.mdc`
   - **新功能需求开发** → 使用 `dev-api-guidelines-add.mdc`

2. **🎯 开发适配规则**：
   - **基础设施开发**：主要使用 `dev-api-guidelines-add.mdc`
   - **核心AI功能开发**：`dev-api-guidelines-add.mdc` + `dev-aiapi-guidelines.mdc`
   - **第三方服务集成**：`dev-api-guidelines-add.mdc` + `dev-thirdapi-guidelines.mdc`
   - **高级功能开发**：三个文档并用，根据具体需求选择
   - **维护阶段**：主要使用 `dev-api-guidelines-edit.mdc` + 对应的专项文档

3. **🚨 紧急修复优先级**：
   - **AI服务故障** → 必须使用 `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **第三方服务故障** → 必须使用 `dev-thirdapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **环境切换问题** → 优先使用对应的专项文档 + `dev-api-guidelines-edit.mdc`
   - **生产环境问题** → 优先使用 `dev-api-guidelines-edit.mdc`
   - **架构合规性问题** → 必须使用 `dev-api-guidelines-edit.mdc`

4. **🤖 AI功能开发专用规则**：
   - **所有AI相关开发** → 必须使用 `dev-aiapi-guidelines.mdc` 作为主要参考
   - **新增AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-add.mdc`
   - **修复AI功能** → `dev-aiapi-guidelines.mdc` + `dev-api-guidelines-edit.mdc`
   - **AI服务环境切换** → 仅使用 `dev-aiapi-guidelines.mdc` + 本文档环境切换规范

#### **🔄 多文档协作机制**

1. **📋 文档协作优先级**：
   ```
   AI功能开发：dev-aiapi-guidelines.mdc (主) + dev-api-guidelines-add.mdc (辅)
   AI问题修复：dev-aiapi-guidelines.mdc (主) + dev-api-guidelines-edit.mdc (辅)
   第三方服务开发：dev-thirdapi-guidelines.mdc (主) + dev-api-guidelines-add.mdc (辅)
   第三方服务修复：dev-thirdapi-guidelines.mdc (主) + dev-api-guidelines-edit.mdc (辅)
   非AI新功能：dev-api-guidelines-add.mdc (主) + 其他文档 (可选)
   非AI问题修复：dev-api-guidelines-edit.mdc (主)
   ```

2. **🎯 具体使用场景判断**：
   - **包含"AI"、"生成"、"智能"关键词** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **涉及微信、支付宝、短信等第三方服务** → 必须使用 `dev-thirdapi-guidelines.mdc`
   - **文本生成、图像生成、语音合成、视频生成** → 必须使用 `dev-aiapi-guidelines.mdc`
   - **环境切换、AiServiceClient、ThirdPartyServiceClient** → 使用对应的专项文档

#### **⚠️ 重要注意事项**

1. **🛡️ 架构合规性检查**：
   - 所有开发必须遵循index-new.mdc中定义的职责边界
   - WebSocket仅限Python工具使用
   - 并发用户数统一为1000
   - AI服务调用必须使用 AiServiceClient::call()
   - 第三方服务调用必须使用 ThirdPartyServiceClient::call()
   - 环境切换必须通过配置文件实现，不得硬编码

2. **📋 文档完整性保证**：
   - **环境切换机制**：完整实现 mock/real 模式自动切换
   - **dev-api-guidelines-add.mdc**：包含119个API接口、15个开发阶段、完整AI服务环境切换架构
   - **dev-api-guidelines-edit.mdc**：包含8个控制器、完整中间件、监控系统
   - **dev-aiapi-guidelines.mdc**：包含5个AI平台、87个AI接口、环境切换规范
   - **dev-thirdapi-guidelines.mdc**：包含第三方服务环境切换规范
   - **总接口统计**：206个总接口（119个API接口 + 87个AI接口）

3. **🤖 AI服务环境切换规范**：
   - 所有AI功能开发必须使用dev-aiapi-guidelines.mdc作为权威依据
   - AI API调用必须使用 AiServiceClient::call() 方法
   - 支持5个AI平台：DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包
   - 环境切换通过 AI_SERVICE_MODE 环境变量控制 (mock/real)
   - 模拟服务超时30秒，真实服务超时60秒
   - 必须实现AI服务降级和错误处理机制

4. **🔗 第三方服务环境切换规范**：
   - 所有第三方服务调用必须使用 ThirdPartyServiceClient::call() 方法
   - 环境切换通过 THIRD_PARTY_MODE 环境变量控制 (mock/real)
   - 支持微信、支付宝、短信等主要第三方服务
   - 必须实现第三方服务降级和错误处理机制

5. **🔄 持续更新机制**：
   - **新增功能** → 更新 `dev-api-guidelines-add.mdc`
   - **修复问题** → 更新 `dev-api-guidelines-edit.mdc`
   - **AI服务变更** → 更新 `dev-aiapi-guidelines.mdc`
   - **第三方服务变更** → 更新 `dev-thirdapi-guidelines.mdc`
   - **环境切换机制变更** → 更新 `index-new.mdc` 和对应专项文档
   - 定期同步所有文档，确保与index-new.mdc规范一致
   - AI服务接口变更需要同步更新相关控制器和服务层

6. **📊 文档使用统计和监控**：
   - 优先使用覆盖率最高的文档（dev-api-guidelines-add.mdc）
   - AI相关开发必须使用dev-aiapi-guidelines.mdc（100%AI接口覆盖）
   - 第三方服务开发必须使用dev-thirdapi-guidelines.mdc
   - 问题修复优先使用dev-api-guidelines-edit.mdc（包含完整修复方案）
   - 定期评估文档使用效果，持续优化文档结构

#### **🎯 文档选择决策树**

```
开发任务分析
├─ 是否涉及环境切换？
│  ├─ AI服务环境切换 → dev-aiapi-guidelines.mdc (主) + index-new.mdc (架构参考)
│  ├─ 第三方服务环境切换 → dev-thirdapi-guidelines.mdc (主) + index-new.mdc (架构参考)
│  └─ 否 → 继续判断
├─ 是否涉及客户端对接？
│  ├─ Python工具对接 → dev-api-guidelines-pyapi.mdc (最高权重) + index-new.mdc (架构边界)
│  ├─ WEB工具对接 → dev-api-guidelines-webapi.mdc (最高权重) + index-new.mdc (架构边界)
│  ├─ 管理后台对接 → dev-api-guidelines-adminapi.mdc (最高权重) + index-new.mdc (架构边界)
│  └─ 否 → 继续判断
├─ 是否涉及AI功能？
│  ├─ 是 → dev-aiapi-guidelines.mdc (必须主文档)
│  │  ├─ 新增AI功能 → + dev-api-guidelines-add.mdc (新增规范)
│  │  ├─ 修复AI功能 → + dev-api-guidelines-edit.mdc (修复规范)
│  │  └─ 纯AI接口调用 → 仅使用 dev-aiapi-guidelines.mdc
│  └─ 否 → 继续判断
├─ 是否涉及第三方服务？
│  ├─ 是 → dev-thirdapi-guidelines.mdc (必须主文档)
│  │  ├─ 新增第三方功能 → + dev-api-guidelines-add.mdc (新增规范)
│  │  ├─ 修复第三方功能 → + dev-api-guidelines-edit.mdc (修复规范)
│  │  └─ 纯第三方接口调用 → 仅使用 dev-thirdapi-guidelines.mdc
│  └─ 否 → 继续判断
├─ 是新功能开发？
│  ├─ 是 → dev-api-guidelines-add.mdc (主文档)
│  │  ├─ 涉及AI功能 → + dev-aiapi-guidelines.mdc (AI专项规范)
│  │  ├─ 涉及第三方服务 → + dev-thirdapi-guidelines.mdc (第三方专项规范)
│  │  └─ 涉及客户端对接 → + 对应的 pyapi/webapi/adminapi 文档
│  └─ 否 → 继续判断
├─ 是问题修复？
│  ├─ 是 → dev-api-guidelines-edit.mdc (主文档)
│  │  ├─ AI相关问题 → + dev-aiapi-guidelines.mdc (AI专项规范)
│  │  ├─ 第三方服务问题 → + dev-thirdapi-guidelines.mdc (第三方专项规范)
│  │  └─ 客户端对接问题 → + 对应的 pyapi/webapi/adminapi 文档
│  └─ 否 → 继续判断
└─ 复杂场景 → 多文档组合使用
   ├─ 架构重构 → dev-api-guidelines-edit.mdc + dev-api-guidelines-add.mdc + 相关专项文档
   ├─ 性能优化 → dev-api-guidelines-edit.mdc + 相关专项文档 (AI/第三方/客户端)
   ├─ 安全加固 → dev-api-guidelines-edit.mdc + dev-api-guidelines-add.mdc (如需新增安全功能)
   └─ 全栈开发 → 根据涉及的技术栈选择对应的多个文档组合
```


## 📝 技术栈总结

### 后端技术栈
- **管理后台**: Laravel 10
- **工具API接口服务**: Lumen 10
- **数据库**: MySQL 8.0.12
- **缓存**: Redis 7.4.2
- **Web服务器**: Nginx 1.26.2
- **PHP版本**: 8.1.29

### 前端技术栈
- **WEB网页工具**: 响应式UI布局（支持PC和移动），全静态前端调用工具API接口服务
- **Python用户终端工具**: Python + PySide6 + PyInstaller + WebSocket客户端

### 开发工具
- **操作系统**: Windows 11（开发环境）
- **生产环境**: CentOS 8 Stream
- **版本控制**: Git
- **API文档**: 基于OpenAPI规范

---

## 📝 文档维护说明

本文档是AI视频创作工具系统的核心架构规范，所有开发工作都应严格遵循本文档的规定。如需修改架构设计，必须先更新本文档并经过团队评审。

**最后更新**: 2025-08-03
**文档版本**: v4.0 - 开发文档使用指南完整版
**维护人员**: 开发团队

### **v4.0 更新内容**
- ✅ **开发文档使用指南**: 完整的分类文档应用场景明确定义
- ✅ **六大核心文档体系**: 明确的文档分类和职责边界
- ✅ **文档选择决策流程**: 快速决策树和使用优先级规则
- ✅ **环境切换机制规范**: AI服务和第三方服务的环境切换指导
- ✅ **多文档协作机制**: 文档组合使用的具体规则和最佳实践
- ✅ **架构合规性检查**: 完整的开发约束和注意事项
- ✅ **持续更新机制**: 文档维护和同步的规范流程

### **v3.0 更新内容**
- ✅ **完整项目架构图**: 包含环境切换服务客户端层的完整架构
- ✅ **业务流程图**: 8个核心业务流程，包含环境切换机制优化
- ✅ **环境切换机制**: AiServiceClient 和 ThirdPartyServiceClient 实现
- ✅ **性能指标**: 完整的系统性能要求和AI生成超时配置
- ✅ **AI模型配置**: 5个AI平台的业务模型配置矩阵
- ✅ **作品发布规则**: 完整的作品发布流程和安全规则
- ✅ **架构边界规范**: 资源下载铁律和WebSocket使用边界
