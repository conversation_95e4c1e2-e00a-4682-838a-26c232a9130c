-- 添加experience字段到p_users表
-- 解决用户成长API的500错误

-- 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'tool_api' 
  AND TABLE_NAME = 'p_users' 
  AND COLUMN_NAME = 'experience';

-- 如果字段不存在，则添加
ALTER TABLE p_users 
ADD COLUMN experience INT DEFAULT 0 COMMENT '用户经验值' 
AFTER level;

-- 为现有用户初始化经验值（基于等级计算）
UPDATE p_users 
SET experience = level * 1000 
WHERE experience = 0 OR experience IS NULL;

-- 验证字段添加成功
DESCRIBE p_users;

-- 查看示例数据
SELECT id, username, level, experience 
FROM p_users 
LIMIT 5;
