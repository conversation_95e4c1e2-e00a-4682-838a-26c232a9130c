<?php

/**
 * 检查数据库结构和用户成长API问题
 */

require_once 'php/api/vendor/autoload.php';

// 加载环境变量
$dotenv = Dotenv\Dotenv::createImmutable('php/api');
$dotenv->load();

echo "🔍 检查数据库结构和用户成长API问题...\n\n";

try {
    // 连接数据库
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'tool_api';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    $prefix = $_ENV['DB_PREFIX'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ 数据库连接成功\n";
    echo "📋 数据库配置:\n";
    echo "   - 主机: $host:$port\n";
    echo "   - 数据库: $database\n";
    echo "   - 表前缀: " . ($prefix ?: '无') . "\n\n";
    
    // 检查p_users表结构
    $tableName = $prefix . 'users';
    echo "🔍 检查表结构: $tableName\n";
    
    $stmt = $pdo->prepare("DESCRIBE `$tableName`");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "📊 表字段列表:\n";
    $hasExperience = false;
    foreach ($columns as $column) {
        $field = $column['Field'];
        $type = $column['Type'];
        $null = $column['Null'];
        $default = $column['Default'];
        
        if ($field === 'experience') {
            $hasExperience = true;
            echo "   ✅ $field ($type) - 找到experience字段\n";
        } else {
            echo "   - $field ($type)\n";
        }
    }
    
    if (!$hasExperience) {
        echo "\n❌ 缺少experience字段！\n";
        echo "🛠️  需要执行以下SQL添加字段:\n";
        echo "   ALTER TABLE `$tableName` ADD COLUMN experience INT DEFAULT 0 COMMENT '用户经验值' AFTER level;\n\n";
    } else {
        echo "\n✅ experience字段存在\n\n";
    }
    
    // 检查用户数据
    echo "👥 检查用户数据:\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM `$tableName`");
    $stmt->execute();
    $userCount = $stmt->fetch()['total'];
    echo "   - 用户总数: $userCount\n";
    
    if ($userCount > 0) {
        $stmt = $pdo->prepare("SELECT id, username, level, " . ($hasExperience ? "experience" : "0 as experience") . " FROM `$tableName` LIMIT 3");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        echo "   - 示例用户数据:\n";
        foreach ($users as $user) {
            echo "     ID: {$user['id']}, 用户名: {$user['username']}, 等级: {$user['level']}, 经验: {$user['experience']}\n";
        }
    }
    
    // 测试token验证
    echo "\n🔑 测试token验证:\n";
    $testToken = 'nQ2PapFzEiDkQdcDnGg10A6jm8edtnZrml3s1nMDREvb9';
    
    // 模拟token解析（这里需要实际的token解析逻辑）
    echo "   - 测试token: $testToken\n";
    echo "   - 注意: 需要检查Redis中的token存储和ApiTokenHelper的实现\n";
    
    // 检查Redis连接
    echo "\n🔴 检查Redis连接:\n";
    try {
        $redis = new Redis();
        $redisHost = $_ENV['REDIS_HOST'] ?? '127.0.0.1';
        $redisPort = $_ENV['REDIS_PORT'] ?? 6379;
        $redis->connect($redisHost, $redisPort);
        echo "   ✅ Redis连接成功 ($redisHost:$redisPort)\n";
        
        // 检查token相关的键
        $keys = $redis->keys('user:token:*');
        echo "   - 找到 " . count($keys) . " 个用户token\n";
        
        $redis->close();
    } catch (Exception $e) {
        echo "   ❌ Redis连接失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n📋 问题诊断总结:\n";
echo "1. 检查数据库表结构是否包含experience字段\n";
echo "2. 检查Redis连接和token存储\n";
echo "3. 检查AuthService的token验证逻辑\n";
echo "4. 检查UserGrowthService的字段访问\n";

?>
