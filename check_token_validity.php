<?php

/**
 * 检查Token有效性
 */

echo "🔑 检查Token有效性...\n\n";

$token = 'qkR9zACDE6kjqKbNCOSyJYr7BDPKt9RYlbehPzr5KwxyQ';

try {
    require_once 'php/api/vendor/autoload.php';
    
    // 加载环境变量
    $dotenv = Dotenv\Dotenv::createImmutable('php/api');
    $dotenv->load();
    
    echo "✅ 环境加载成功\n";
    echo "🔑 测试Token: $token\n\n";
    
    // 连接Redis检查token
    $redis = new Redis();
    $redisHost = $_ENV['REDIS_HOST'] ?? '127.0.0.1';
    $redisPort = $_ENV['REDIS_PORT'] ?? 6379;
    
    if ($redis->connect($redisHost, $redisPort)) {
        echo "✅ Redis连接成功\n";
        
        // 检查token相关的键
        $tokenKey = "user:token:$token";
        $exists = $redis->exists($tokenKey);
        
        echo "🔍 检查Token键: $tokenKey\n";
        echo "   存在状态: " . ($exists ? "存在" : "不存在") . "\n";
        
        if ($exists) {
            $userData = $redis->get($tokenKey);
            echo "   用户数据: $userData\n";
            
            $ttl = $redis->ttl($tokenKey);
            echo "   剩余时间: " . ($ttl > 0 ? $ttl . "秒" : "永久") . "\n";
        }
        
        // 查找所有token键
        $allTokens = $redis->keys('user:token:*');
        echo "\n📊 Redis中的所有token数量: " . count($allTokens) . "\n";
        
        if (count($allTokens) > 0) {
            echo "   示例token键:\n";
            foreach (array_slice($allTokens, 0, 3) as $key) {
                echo "   - $key\n";
            }
        }
        
        $redis->close();
    } else {
        echo "❌ Redis连接失败\n";
    }
    
    // 测试数据库中的用户
    echo "\n👥 检查数据库用户...\n";
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $database = $_ENV['DB_DATABASE'] ?? 'ai_tool';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    
    $stmt = $pdo->query("SELECT id, username, status FROM p_users WHERE status = 1 LIMIT 5");
    $users = $stmt->fetchAll();
    
    echo "   活跃用户数量: " . count($users) . "\n";
    foreach ($users as $user) {
        echo "   - ID: {$user['id']}, 用户名: {$user['username']}, 状态: {$user['status']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
}

echo "\n💡 建议:\n";
echo "1. 如果token不存在于Redis中，可能已过期\n";
echo "2. 尝试重新登录获取新的token\n";
echo "3. 检查token是否正确复制（无多余空格）\n";
echo "4. 确认用户账户状态正常\n";

?>
