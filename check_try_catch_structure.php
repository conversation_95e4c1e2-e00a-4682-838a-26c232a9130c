<?php

/**
 * 扫描控制器文件，检查public function方法是否以try {开始
 */

$controllerDir = 'php/api/app/Http/Controllers/Api';
$results = [];

// 获取所有PHP文件
$files = glob($controllerDir . '/*.php');
sort($files);

echo "开始扫描 " . count($files) . " 个控制器文件...\n\n";

foreach ($files as $file) {
    $fileName = basename($file);
    echo "正在检查: $fileName\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  错误: 无法读取文件\n";
        continue;
    }
    
    // 使用更精确的正则表达式查找public function
    $pattern = '/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{/';
    preg_match_all($pattern, $content, $functionMatches, PREG_OFFSET_CAPTURE);
    
    $methodsWithoutTry = [];
    $totalMethods = 0;
    
    foreach ($functionMatches[0] as $index => $match) {
        $methodName = $functionMatches[1][$index][0];
        $functionStart = $match[1] + strlen($match[0]);
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }
        
        $totalMethods++;
        
        // 找到函数体的开始位置，提取函数体内容
        $braceCount = 1;
        $pos = $functionStart;
        $functionBody = '';
        
        while ($pos < strlen($content) && $braceCount > 0) {
            $char = $content[$pos];
            if ($char === '{') {
                $braceCount++;
            } elseif ($char === '}') {
                $braceCount--;
            }
            
            if ($braceCount > 0) {
                $functionBody .= $char;
            }
            $pos++;
        }
        
        // 检查方法体的第一行是否以try {开始
        $lines = explode("\n", trim($functionBody));
        $firstNonEmptyLine = '';
        
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            if (!empty($trimmedLine)) {
                $firstNonEmptyLine = $trimmedLine;
                break;
            }
        }
        
        // 检查第一行是否以try {开始
        if (!preg_match('/^try\s*\{/', $firstNonEmptyLine)) {
            $methodsWithoutTry[] = $methodName;
        }
    }
    
    if (!empty($methodsWithoutTry)) {
        $results[] = [
            'file' => $fileName,
            'methods' => $methodsWithoutTry,
            'total' => $totalMethods
        ];
    }
    
    echo "  找到 $totalMethods 个public方法，" . count($methodsWithoutTry) . " 个不符合要求\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "扫描完成！以下是不符合try-catch架构的方法：\n";
echo str_repeat("=", 60) . "\n\n";

if (empty($results)) {
    echo "✅ 所有控制器文件的public function方法都正确以'try {'开始！\n";
} else {
    $counter = 1;
    foreach ($results as $result) {
        echo "### {$counter}. {$result['file']} ({$result['total']}个方法)\n";
        foreach ($result['methods'] as $method) {
            echo "- [ ] `{$method}()`\n";
        }
        echo "\n";
        $counter++;
    }
}

// 统计总方法数
$totalMethodsCount = 0;
foreach ($files as $file) {
    $content = file_get_contents($file);
    $pattern = '/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{/';
    preg_match_all($pattern, $content, $functionMatches);

    foreach ($functionMatches[1] as $methodName) {
        if ($methodName !== '__construct') {
            $totalMethodsCount++;
        }
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 扫描统计信息\n";
echo str_repeat("=", 60) . "\n";
echo "总计扫描文件数: " . count($files) . "\n";
echo "总计public方法数: " . $totalMethodsCount . "\n";
echo "需要修复的文件数: " . count($results) . "\n";
echo "符合规范的文件数: " . (count($files) - count($results)) . "\n";
echo "符合规范率: " . round((count($files) - count($results)) / count($files) * 100, 2) . "%\n";

?>
