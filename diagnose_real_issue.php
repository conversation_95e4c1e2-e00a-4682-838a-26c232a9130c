<?php

/**
 * 重新诊断真实问题 - PHP 8.1.29环境
 */

echo "🔍 重新诊断用户成长API问题 (PHP " . PHP_VERSION . ")...\n\n";

// 1. 检查路由修复是否生效
echo "📋 检查路由配置修复...\n";
$routeFile = 'php/api/routes/web.php';
$routeContent = file_get_contents($routeFile);

if (strpos($routeContent, "middleware' => 'auth'") !== false) {
    echo "❌ 路由中仍然使用auth中间件\n";
} else {
    echo "✅ 路由中间件已移除\n";
}

// 2. 直接测试API端点
echo "\n🧪 直接测试API端点...\n";

try {
    // 设置环境
    require_once 'php/api/vendor/autoload.php';
    
    // 模拟请求
    $token = 'nQ2PapFzEiDkQdcDnGg10A6jm8edtnZrml3s1nMDREvb9';
    
    // 创建模拟请求对象
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/py-api/user-growth/profile';
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $token;
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    echo "✅ 环境设置完成\n";
    echo "🔑 使用Token: $token\n";
    
    // 测试AuthService
    echo "\n🔐 测试AuthService...\n";
    
    // 创建简单的Request对象模拟
    $requestData = [
        'headers' => [
            'Authorization' => 'Bearer ' . $token
        ]
    ];
    
    echo "📊 模拟请求数据准备完成\n";
    
} catch (Exception $e) {
    echo "❌ 环境加载失败: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

// 3. 检查具体的错误原因
echo "\n🔍 分析'Illegal offset type'错误...\n";
echo "这个错误通常发生在以下情况:\n";
echo "1. 数组键类型不正确（如使用对象作为数组键）\n";
echo "2. 认证配置中的guard配置问题\n";
echo "3. 中间件参数传递错误\n";

// 4. 检查认证配置
echo "\n⚙️  检查认证相关配置...\n";

$envFile = 'php/api/.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    // 检查关键配置
    $configs = [
        'APP_KEY' => 'APP_KEY',
        'DB_CONNECTION' => 'DB_CONNECTION', 
        'REDIS_HOST' => 'REDIS_HOST'
    ];
    
    foreach ($configs as $key => $name) {
        if (strpos($envContent, $key) !== false) {
            echo "✅ 找到配置: $name\n";
        } else {
            echo "❌ 缺少配置: $name\n";
        }
    }
} else {
    echo "❌ .env文件不存在\n";
}

echo "\n💡 建议的解决步骤:\n";
echo "1. 确认路由中间件已完全移除\n";
echo "2. 检查AuthService的token解析逻辑\n";
echo "3. 验证Redis连接和token存储\n";
echo "4. 测试简化版本的API调用\n";

echo "\n🎯 现在请重新在浏览器中测试API接口\n";
echo "如果仍有问题，请提供最新的错误信息。\n";

?>
