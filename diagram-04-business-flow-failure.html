<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务流程3: 处理失败的业务流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>❌ 业务流程3: 处理失败的业务流程（环境切换优化版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Python用户终端工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)
    AI->>SC: 返回失败结果
    SC->>A: 返回失败结果+环境模式信息
    A->>W: 返回失败结果
    W->>P: 推送失败结果(详细错误信息)
    A->>DB: 更新业务日志(状态:失败)
    A->>DB: 退还积分(解冻→退还)
    A->>R: 同步退还状态
    A->>E: 发布失败事件(异步处理)
    Note over P: 业务失败，积分已退还
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
