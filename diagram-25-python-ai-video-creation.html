<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python用户终端工具业务流程F-1: AI创作视频任务流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Python用户终端工具业务流程F-1: AI创作视频任务流程</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant W as WebSocket服务
    participant SC as AiServiceClient

    P->>A: 创建AI创作视频任务(项目参数/Token)
    A->>A: 验证Token和用户权限
    A->>DB: 检查用户积分余额
    alt 积分不足
        A->>P: 返回积分不足错误
    else 积分充足
        A->>DB: 创建视频项目记录
        A->>DB: 创建任务队列记录
        A->>R: 缓存项目状态
        A->>W: 建立WebSocket连接
        A->>P: 返回项目ID和WebSocket连接信息
        
        Note over A: 开始执行AI任务调度
        A->>SC: 调用AI任务调度服务
        SC->>A: 返回任务执行状态
        A->>W: 推送任务进度到Python工具
        W->>P: 实时显示任务进度
        
        alt 任务执行成功
            A->>DB: 更新项目状态为完成
            A->>R: 更新缓存状态
            A->>W: 推送完成通知
            W->>P: 显示任务完成
        else 任务执行失败
            A->>DB: 更新项目状态为失败
            A->>DB: 退还用户积分
            A->>W: 推送失败通知
            W->>P: 显示任务失败和错误信息
        end
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
