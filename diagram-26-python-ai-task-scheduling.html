<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python用户终端工具业务流程F-1: AI任务调度流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Python用户终端工具业务流程F-1: AI任务调度流程</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 请求AI任务(类型/参数/Token)
    A->>A: 验证Token和任务参数
    A->>DB: 检查用户积分和权限
    A->>DB: 创建AI任务记录
    
    Note over A: 根据任务类型调度不同AI服务
    alt 文生文任务
        A->>SC: 调用文本生成服务(DeepSeek)
        SC->>AI: 请求文本生成
        AI->>SC: 返回生成文本
    else 图生图任务
        A->>SC: 调用图像生成服务(LiblibAI)
        SC->>AI: 请求图像生成
        AI->>SC: 返回生成图像URL
    else 图生视频任务
        A->>SC: 调用视频生成服务(KlingAI)
        SC->>AI: 请求视频生成
        AI->>SC: 返回生成视频URL
    else 语音生成任务
        A->>SC: 调用语音合成服务(MiniMax)
        SC->>AI: 请求语音合成
        AI->>SC: 返回语音文件URL
    else 音效/音乐生成任务
        A->>SC: 调用音频生成服务(火山引擎豆包)
        SC->>AI: 请求音频生成
        AI->>SC: 返回音频文件URL
    end
    
    SC->>A: 返回AI生成结果
    A->>DB: 更新任务状态和结果
    A->>R: 缓存任务结果
    A->>P: 返回AI生成结果
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
