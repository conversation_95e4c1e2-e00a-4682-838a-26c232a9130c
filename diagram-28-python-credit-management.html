<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python用户终端工具业务流程F-4: 积分管理流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 Python用户终端工具业务流程F-4: 积分管理流程</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 查询积分信息(Token)
    A->>A: 验证Token有效性
    A->>R: 检查积分缓存
    alt 缓存命中
        R->>A: 返回缓存的积分信息
    else 缓存未命中
        A->>DB: 查询用户积分余额
        A->>DB: 查询积分明细记录
        A->>R: 更新积分缓存
    end
    A->>P: 返回积分信息(余额/明细)
    
    Note over P: 用户请求积分明细
    P->>A: 查询积分明细(分页参数/Token)
    A->>DB: 分页查询积分明细
    A->>P: 返回积分明细列表(收入/支出/余额变化)
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
