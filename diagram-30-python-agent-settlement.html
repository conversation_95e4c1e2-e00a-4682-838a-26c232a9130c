<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python用户终端工具业务流程F-5: 代理结算流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💸 Python用户终端工具业务流程F-5: 代理结算流程</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Python用户终端工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    P->>A: 查询可结算金额(Token)
    A->>A: 验证代理身份
    A->>DB: 计算未结算推广收益
    A->>P: 返回可结算金额
    
    P->>A: 申请提现结算(金额/收款方式/Token)
    A->>A: 验证提现参数和最小金额
    A->>DB: 检查可结算余额
    alt 余额不足或不满足提现条件
        A->>P: 返回提现失败(原因)
    else 满足提现条件
        A->>DB: 创建提现申请记录
        A->>DB: 冻结对应金额
        A->>P: 返回提现申请成功(审核中)
        
        Note over A: 管理员审核通过后
        A->>TP: 调用支付接口转账
        TP->>A: 返回转账结果
        alt 转账成功
            A->>DB: 更新提现状态为成功
            A->>DB: 扣除已结算金额
            A->>R: 更新代理收益缓存
            A->>P: 推送提现成功通知
        else 转账失败
            A->>DB: 更新提现状态为失败
            A->>DB: 解冻冻结金额
            A->>P: 推送提现失败通知
        end
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
