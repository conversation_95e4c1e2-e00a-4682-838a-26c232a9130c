<?php

/**
 * 执行数据库修复 - 添加缺少的字段
 */

require_once 'php/api/vendor/autoload.php';

// 加载环境变量
$dotenv = Dotenv\Dotenv::createImmutable('php/api');
$dotenv->load();

echo "🔧 开始修复数据库结构...\n\n";

try {
    // 连接数据库
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'tool_api';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    $prefix = $_ENV['DB_PREFIX'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $tableName = $prefix . 'users';
    echo "✅ 数据库连接成功，操作表: $tableName\n\n";
    
    // 检查并添加缺少的字段
    $fieldsToAdd = [
        'level' => "ADD COLUMN level INT DEFAULT 1 COMMENT '用户等级' AFTER nickname",
        'experience' => "ADD COLUMN experience INT DEFAULT 0 COMMENT '用户经验值' AFTER level",
        'bio' => "ADD COLUMN bio TEXT NULL COMMENT '用户简介' AFTER avatar",
        'follower_count' => "ADD COLUMN follower_count INT DEFAULT 0 COMMENT '粉丝数量' AFTER bio",
        'following_count' => "ADD COLUMN following_count INT DEFAULT 0 COMMENT '关注数量' AFTER follower_count"
    ];
    
    // 检查现有字段
    $stmt = $pdo->prepare("DESCRIBE `$tableName`");
    $stmt->execute();
    $existingColumns = array_column($stmt->fetchAll(), 'Field');
    
    echo "📋 现有字段: " . implode(', ', $existingColumns) . "\n\n";
    
    // 添加缺少的字段
    foreach ($fieldsToAdd as $fieldName => $alterSQL) {
        if (!in_array($fieldName, $existingColumns)) {
            echo "➕ 添加字段: $fieldName\n";
            try {
                $pdo->exec("ALTER TABLE `$tableName` $alterSQL");
                echo "   ✅ 成功添加字段: $fieldName\n";
            } catch (Exception $e) {
                echo "   ❌ 添加字段失败: $fieldName - " . $e->getMessage() . "\n";
            }
        } else {
            echo "⏭️  字段已存在: $fieldName\n";
        }
    }
    
    echo "\n🔄 初始化现有用户数据...\n";
    
    // 为现有用户初始化数据
    $updateSQL = "UPDATE `$tableName` SET 
        level = COALESCE(level, 1),
        experience = COALESCE(experience, level * 1000, 1000),
        follower_count = COALESCE(follower_count, 0),
        following_count = COALESCE(following_count, 0)";
    
    $stmt = $pdo->prepare($updateSQL);
    $stmt->execute();
    $affectedRows = $stmt->rowCount();
    echo "✅ 更新了 $affectedRows 个用户的数据\n\n";
    
    // 验证修改结果
    echo "🔍 验证修改结果:\n";
    $stmt = $pdo->prepare("DESCRIBE `$tableName`");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $requiredFields = ['level', 'experience', 'bio', 'follower_count', 'following_count'];
    foreach ($requiredFields as $field) {
        $found = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $field) {
                echo "   ✅ $field: {$column['Type']}\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "   ❌ 缺少字段: $field\n";
        }
    }
    
    // 查看示例数据
    echo "\n👥 示例用户数据:\n";
    $stmt = $pdo->prepare("SELECT id, username, level, experience, follower_count, following_count FROM `$tableName` LIMIT 3");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "   ID: {$user['id']}, 用户: {$user['username']}, 等级: {$user['level']}, 经验: {$user['experience']}, 粉丝: {$user['follower_count']}, 关注: {$user['following_count']}\n";
    }
    
    echo "\n🎉 数据库修复完成！\n";
    echo "现在可以重新测试用户成长API了。\n";
    
} catch (Exception $e) {
    echo "❌ 修复失败: " . $e->getMessage() . "\n";
    echo "请检查数据库连接和权限设置。\n";
}

?>
