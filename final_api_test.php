<?php

/**
 * 最终API测试 - 验证resources方法修复
 */

echo "🎯 最终API测试 - 验证resources方法修复\n\n";

// 直接测试API端点
$apiUrl = 'http://localhost/tool_api/php/api/public/index.php/py-api/user-growth/profile';
$token = 'nQ2PapFzEiDkQdcDnGg10A6jm8edtnZrml3s1nMDREvb9';

echo "📡 测试API: $apiUrl\n";
echo "🔑 Token: $token\n\n";

// 设置请求头
$headers = [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json',
    'Accept: application/json',
    'User-Agent: Final-Test-Script'
];

// 初始化cURL
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $apiUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_SSL_VERIFYPEER => false
]);

echo "🚀 发送API请求...\n";

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "📊 响应结果:\n";
echo "   HTTP状态码: $httpCode\n";

if ($error) {
    echo "   ❌ cURL错误: $error\n";
} else {
    echo "   📄 响应长度: " . strlen($response) . " 字节\n";
    
    // 解析响应
    $jsonData = json_decode($response, true);
    
    if ($httpCode == 200) {
        echo "   ✅ HTTP状态正常\n";
        
        if ($jsonData && isset($jsonData['code'])) {
            if ($jsonData['code'] == 200) {
                echo "   🎉 API调用成功！\n";
                echo "   📋 用户成长数据:\n";
                
                if (isset($jsonData['data'])) {
                    $data = $jsonData['data'];
                    echo "      - 用户ID: " . ($data['user_id'] ?? '未知') . "\n";
                    echo "      - 等级: " . ($data['level'] ?? '未知') . "\n";
                    echo "      - 经验值: " . ($data['experience'] ?? '未知') . "\n";
                    echo "      - 等级进度: " . ($data['level_progress'] ?? '未知') . "%\n";
                    echo "      - 称号: " . ($data['title'] ?? '未知') . "\n";
                    
                    if (isset($data['statistics'])) {
                        echo "      - 统计数据: " . json_encode($data['statistics']) . "\n";
                    }
                }
                
                echo "\n✅ 用户成长API修复成功！\n";
                
            } else {
                echo "   ❌ API返回错误\n";
                echo "   错误码: " . $jsonData['code'] . "\n";
                echo "   错误信息: " . ($jsonData['message'] ?? '未知') . "\n";
                
                // 检查是否还是resources方法的问题
                if (isset($jsonData['message']) && strpos($jsonData['message'], 'resources()') !== false) {
                    echo "\n🔍 仍然是resources方法问题，可能需要重启Web服务器\n";
                }
            }
        } else {
            echo "   ❌ 响应格式异常\n";
        }
        
    } else {
        echo "   ❌ HTTP状态异常\n";
        
        if ($jsonData && isset($jsonData['message'])) {
            echo "   错误信息: " . $jsonData['message'] . "\n";
            
            if (strpos($jsonData['message'], 'resources()') !== false) {
                echo "\n🔧 检测到resources方法问题，建议:\n";
                echo "   1. 重启Web服务器 (Apache/Nginx)\n";
                echo "   2. 重启PHP-FPM (如果使用)\n";
                echo "   3. 清除OPcache缓存\n";
            }
        }
    }
    
    // 显示原始响应（前500字符）
    echo "\n📄 原始响应片段:\n";
    echo substr($response, 0, 500) . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 修复总结:\n";
echo "1. ✅ 添加了数据库字段 (level, experience等)\n";
echo "2. ✅ 修复了UserGrowthService字段访问逻辑\n";
echo "3. ✅ 移除了路由中间件问题\n";
echo "4. ✅ 在User模型中添加了resources()关系方法\n";
echo "\n💡 如果仍有问题，请重启Web服务器后再次测试\n";

?>
