<?php

/**
 * 最终验证脚本 - 100%准确性检查
 */

$controllerDir = 'php/api/app/Http/Controllers/Api';
$files = glob($controllerDir . '/*.php');
sort($files);

$totalFiles = count($files);
$totalMethods = 0;
$problemMethods = [];
$allMethodsDetails = [];

echo "🔍 开始最终验证 $totalFiles 个控制器文件...\n\n";

foreach ($files as $file) {
    $fileName = basename($file);
    echo "检查: $fileName\n";
    
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    // 查找所有public function
    for ($i = 0; $i < count($lines); $i++) {
        $line = trim($lines[$i]);
        
        // 匹配public function行
        if (preg_match('/^public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $line, $matches)) {
            $methodName = $matches[1];
            
            // 跳过构造函数
            if ($methodName === '__construct') {
                continue;
            }
            
            $totalMethods++;
            
            // 找到函数体开始的位置（找到{）
            $braceFound = false;
            $startLine = $i;
            
            // 在当前行或后续行中找到开始的大括号
            for ($j = $i; $j < count($lines); $j++) {
                if (strpos($lines[$j], '{') !== false) {
                    $startLine = $j;
                    $braceFound = true;
                    break;
                }
            }
            
            if (!$braceFound) {
                continue;
            }
            
            // 找到函数体的第一行非空内容
            $firstNonEmptyLine = '';
            for ($k = $startLine + 1; $k < count($lines); $k++) {
                $trimmedLine = trim($lines[$k]);
                if (!empty($trimmedLine)) {
                    $firstNonEmptyLine = $trimmedLine;
                    break;
                }
            }
            
            // 检查是否以try {开始
            $hasTry = preg_match('/^try\s*\{/', $firstNonEmptyLine);
            
            $methodInfo = [
                'file' => $fileName,
                'method' => $methodName,
                'line' => $i + 1,
                'first_line' => $firstNonEmptyLine,
                'has_try' => $hasTry
            ];
            
            $allMethodsDetails[] = $methodInfo;
            
            if (!$hasTry) {
                $problemMethods[] = $methodInfo;
                echo "  ❌ $methodName(): '$firstNonEmptyLine'\n";
            } else {
                echo "  ✅ $methodName(): 正确\n";
            }
        }
    }
    echo "\n";
}

echo str_repeat("=", 80) . "\n";
echo "🎯 最终验证结果\n";
echo str_repeat("=", 80) . "\n";

if (empty($problemMethods)) {
    echo "✅ 验证通过！所有 $totalMethods 个public方法都正确以'try {'开始\n\n";
} else {
    echo "❌ 发现问题！以下方法不符合要求：\n\n";
    
    $groupedProblems = [];
    foreach ($problemMethods as $problem) {
        $groupedProblems[$problem['file']][] = $problem;
    }
    
    $counter = 1;
    foreach ($groupedProblems as $file => $methods) {
        echo "### $counter. $file (" . count($methods) . "个问题方法)\n";
        foreach ($methods as $method) {
            echo "- [ ] `{$method['method']}()` (第{$method['line']}行)\n";
        }
        echo "\n";
        $counter++;
    }
}

echo "📊 统计信息:\n";
echo "- 扫描文件总数: $totalFiles\n";
echo "- public方法总数: $totalMethods\n";
echo "- 问题方法总数: " . count($problemMethods) . "\n";
echo "- 合规率: " . round(($totalMethods - count($problemMethods)) / $totalMethods * 100, 2) . "%\n";

// 保存详细报告
$reportData = [
    'scan_time' => date('Y-m-d H:i:s'),
    'total_files' => $totalFiles,
    'total_methods' => $totalMethods,
    'problem_methods' => count($problemMethods),
    'compliance_rate' => round(($totalMethods - count($problemMethods)) / $totalMethods * 100, 2),
    'all_methods' => $allMethodsDetails,
    'problems' => $problemMethods
];

file_put_contents('verification_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n详细报告已保存到: verification_report.json\n";

?>
