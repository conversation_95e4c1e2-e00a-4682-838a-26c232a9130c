-- 修复p_users表缺少的字段
-- 解决用户成长API的500错误

-- 检查当前表结构
DESCRIBE p_users;

-- 添加缺少的字段
-- 1. 添加level字段（用户等级）
ALTER TABLE p_users 
ADD COLUMN level INT DEFAULT 1 COMMENT '用户等级' 
AFTER nickname;

-- 2. 添加experience字段（用户经验值）
ALTER TABLE p_users 
ADD COLUMN experience INT DEFAULT 0 COMMENT '用户经验值' 
AFTER level;

-- 3. 添加bio字段（用户简介）
ALTER TABLE p_users 
ADD COLUMN bio TEXT NULL COMMENT '用户简介' 
AFTER avatar;

-- 4. 添加follower_count字段（粉丝数）
ALTER TABLE p_users 
ADD COLUMN follower_count INT DEFAULT 0 COMMENT '粉丝数量' 
AFTER bio;

-- 5. 添加following_count字段（关注数）
ALTER TABLE p_users 
ADD COLUMN following_count INT DEFAULT 0 COMMENT '关注数量' 
AFTER follower_count;

-- 为现有用户初始化数据
UPDATE p_users SET 
    level = 1,
    experience = 1000,  -- 基于等级计算的默认经验值
    follower_count = 0,
    following_count = 0
WHERE level IS NULL OR experience IS NULL;

-- 验证修改结果
DESCRIBE p_users;

-- 查看示例数据
SELECT id, username, level, experience, follower_count, following_count 
FROM p_users 
LIMIT 5;
