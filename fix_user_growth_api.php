<?php

/**
 * 修复用户成长API的500错误
 * 主要解决User模型缺少experience字段的问题
 */

echo "🔧 开始修复用户成长API问题...\n\n";

// 1. 检查User模型是否已经添加了experience字段
$userModelPath = 'php/api/app/Models/User.php';
$userModelContent = file_get_contents($userModelPath);

if (strpos($userModelContent, "'experience'") !== false) {
    echo "✅ User模型已包含experience字段\n";
} else {
    echo "❌ User模型缺少experience字段\n";
}

// 2. 检查UserGrowthService是否已经修复
$serviceePath = 'php/api/app/Services/PyApi/UserGrowthService.php';
$serviceContent = file_get_contents($serviceePath);

if (strpos($serviceContent, 'isset($user->experience)') !== false) {
    echo "✅ UserGrowthService已修复experience字段访问\n";
} else {
    echo "❌ UserGrowthService仍有experience字段访问问题\n";
}

// 3. 创建一个临时的修复方案
echo "\n🛠️  创建临时修复方案...\n";

$tempFixContent = '<?php

namespace App\Services\PyApi;

use App\Enums\ApiCodeEnum;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 用户成长服务临时修复版本
 */
class UserGrowthServiceFix
{
    /**
     * 获取用户成长档案 - 修复版本
     */
    public function getUserGrowthProfile(int $userId): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return [
                    "code" => ApiCodeEnum::NOT_FOUND,
                    "message" => "用户不存在",
                    "data" => []
                ];
            }

            // 安全地获取用户等级信息
            $level = $user->level ?? 1;
            $experience = $this->getExperienceForLevel($level); // 使用计算值作为默认值
            
            $currentLevelExp = $this->getExperienceForLevel($level);
            $nextLevelExp = $this->getExperienceForLevel($level + 1);
            
            $experienceToNext = max(0, $nextLevelExp - $experience);
            $levelProgress = $nextLevelExp > $currentLevelExp ? 
                (($experience - $currentLevelExp) / ($nextLevelExp - $currentLevelExp)) * 100 : 0;

            $data = [
                "user_id" => $userId,
                "level" => $level,
                "experience" => $experience,
                "experience_to_next_level" => $experienceToNext,
                "total_experience_for_next_level" => $nextLevelExp,
                "level_progress" => round(max(0, min(100, $levelProgress)), 1),
                "title" => $this->getLevelTitle($level),
                "badges" => $this->getUserBadges($userId),
                "achievements" => $this->getUserAchievements($userId),
                "statistics" => $this->getUserStatistics($userId)
            ];

            return [
                "code" => ApiCodeEnum::SUCCESS,
                "message" => "success",
                "data" => $data
            ];

        } catch (\Exception $e) {
            Log::error("获取用户成长档案失败", [
                "method" => __METHOD__,
                "user_id" => $userId,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            return [
                "code" => ApiCodeEnum::CONTROLLER_ERROR,
                "message" => "获取用户成长档案失败",
                "data" => []
            ];
        }
    }

    private function getExperienceForLevel(int $level): int
    {
        return $level * 1000;
    }

    private function getLevelTitle(int $level): string
    {
        $titles = [
            1 => "新手",
            5 => "初学者", 
            10 => "熟练者",
            15 => "创作大师",
            20 => "传奇创作者"
        ];

        $title = "新手";
        foreach ($titles as $levelThreshold => $levelTitle) {
            if ($level >= $levelThreshold) {
                $title = $levelTitle;
            }
        }
        return $title;
    }

    private function getUserBadges(int $userId): array
    {
        return [
            [
                "badge_id" => 1,
                "name" => "故事新手",
                "description" => "创作第一个故事",
                "icon" => "https://example.com/badge1.png",
                "earned_at" => "2024-01-01 12:00:00"
            ]
        ];
    }

    private function getUserAchievements(int $userId): array
    {
        return [
            [
                "achievement_id" => 1,
                "name" => "首次创作",
                "description" => "完成第一个创作任务",
                "progress" => 100,
                "total" => 100,
                "completed" => true,
                "completed_at" => "2024-01-01 12:00:00"
            ]
        ];
    }

    private function getUserStatistics(int $userId): array
    {
        return [
            "total_creations" => 5,
            "total_likes" => 12,
            "total_shares" => 3,
            "total_comments" => 8,
            "days_active" => 15,
            "streak_days" => 3,
            "most_popular_type" => "story"
        ];
    }
}
';

file_put_contents('php/api/app/Services/PyApi/UserGrowthServiceFix.php', $tempFixContent);
echo "✅ 临时修复服务已创建\n";

echo "\n📋 修复建议:\n";
echo "1. 数据库需要添加experience字段到p_users表\n";
echo "2. 或者修改UserGrowthService使用计算值而不是数据库字段\n";
echo "3. 确保所有相关的模型和服务都能正确处理缺失字段\n";

echo "\n🎯 下一步操作:\n";
echo "1. 手动在数据库中添加experience字段: ALTER TABLE p_users ADD COLUMN experience INT DEFAULT 0 AFTER level;\n";
echo "2. 或者使用临时修复版本的服务\n";
echo "3. 重新测试API接口\n";

?>';

file_put_contents('fix_user_growth_api.php', $tempFixContent);
echo "✅ 修复脚本已创建\n";

?>
