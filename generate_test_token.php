<?php

/**
 * 生成测试Token
 */

echo "🔑 生成新的测试Token...\n\n";

try {
    require_once 'php/api/vendor/autoload.php';
    
    // 加载环境变量
    $dotenv = Dotenv\Dotenv::createImmutable('php/api');
    $dotenv->load();
    
    // 连接Redis
    $redis = new Redis();
    $redisHost = $_ENV['REDIS_HOST'] ?? '127.0.0.1';
    $redisPort = $_ENV['REDIS_PORT'] ?? 6379;
    
    if (!$redis->connect($redisHost, $redisPort)) {
        throw new Exception("Redis连接失败");
    }
    
    echo "✅ Redis连接成功\n";
    
    // 连接数据库
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $database = $_ENV['DB_DATABASE'] ?? 'ai_tool';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    echo "✅ 数据库连接成功\n";
    
    // 获取第一个用户
    $stmt = $pdo->query("SELECT * FROM p_users WHERE status = 1 LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        throw new Exception("没有找到活跃用户");
    }
    
    echo "👤 使用用户: ID={$user['id']}, 用户名={$user['username']}\n";
    
    // 手动生成token（模拟ApiTokenHelper逻辑）
    $userId = $user['id'];

    // 生成随机字符串部分（默认长度20）
    $randomStr = bin2hex(random_bytes(10)); // 20字符

    // 生成hash部分（模拟Hashids编码）
    $hashData = [$userId, time(), mt_rand(0, 10000)];
    $hashStr = base64_encode(json_encode($hashData)); // 简化的hash

    $newToken = $randomStr . $hashStr;
    echo "🔑 新Token: $newToken\n";
    echo "👤 用户ID: $userId\n";

    // 使用系统标准的存储方式
    $tokenKey = "user:token:$userId";  // 使用用户ID作为键
    $encryptedToken = md5($newToken);  // 直接使用md5加密

    echo "🔐 加密Token: $encryptedToken\n";
    echo "🗝️ Redis键: $tokenKey\n";

    // 设置token，有效期24小时
    $result = $redis->setex($tokenKey, 86400, $encryptedToken);
    
    if ($result) {
        echo "✅ Token已存储到Redis\n";
        echo "⏰ 有效期: 24小时\n";

        // 验证存储
        $stored = $redis->get($tokenKey);
        if ($stored) {
            echo "✅ Token验证成功\n";
            echo "   存储的加密Token: $stored\n";
            echo "   用户ID: $userId\n";
            echo "   用户名: {$user['username']}\n";

            // 验证token格式
            echo "   Token长度: " . strlen($newToken) . " 字符\n";
        }
        
        echo "\n🎯 测试说明:\n";
        echo "1. 在API测试页面的Authorization字段中输入:\n";
        echo "   Bearer $newToken\n";
        echo "\n2. 或者使用URL参数方式:\n";
        echo "   ?token=$newToken\n";
        echo "\n3. 然后测试用户成长API:\n";
        echo "   GET /py-api/user-growth/profile\n";
        echo "\n4. 应该返回200状态码和用户成长数据\n";
        
    } else {
        echo "❌ Token存储失败\n";
    }
    
    $redis->close();
    
} catch (Exception $e) {
    echo "❌ 生成失败: " . $e->getMessage() . "\n";
}

?>
