<?php

/**
 * 初始化用户数据 - 为现有用户设置合理的等级和经验值
 */

require_once 'php/api/vendor/autoload.php';

// 加载环境变量
$dotenv = Dotenv\Dotenv::createImmutable('php/api');
$dotenv->load();

echo "🔄 初始化用户数据...\n\n";

try {
    // 连接数据库
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'tool_api';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    $prefix = $_ENV['DB_PREFIX'] ?? '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $tableName = $prefix . 'users';
    echo "✅ 数据库连接成功\n\n";
    
    // 为所有用户设置初始经验值
    echo "🎯 为用户设置初始数据...\n";
    
    $updateSQL = "UPDATE `$tableName` SET 
        experience = level * 1000
        WHERE experience = 0";
    
    $stmt = $pdo->prepare($updateSQL);
    $stmt->execute();
    $affectedRows = $stmt->rowCount();
    echo "✅ 更新了 $affectedRows 个用户的经验值\n";
    
    // 查看更新后的数据
    echo "\n👥 更新后的用户数据:\n";
    $stmt = $pdo->prepare("SELECT id, username, level, experience FROM `$tableName` LIMIT 5");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "   ID: {$user['id']}, 用户: {$user['username']}, 等级: {$user['level']}, 经验: {$user['experience']}\n";
    }
    
    echo "\n✅ 用户数据初始化完成！\n";
    
} catch (Exception $e) {
    echo "❌ 初始化失败: " . $e->getMessage() . "\n";
}

?>
