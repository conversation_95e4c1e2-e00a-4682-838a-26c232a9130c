<?php
/**
 * AI服务集成模拟返回数据服务 - 配置管理
 *
 * 🚨 架构边界规范：
 * ✅ 本服务仅进行模拟，不会向真实AI平台发起任何网络请求
 * ✅ 严格按照真实AI平台API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实AI生成内容
 *
 * 环境切换：
 * - 本地开发：工具API → AI模拟服务 → 模拟响应（无真实调用）
 * - 生产环境：工具API → 真实AI平台 → 真实响应（真实调用）
 */

// 基础配置 - 支持环境变量
define('AIAPI_VERSION', '1.0.0');
define('AIAPI_DEBUG', getenv('AIAPI_DEBUG') !== false ? (bool)getenv('AIAPI_DEBUG') : true);
define('AIAPI_LOG_LEVEL', getenv('AIAPI_LOG_LEVEL') ?: 'INFO');

// 数据目录 - 支持环境变量
define('AIAPI_DATA_DIR', getenv('AIAPI_DATA_DIR') ?: (__DIR__ . '/../data/'));
define('AIAPI_LOG_DIR', getenv('AIAPI_LOG_DIR') ?: (__DIR__ . '/../logs/'));
define('AIAPI_CACHE_DIR', getenv('AIAPI_CACHE_DIR') ?: (__DIR__ . '/../cache/'));

// 性能配置
define('AIAPI_MAX_REQUEST_SIZE', getenv('AIAPI_MAX_REQUEST_SIZE') ?: (10 * 1024 * 1024)); // 10MB
define('AIAPI_REQUEST_TIMEOUT', getenv('AIAPI_REQUEST_TIMEOUT') ?: 30); // 30秒
define('AIAPI_CACHE_TTL', getenv('AIAPI_CACHE_TTL') ?: 3600); // 1小时

// 创建必要的目录
$directories = [AIAPI_DATA_DIR, AIAPI_LOG_DIR, AIAPI_CACHE_DIR];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// AI平台配置
$aiPlatforms = [
    'deepseek' => [
        'name' => 'DeepSeek',
        'description' => '剧情生成和分镜脚本专家',
        'mock_enabled' => true,
        'real_api' => [
            'base_url' => 'https://api.deepseek.com',
            'beta_url' => 'https://api.deepseek.com/beta',
            'api_key' => '',
            'timeout' => 60,
            'max_retries' => 3
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://aiapi.tiptop.cn',
            'response_delay' => [1000, 3000], // 模拟网络延迟1-3秒（毫秒）
            'success_rate' => 95, // 95%成功率
            'simulate_real_behavior' => true, // 模拟真实API行为
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        'models' => [
            'deepseek-chat' => [
                'context_length' => 128000,
                'max_tokens' => 4096,
                'price_input' => 0.14, // 每1M tokens价格(元)
                'price_output' => 0.28
            ],
            'deepseek-reasoner' => [
                'context_length' => 64000,
                'max_tokens' => 4096,
                'price_input' => 0.55,
                'price_output' => 2.19
            ]
        ]
    ],
    
    'liblib' => [
        'name' => 'LiblibAI',
        'description' => '图像生成专业平台',
        'mock_enabled' => true,
        'real_api' => [
            'base_url' => 'https://api.liblib.art',
            'webui_url' => 'https://www.liblib.art/webui/api',
            'comfyui_url' => 'https://www.liblib.art/comfyui/api',
            'api_key' => '',
            'timeout' => 180,
            'max_retries' => 3
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://aiapi.tiptop.cn',
            'response_delay' => [10000, 30000], // 模拟图像生成延迟10-30秒（毫秒）
            'success_rate' => 90, // 90%成功率
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        'models' => [
            'star-3-alpha' => [
                'type' => 'image_generation',
                'max_resolution' => '2048x2048',
                'formats' => ['jpg', 'png', 'webp']
            ],
            'comfyui-workflow' => [
                'type' => 'workflow',
                'supports' => ['controlnet', 'lora', 'custom_models']
            ]
        ]
    ],
    
    'kling' => [
        'name' => 'KlingAI',
        'description' => '视频生成领导者',
        'mock_enabled' => true,
        'real_api' => [
            'base_url' => 'https://api.klingai.com',
            'auth_url' => 'https://api.klingai.com/v1/auth',
            'api_key' => '',
            'timeout' => 300,
            'max_retries' => 3
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://aiapi.tiptop.cn',
            'response_delay' => [30000, 60000], // 模拟图像生成延迟30-60秒（毫秒）
            'video_delay' => [60000, 300000], // 模拟视频生成延迟1-5分钟（毫秒）
            'success_rate' => 85, // 85%成功率
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        'models' => [
            'kling-v1' => [
                'type' => 'video_generation',
                'max_duration' => 5,
                'resolution' => '1280x720',
                'aspect_ratios' => ['16:9', '9:16', '1:1']
            ],
            'kling-v2-master' => [
                'type' => 'video_generation',
                'max_duration' => 10,
                'resolution' => '1920x1080',
                'aspect_ratios' => ['16:9', '9:16', '1:1']
            ]
        ]
    ],
    
    'minimax' => [
        'name' => 'MiniMax', // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax
        'description' => '多模态AI平台',
        'mock_enabled' => true,
        'real_api' => [
            'base_url' => 'https://api.minimaxi.chat',
            'hailuo_url' => 'https://api.hailuoai.com',
            'api_key' => '',
            'group_id' => '',
            'timeout' => 180,
            'max_retries' => 3
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://aiapi.tiptop.cn',
            'response_delay' => [2000, 8000], // 模拟多模态处理延迟2-8秒（毫秒）
            'success_rate' => 92, // 92%成功率
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        'models' => [
            'minimax-m1' => [
                'type' => 'text_generation',
                'context_length' => 1000000,
                'max_tokens' => 80000
            ],
            'hailuo-02' => [
                'type' => 'video_generation',
                'max_duration' => 10,
                'resolution' => '1920x1080'
            ]
        ]
    ],

    // 火山引擎豆包 - 专业语音AI平台
    'volcengine_doubao' => [
        'name' => '火山引擎豆包',
        'description' => '专业语音AI平台，支持语音合成、音效生成、音色生成',
        'business_types' => ['voice_synthesis', 'sound_effects', 'voice_cloning'],
        'real_api' => [
            'base_url' => 'https://openspeech.bytedance.com',
            'api_key' => env('VOLCENGINE_API_KEY', ''),
            'app_id' => env('VOLCENGINE_APP_ID', ''),
            'timeout' => 60
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://aiapi.tiptop.cn',
            'response_delay' => [3000, 10000], // 模拟语音处理延迟3-10秒（毫秒）
            'success_rate' => 94, // 94%成功率
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        'models' => [
            'tts-v1' => [
                'type' => 'text_to_speech',
                'voices' => ['zh_female_1', 'zh_male_1', 'en_female_1'],
                'formats' => ['mp3', 'wav', 'pcm']
            ],
            'sound-effects-v1' => [
                'type' => 'sound_generation',
                'categories' => ['nature', 'urban', 'music', 'ambient'],
                'duration_range' => [1, 30]
            ],
            'voice-clone-v1' => [
                'type' => 'voice_cloning',
                'min_sample_duration' => 10,
                'supported_languages' => ['zh-CN', 'en-US']
            ]
        ]
    ]
];

// 业务流程配置
$businessFlow = [
    'story_creation' => [
        'platform' => 'deepseek',
        'model' => 'deepseek-chat',
        'description' => '剧情创作和分镜脚本生成'
    ],
    'character_binding' => [
        'platform' => 'database',
        'description' => '角色绑定到分镜'
    ],
    'image_generation' => [
        'platform' => 'liblib', // 或 'kling'
        'model' => 'star-3-alpha',
        'description' => '分镜图片生成'
    ],
    'video_editing' => [
        'platform' => 'kling', // 或 'minimax'
        'model' => 'kling-v2-master',
        'description' => '视频编辑和生成'
    ]
];

// 模拟响应数据配置 - 严格按照真实AI平台格式
$mockResponseData = [
    // DeepSeek 模拟响应数据
    'deepseek' => [
        'story_templates' => [
            '在一个充满科技感的未来城市中，主角发现了一个隐藏的秘密...',
            '古老的森林深处，传说中的神秘生物终于现身...',
            '时间旅行者回到过去，试图改变历史的进程...',
            '外星文明的到来，改变了地球人类的命运...',
            '虚拟现实世界中，现实与幻想的边界变得模糊...'
        ],
        'character_types' => ['勇敢的探险家', '智慧的科学家', '神秘的魔法师', '坚强的战士', '善良的治愈者'],
        'scene_descriptions' => ['壮丽的山脉', '繁华的都市', '神秘的森林', '古老的城堡', '未来的太空站']
    ],

    // LiblibAI 模拟响应数据
    'liblib' => [
        'image_styles' => ['写实风格', '动漫风格', '水彩风格', '油画风格', '素描风格'],
        'image_qualities' => ['高清', '超高清', '4K', '8K'],
        'aspect_ratios' => ['1:1', '16:9', '9:16', '4:3', '3:4'],
        'mock_image_urls' => [
            'https://mock-images.liblib.ai/generated_001.jpg',
            'https://mock-images.liblib.ai/generated_002.jpg',
            'https://mock-images.liblib.ai/generated_003.jpg'
        ]
    ],

    // KlingAI 模拟响应数据
    'kling' => [
        'video_durations' => [5, 10, 15, 30],
        'video_resolutions' => ['720p', '1080p', '4K'],
        'video_styles' => ['真实', '动画', '艺术', '科幻'],
        'mock_video_urls' => [
            'https://mock-videos.kling.ai/generated_001.mp4',
            'https://mock-videos.kling.ai/generated_002.mp4',
            'https://mock-videos.kling.ai/generated_003.mp4'
        ]
    ],

    // MiniMax 模拟响应数据
    'minimax' => [
        'content_types' => ['text', 'image', 'video', 'audio'],
        'languages' => ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'],
        'mock_responses' => [
            'text' => '这是一个由MiniMax生成的模拟文本内容...',
            'image_url' => 'https://mock-content.minimax.ai/image_001.jpg',
            'video_url' => 'https://mock-content.minimax.ai/video_001.mp4',
            'audio_url' => 'https://mock-content.minimax.ai/audio_001.mp3'
        ]
    ],

    // 火山引擎豆包 模拟响应数据
    'volcengine_doubao' => [
        'voice_types' => ['甜美女声', '磁性男声', '童声', '老年声', '机器人声'],
        'audio_formats' => ['mp3', 'wav', 'pcm'],
        'sample_rates' => [16000, 22050, 44100, 48000],
        'mock_audio_urls' => [
            'https://mock-audio.volcengine.com/tts_001.mp3',
            'https://mock-audio.volcengine.com/tts_002.mp3',
            'https://mock-audio.volcengine.com/sound_effect_001.mp3'
        ]
    ]
];

// 响应格式配置 - 统一的API响应格式
$responseFormats = [
    'success' => [
        'status' => 'success',
        'data' => null,
        'message' => '',
        'timestamp' => null,
        'request_id' => null
    ],
    'error' => [
        'status' => 'error',
        'error' => [
            'code' => '',
            'message' => '',
            'details' => null
        ],
        'timestamp' => null,
        'request_id' => null
    ]
];

// AI平台错误码配置 - 模拟真实平台的错误响应
$aiErrorCodes = [
    // 通用错误码
    'SUCCESS' => ['code' => 0, 'message' => '成功'],
    'INVALID_PARAMS' => ['code' => 1001, 'message' => '参数错误'],
    'MISSING_PARAMS' => ['code' => 1002, 'message' => '缺少必需参数'],
    'INVALID_API_KEY' => ['code' => 1003, 'message' => 'API密钥无效'],
    'QUOTA_EXCEEDED' => ['code' => 1004, 'message' => '配额已用完'],
    'RATE_LIMIT_EXCEEDED' => ['code' => 1005, 'message' => '请求频率超限'],
    'SERVICE_UNAVAILABLE' => ['code' => 1006, 'message' => '服务暂时不可用'],

    // DeepSeek 特定错误码
    'DEEPSEEK_CONTENT_FILTERED' => ['code' => 2001, 'message' => '内容被过滤'],
    'DEEPSEEK_MODEL_OVERLOADED' => ['code' => 2002, 'message' => '模型负载过高'],
    'DEEPSEEK_CONTEXT_TOO_LONG' => ['code' => 2003, 'message' => '上下文长度超限'],

    // LiblibAI 特定错误码
    'LIBLIB_IMAGE_GENERATION_FAILED' => ['code' => 3001, 'message' => '图像生成失败'],
    'LIBLIB_STYLE_NOT_SUPPORTED' => ['code' => 3002, 'message' => '不支持的风格'],
    'LIBLIB_RESOLUTION_TOO_HIGH' => ['code' => 3003, 'message' => '分辨率过高'],

    // KlingAI 特定错误码
    'KLING_VIDEO_GENERATION_FAILED' => ['code' => 4001, 'message' => '视频生成失败'],
    'KLING_DURATION_TOO_LONG' => ['code' => 4002, 'message' => '视频时长超限'],
    'KLING_CONTENT_VIOLATION' => ['code' => 4003, 'message' => '内容违规'],

    // MiniMax 特定错误码
    'MINIMAX_MULTIMODAL_ERROR' => ['code' => 5001, 'message' => '多模态处理错误'],
    'MINIMAX_LANGUAGE_NOT_SUPPORTED' => ['code' => 5002, 'message' => '不支持的语言'],
    'MINIMAX_FILE_TOO_LARGE' => ['code' => 5003, 'message' => '文件过大'],

    // 火山引擎豆包 特定错误码
    'VOLCENGINE_TTS_FAILED' => ['code' => 6001, 'message' => '语音合成失败'],
    'VOLCENGINE_VOICE_NOT_FOUND' => ['code' => 6002, 'message' => '音色不存在'],
    'VOLCENGINE_AUDIO_TOO_LONG' => ['code' => 6003, 'message' => '音频时长超限']
];

// 模拟业务状态配置
$mockBusinessStates = [
    'generation_states' => ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    'progress_steps' => [
        'initializing' => '初始化中...',
        'analyzing' => '分析输入内容...',
        'generating' => '生成中...',
        'post_processing' => '后处理中...',
        'finalizing' => '完成中...'
    ],
    'failure_reasons' => [
        'content_filtered' => '内容被过滤',
        'resource_exhausted' => '资源不足',
        'timeout' => '处理超时',
        'invalid_input' => '输入无效',
        'system_error' => '系统错误'
    ]
];

// 导出配置
$GLOBALS['aiapi_config'] = [
    'platforms' => $aiPlatforms,
    'business_flow' => $businessFlow,
    'response_formats' => $responseFormats,
    'mock_response_data' => $mockResponseData,
    'error_codes' => $aiErrorCodes,
    'mock_business_states' => $mockBusinessStates
];

/**
 * 获取配置信息
 */
function getAIConfig($platform = null, $key = null)
{
    $config = $GLOBALS['aiapi_config'];
    
    if ($platform === null) {
        return $config;
    }
    
    if (!isset($config['platforms'][$platform])) {
        return null;
    }
    
    if ($key === null) {
        return $config['platforms'][$platform];
    }
    
    return $config['platforms'][$platform][$key] ?? null;
}

/**
 * 检查平台是否启用模拟模式
 */
function isMockEnabled($platform)
{
    $config = getAIConfig($platform, 'mock_enabled');
    return $config === true;
}

/**
 * 生成请求ID
 */
function generateRequestId()
{
    return 'req_' . uniqid() . '_' . time();
}
