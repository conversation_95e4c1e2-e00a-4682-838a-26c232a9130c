<?php
/**
 * DeepSeek API控制器
 * 
 * 🚨 架构边界规范：
 * ✅ 本控制器仅进行模拟，不会向真实DeepSeek平台发起任何网络请求
 * ✅ 严格按照DeepSeek官方API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实AI生成内容
 * 
 * 业务职责：剧情生成和分镜脚本专家
 * 支持模型：deepseek-chat, deepseek-reasoner
 */

class DeepSeekController
{
    private $logger;
    private $config;
    private $mockData;
    private $errorCodes;
    private $platform = 'deepseek';
    
    public function __construct()
    {
        global $aiapi_config;
        $this->logger = new Logger();
        $this->config = $aiapi_config['platforms'][$this->platform];
        $this->mockData = $aiapi_config['mock_response_data'][$this->platform];
        $this->errorCodes = $aiapi_config['error_codes'];
    }
    
    /**
     * 对话完成接口 - 剧情生成和分镜脚本
     * POST /deepseek/chat/completions
     */
    public function chatCompletions($params = [])
    {
        $startTime = microtime(true);
        
        try {
            // 获取请求数据
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'messages']);
            
            // 验证模型支持
            $validModels = ['deepseek-chat', 'deepseek-reasoner'];
            if (!in_array($data['model'], $validModels)) {
                return HttpHelper::errorResponse(
                    'INVALID_MODEL',
                    '不支持的模型: ' . $data['model'] . '。当前支持的模型：' . implode(', ', $validModels),
                    [
                        'valid_models' => $validModels,
                        'model_info' => [
                            'deepseek-chat' => '通用对话模型，支持创意写作、逻辑推理、代码生成',
                            'deepseek-reasoner' => '推理模型，支持复杂推理、逻辑分析、思维链展示'
                        ]
                    ]
                );
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return HttpHelper::errorResponse(
                    'API_ERROR',
                    '服务暂时不可用，请稍后重试',
                    null,
                    503
                );
            }
            
            // 生成模拟响应
            $response = $this->generateChatResponse($data);
            
            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'chat/completions',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'chat/completions', $e->getMessage(), $data ?? []);
            
            return HttpHelper::errorResponse(
                'PROCESSING_ERROR',
                $e->getMessage()
            );
        }
    }
    
    /**
     * 生成对话完成响应
     */
    private function generateChatResponse($data)
    {
        $model = $data['model'];
        $messages = $data['messages'];

        // 参数支持
        $stream = $data['stream'] ?? false;
        $maxTokens = $data['max_tokens'] ?? 4096;
        $temperature = $data['temperature'] ?? 0.7;
        $topP = $data['top_p'] ?? 0.9;
        $frequencyPenalty = $data['frequency_penalty'] ?? 0;
        $presencePenalty = $data['presence_penalty'] ?? 0;
        $responseFormat = $data['response_format'] ?? ['type' => 'text'];
        $stop = $data['stop'] ?? null;
        $streamOptions = $data['stream_options'] ?? null;
        $tools = $data['tools'] ?? null;
        $toolChoice = $data['tool_choice'] ?? 'auto';
        $logprobs = $data['logprobs'] ?? false;
        $topLogprobs = $data['top_logprobs'] ?? null;

        // 模型兼容性检查
        if ($model === 'deepseek-reasoner') {
            // deepseek-reasoner 不支持某些参数
            if (isset($data['temperature']) || isset($data['top_p']) ||
                isset($data['presence_penalty']) || isset($data['frequency_penalty']) ||
                isset($data['logprobs']) || isset($data['top_logprobs'])) {
                return HttpHelper::errorResponse(
                    'INVALID_PARAMETER',
                    'deepseek-reasoner 模型不支持 temperature、top_p、presence_penalty、frequency_penalty、logprobs、top_logprobs 参数',
                    ['unsupported_params' => ['temperature', 'top_p', 'presence_penalty', 'frequency_penalty', 'logprobs', 'top_logprobs']]
                );
            }
        }

        // 流式响应处理
        if ($stream) {
            return $this->generateStreamResponse($data, $messages, $model, $streamOptions);
        }

        // JSON输出模式处理
        if ($responseFormat['type'] === 'json_object') {
            return $this->generateJsonResponse($data, $messages, $model);
        }

        // Function Calling处理
        if (!empty($tools)) {
            return $this->generateFunctionCallingResponse($data, $messages, $model, $tools, $toolChoice);
        }

        // 🚨 架构边界：模拟业务状态 - 不发起真实请求，仅模拟响应
        $successRate = $this->config['mock_api']['success_rate'];
        $randomValue = rand(1, 100);
        
        if ($randomValue > $successRate) {
            // 模拟失败情况
            return $this->generateErrorResponse($data);
        }
        
        // 模拟成功情况 - 使用配置的模拟数据
        return $this->generateSuccessResponse($data, $messages, $model);
    }
    
    /**
     * 🚨 架构边界：生成成功响应 - 使用模拟数据，不调用真实API
     */
    private function generateSuccessResponse($data, $messages, $model)
    {
        // 从最后一条用户消息中提取内容类型
        $lastUserMessage = '';
        foreach (array_reverse($messages) as $message) {
            if ($message['role'] === 'user') {
                $lastUserMessage = $message['content'];
                break;
            }
        }
        
        // 根据内容类型选择合适的模拟响应
        $responseContent = $this->selectMockContent($lastUserMessage);
        
        // 模拟token使用情况
        $promptTokens = $this->estimateTokens($messages);
        $completionTokens = $this->estimateTokens([$responseContent]);
        
        return [
            'id' => 'chatcmpl-mock-' . uniqid(),
            'object' => 'chat.completion',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $responseContent
                    ],
                    'finish_reason' => 'stop'
                ]
            ],
            'usage' => [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $promptTokens + $completionTokens
            ],
            'system_fingerprint' => 'mock-deepseek-' . date('Ymd')
        ];
    }
    
    /**
     * 🚨 架构边界：生成错误响应 - 模拟真实API错误格式
     */
    private function generateErrorResponse($data)
    {
        // 随机选择一个错误类型
        $errorTypes = [
            'DEEPSEEK_CONTENT_FILTERED',
            'DEEPSEEK_MODEL_OVERLOADED', 
            'DEEPSEEK_CONTEXT_TOO_LONG',
            'QUOTA_EXCEEDED',
            'RATE_LIMIT_EXCEEDED'
        ];
        
        $errorType = $errorTypes[array_rand($errorTypes)];
        $errorInfo = $this->errorCodes[$errorType];
        
        return [
            'error' => [
                'message' => $errorInfo['message'],
                'type' => 'invalid_request_error',
                'param' => null,
                'code' => $errorInfo['code']
            ]
        ];
    }
    
    /**
     * 🚨 架构边界：选择模拟内容 - 基于输入内容类型返回合适的模拟响应
     */
    private function selectMockContent($userInput)
    {
        $input = strtolower($userInput);
        
        // 检测内容类型并返回相应的模拟内容
        if (strpos($input, '剧情') !== false || strpos($input, '故事') !== false) {
            return $this->mockData['story_templates'][array_rand($this->mockData['story_templates'])];
        } elseif (strpos($input, '角色') !== false || strpos($input, '人物') !== false) {
            $character = $this->mockData['character_types'][array_rand($this->mockData['character_types'])];
            return "我为您创建了一个{$character}的角色设定：这是一个充满智慧和勇气的角色，拥有独特的背景故事和鲜明的个性特征...";
        } elseif (strpos($input, '场景') !== false || strpos($input, '背景') !== false) {
            $scene = $this->mockData['scene_descriptions'][array_rand($this->mockData['scene_descriptions'])];
            return "场景描述：{$scene}。这个场景充满了丰富的视觉元素和情感氛围，为故事提供了完美的背景设定...";
        } else {
            // 默认返回通用的AI助手响应
            return "我理解您的需求。作为DeepSeek AI助手，我可以帮您创作剧情、设计角色、描述场景等。请告诉我您具体需要什么类型的内容，我会为您提供专业的创作建议。";
        }
    }
    
    /**
     * 🚨 架构边界：估算token数量 - 模拟真实的token计算
     */
    private function estimateTokens($content)
    {
        if (is_array($content)) {
            $text = '';
            foreach ($content as $item) {
                if (is_array($item) && isset($item['content'])) {
                    $text .= $item['content'];
                } else {
                    $text .= (string)$item;
                }
            }
        } else {
            $text = (string)$content;
        }
        
        // 简化的token估算：中文约1.5字符/token，英文约4字符/token
        $chineseChars = preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text);
        $otherChars = mb_strlen($text) - $chineseChars;
        
        return intval($chineseChars / 1.5 + $otherChars / 4);
    }
}
