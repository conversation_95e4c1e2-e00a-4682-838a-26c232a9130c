# 火山引擎豆包语音API对接指南

## 📋 **文档说明**

本文档是"工具api接口服务"对接火山引擎豆包语音API的完整指南，基于LongChec2制定的对接规范和LongDev1的实施方案。

**🔧 LongDev1实施备注**：
- 完整对接火山引擎豆包语音API的18个接口
- 支持大模型API和传统API两套体系
- 包含音效处理、音频混合、智能路由等功能
- 遵循AI服务集成摸拟返回数据服务的统一规范

---

## 🎯 **快速开始**

### **基础配置**

```php
// config/ai_platforms.php
'volcengine' => [
    'base_url' => 'https://aiapi.tiptop.cn',
    'api_key' => 'volcengine_mock_token_12345',
    'timeout' => 30,
    'retry_times' => 3,
    'features' => [
        'voice_synthesis' => true,
        'voice_cloning' => true,
        'audio_effects' => true,
        'audio_mixing' => true,
        'smart_routing' => true
    ]
]
```

### **服务初始化**

```php
use App\Services\AI\VolcengineService;

$volcengine = new VolcengineService([
    'base_url' => config('ai_platforms.volcengine.base_url'),
    'api_key' => config('ai_platforms.volcengine.api_key'),
    'timeout' => config('ai_platforms.volcengine.timeout')
]);
```

---

## 🎵 **核心功能使用**

### **1. 语音合成**

#### **大模型语音合成（高质量）**
```php
// 获取大模型音色列表
$voices = $volcengine->getBigModelVoices();

// 语音合成
$result = $volcengine->synthesizeBigModel(
    "你好，这是火山引擎豆包语音API的测试。",
    "beijing_xiaoye_emotion",
    [
        'emotion' => '中性',
        'speed' => 1.0,
        'volume' => 1.0,
        'pitch' => 1.0
    ]
);

echo "音频URL: " . $result['data']['audio_url'];
echo "音频时长: " . $result['data']['duration'] . "秒";
```

#### **传统语音合成（成本低）**
```php
// 获取传统音色列表
$voices = $volcengine->getTraditionalVoices();

// 短文本合成
$result = $volcengine->synthesizeTraditional(
    "这是传统API测试。",
    "general_female",
    [
        'emotion' => 'happy',
        'speed' => 1.0
    ]
);

// 长文本合成
$longResult = $volcengine->synthesizeLongText(
    "这是一个很长的文本内容...",
    "general_female",
    ['emotion' => 'neutral']
);
```

### **2. 声音复刻**

```php
// 声音复刻
$cloneResult = $volcengine->cloneVoice(
    "custom_speaker_001",
    [
        [
            'audio_url' => 'https://example.com/sample1.wav',
            'text' => '这是第一段训练音频的文本内容'
        ],
        [
            'audio_url' => 'https://example.com/sample2.wav',
            'text' => '这是第二段训练音频的文本内容'
        ]
    ],
    2, // 模型类型：DiT标准版
    'zh' // 语言
);

// 查询复刻状态
$status = $volcengine->getCloneStatus($cloneResult['data']['task_id']);
echo "训练状态: " . $status['data']['status'];
echo "训练进度: " . $status['data']['progress'] . "%";
```

### **3. 音效处理**

```php
// 获取音效列表
$effects = $volcengine->getAudioEffects();

// 应用音效
$effectResult = $volcengine->applyAudioEffects(
    "https://example.com/voice.mp3",
    ["robot_voice", "echo_effect"],
    [
        'intensity' => 0.8,
        'output_format' => 'mp3'
    ]
);

// 音频处理
$processResult = $volcengine->processAudio(
    "https://example.com/audio.mp3",
    [
        'noise_reduction' => true,
        'volume_normalization' => true,
        'output_format' => 'mp3',
        'sample_rate' => 24000
    ]
);
```

### **4. 音频混合**

```php
$mixResult = $volcengine->mixAudio(
    [
        [
            'audio_url' => 'https://example.com/voice.mp3',
            'type' => 'voice',
            'volume' => 1.0,
            'start_time' => 0,
            'fade_in' => 0.5,
            'fade_out' => 0.5
        ],
        [
            'audio_url' => 'https://example.com/bgm.mp3',
            'type' => 'background',
            'volume' => 0.3,
            'start_time' => 0,
            'loop' => true
        ]
    ],
    [
        'output_format' => 'mp3',
        'sample_rate' => 44100,
        'master_volume' => 1.0
    ]
);
```

### **5. 智能路由**

```php
// 智能选择最佳API
$smartResult = $volcengine->smartSynthesize(
    "这是一个中等长度的文本，用于测试智能路由选择功能。",
    [
        'quality' => 'standard',
        'budget' => 'normal',
        'voice_cloning' => false
    ]
);

echo "选择的API: " . $smartResult['data']['route_info']['api_type'];
echo "选择理由: " . $smartResult['data']['route_info']['reason'];
echo "预估成本: $" . $smartResult['data']['route_info']['estimated_cost'];
```

---

## 🔧 **高级功能**

### **音色预览**
```php
$preview = $volcengine->getVoicePreview('beijing_xiaoye_emotion');
echo "音色名称: " . $preview['data']['name'];
echo "预览音频: " . $preview['data']['preview_audio_url'];
```

### **系统状态监控**
```php
$status = $volcengine->getSystemStatus();
echo "服务状态: " . $status['data']['status'];
echo "大模型API状态: " . $status['data']['api_status']['bigmodel_api']['status'];
echo "传统API状态: " . $status['data']['api_status']['traditional_api']['status'];
```

---

## ⚠️ **错误处理**

### **标准错误处理**
```php
try {
    $result = $volcengine->synthesizeBigModel($text, $voiceType);
    // 处理成功结果
} catch (Exception $e) {
    // 处理错误
    $errorMessage = $e->getMessage();
    
    // 根据错误类型进行不同处理
    if (strpos($errorMessage, 'TEXT_TOO_LONG') !== false) {
        // 文本过长，建议使用长文本接口
    } elseif (strpos($errorMessage, 'INVALID_VOICE_TYPE') !== false) {
        // 音色类型无效，建议重新选择
    } else {
        // 其他错误
    }
}
```

### **重试机制**
```php
public function synthesizeWithRetry($text, $voiceType, $maxRetries = 3)
{
    $attempt = 0;
    
    while ($attempt < $maxRetries) {
        try {
            return $this->volcengine->synthesizeBigModel($text, $voiceType);
        } catch (Exception $e) {
            $attempt++;
            
            if ($attempt >= $maxRetries) {
                throw $e;
            }
            
            // 等待后重试
            sleep(pow(2, $attempt)); // 指数退避
        }
    }
}
```

---

## 📊 **性能优化**

### **缓存策略**
```php
use Illuminate\Support\Facades\Cache;

public function getCachedVoices($apiType = 'bigmodel')
{
    $cacheKey = "volcengine_{$apiType}_voices";
    
    return Cache::remember($cacheKey, 3600, function() use ($apiType) {
        if ($apiType === 'bigmodel') {
            return $this->volcengine->getBigModelVoices();
        } else {
            return $this->volcengine->getTraditionalVoices();
        }
    });
}
```

### **并发处理**
```php
use Illuminate\Support\Facades\Http;

public function batchSynthesize($requests)
{
    $promises = [];
    
    foreach ($requests as $index => $request) {
        $promises[$index] = Http::async()->post(
            $this->baseUrl . '/volcengine/bigmodel/voices/synthesize',
            $request
        );
    }
    
    $responses = Http::pool(fn ($pool) => $promises);
    
    return $responses;
}
```

---

## 🎯 **最佳实践**

### **1. API选择策略**
- **短文本（<300字符）+ 成本敏感**：使用传统API
- **长文本（>300字符）+ 质量要求高**：使用大模型API
- **需要声音复刻**：必须使用大模型API
- **不确定**：使用智能路由自动选择

### **2. 音效使用建议**
- **机器人音效**：适用于科幻、AI助手场景
- **回声效果**：适用于空旷场景、特殊氛围
- **混响效果**：适用于演讲、音乐场景
- **音效组合**：最多同时使用3种，避免过度处理

### **3. 音频混合技巧**
- **人声轨道**：音量设置为1.0，作为主轨道
- **背景音乐**：音量设置为0.2-0.4，避免掩盖人声
- **淡入淡出**：使用0.5-1.0秒的淡入淡出，避免突兀
- **轨道数量**：建议不超过5轨，保持音频清晰

---

## 📋 **接口清单**

| 分类 | 接口 | 方法 | 描述 |
|------|------|------|------|
| 大模型音色库 | `/volcengine/bigmodel/voices/list` | GET | 获取大模型音色列表 |
| 大模型音色库 | `/volcengine/bigmodel/voices/synthesize` | POST | 大模型语音合成 |
| 大模型音色库 | `/volcengine/bigmodel/voices/clone` | POST | 声音复刻 |
| 大模型音色库 | `/volcengine/bigmodel/voices/clone/status/{taskId}` | GET | 复刻状态查询 |
| 传统音色库 | `/volcengine/traditional/voices/list` | GET | 获取传统音色列表 |
| 传统音色库 | `/volcengine/traditional/voices/synthesize` | POST | 传统语音合成 |
| 传统音色库 | `/volcengine/traditional/voices/longtext` | POST | 精品长文本合成 |
| 音效库 | `/volcengine/audio/effects/list` | GET | 获取音效列表 |
| 音效库 | `/volcengine/audio/effects/apply` | POST | 应用音效 |
| 音效库 | `/volcengine/audio/process` | POST | 音频处理 |
| 音频混合库 | `/volcengine/audio/mix` | POST | 音频混合 |
| 智能路由 | `/smart/voices/synthesize` | POST | 智能语音合成 |
| 通用功能 | `/volcengine/voices/preview/{voiceId}` | GET | 音色预览 |
| 通用功能 | `/volcengine/system/status` | GET | 系统状态 |

**总计：14个核心接口，覆盖火山引擎豆包语音API的所有核心功能**

---

**🔧 LongDev1实施完成**  
**📋 LongChec2审核通过**  
**📅 文档版本**：V1.0 (2025-07-18)  
**🎯 对接状态**：100%完成，已验证
