APP_NAME=AI化工具
APP_ENV=production
APP_KEY=
APP_DEBUG=true
APP_URL=https://api.tiptop.cn
APP_TIMEZONE=PRC

SERVER_ID=1

LOG_CHANNEL=stack
LOG_SLACK_WEBHOOK_URL=

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ai_tool
DB_USERNAME=root
DB_PASSWORD=rootroot
DB_PREFIX=p_
DB_ENGINE=InnoDB

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_QUEUE_DB=2

CACHE_DRIVER=redis
CACHE_PREFIX=tp
QUEUE_CONNECTION=redis
QUEUE_REDIS_CONNECTION=queue

TOKEN_RAND_LEN=20

# ==================== 🚨 AI服务环境切换配置 ====================
# mock: 使用模拟服务（开发环境，不产生真实费用）
# real: 使用真实AI平台（生产环境，产生真实费用）
AI_SERVICE_MODE=mock

# 模拟服务配置（开发环境）
AI_MOCK_URL=https://aiapi.tiptop.cn
AI_MOCK_TIMEOUT=30

# 真实服务配置（生产环境）
AI_REAL_TIMEOUT=60

# ==================== 🚨 第三方服务环境切换配置 ====================
# mock: 使用模拟服务（开发环境，不执行真实业务操作）
# real: 使用真实第三方平台（生产环境，执行真实业务操作）
THIRD_PARTY_MODE=mock

# 第三方模拟服务配置（开发环境）
THIRD_PARTY_MOCK_URL=https://thirdapi.tiptop.cn
THIRD_PARTY_MOCK_TIMEOUT=30

# ==================== AI平台真实API配置 ====================
# 🚨 注意：以下配置仅在 AI_SERVICE_MODE=real 时使用

# DeepSeek API配置
DEEPSEEK_API_URL=https://api.deepseek.com
DEEPSEEK_API_KEY=

# LiblibAI API配置
LIBLIB_API_URL=https://openapi.liblibai.cloud
LIBLIB_API_KEY=

# KlingAI API配置
KLING_API_URL=https://api.klingai.com
KLING_API_KEY=

# MiniMax API配置
MINIMAX_API_URL=https://api.minimaxi.chat
MINIMAX_API_KEY=
MINIMAX_GROUP_ID=

# 火山引擎豆包 API配置
VOLCENGINE_API_URL=https://openspeech.bytedance.com
VOLCENGINE_API_KEY=
VOLCENGINE_APP_ID=

# ==================== 第三方平台真实API配置 ====================
# 🚨 注意：以下配置仅在 THIRD_PARTY_MODE=real 时使用

# 微信配置
WECHAT_APP_ID=
WECHAT_APP_SECRET=
WECHAT_MCH_ID=
WECHAT_API_KEY=

# 支付宝配置
ALIPAY_APP_ID=
ALIPAY_PRIVATE_KEY=
ALIPAY_PUBLIC_KEY=

# 阿里云短信配置
ALIYUN_ACCESS_KEY=
ALIYUN_ACCESS_SECRET=
ALIYUN_SMS_SIGN_NAME=

# 腾讯云短信配置
TENCENT_SECRET_ID=
TENCENT_SECRET_KEY=
TENCENT_SMS_APP_ID=

# 邮件配置
MAIL_HOST=
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls

# SendCloud邮件配置
SENDCLOUD_API_USER=
SENDCLOUD_API_KEY=
SENDCLOUD_FROM_EMAIL=
SENDCLOUD_FROM_NAME=

# AI缓存配置
AI_CACHE_ENABLED=true
AI_CACHE_TTL=3600

