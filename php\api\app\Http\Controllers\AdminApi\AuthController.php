<?php

namespace App\Http\Controllers\AdminApi;

use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use App\Services\AdminApi\AuthService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 用户认证与授权管理
 */
class AuthController extends Controller
{
    /**
     * @ApiTitle(注册)
     * @ApiSummary()
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/auth/register)
     * @ApiParams(name="username", type="string", required=true, description="用户帐号")
     * @ApiParams(name="password", type="string", required=true, description="用户密码")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data['token']", type="string", required=true, description="工具平台Token")
     * @ApiReturnParams (name="data['user']", type="object", required=true, description="用户信息")
     * @ApiReturnParams (name="data['user']['id']", type="int", required=true, description="用户ID")
     * @ApiReturn({
        "code": 200,
        "message": "success",
        "data": {
            "token": "Ej2ZeyvnnauWwVI9F5f5NjWpQm83BTJ2ZOAkfgWD4KoJR",
            "user": {
            "id": 10
            }
        }
        })
     */
    public function register(Request $request, AuthService $service)
    {
        try {
            $rules = [
                'username' => 'required|string|min:3|max:50|regex:/^[a-zA-Z0-9_]+$/',
                'password' => 'required|string|min:6|max:100',
                'email' => 'sometimes|email|max:100'
            ];

            $messages = [
                'username.required' => '用户名不能为空',
                'username.min' => '用户名至少需要3个字符',
                'username.max' => '用户名不能超过50个字符',
                'username.regex' => '用户名只能包含字母、数字和下划线',
                'password.required' => '密码不能为空',
                'password.min' => '密码至少需要6个字符',
                'password.max' => '密码不能超过100个字符',
                'email.email' => '邮箱格式不正确',
                'email.max' => '邮箱不能超过100个字符',
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $service->register($request->username, $request->password);

            // 使用统一返回格式
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }
        } catch (\Exception $e) {
            Log::error('注册失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '用户注册失败', []);
        }
    }

    /**
     * @ApiTitle(登录)
     * @ApiSummary()
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/auth/login)
     * @ApiParams(name="username", type="string", required=true, description="用户名")
     * @ApiParams(name="password", type="string", required=true, description="用户密码")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data['token']", type="string", required=true, description="工具平台Token")
     * @ApiReturnParams (name="data['user']", type="object", required=true, description="用户信息")
     * @ApiReturnParams (name="data['user']['id']", type="int", required=true, description="用户ID")
     * @ApiReturn({
        "code": 200,
        "message": "success",
        "data": {
            "token": "Ej2ZeyvnnauWwVI9F5f5NjWpQm83BTJ2ZOAkfgWD4KoJR",
            "user": {
            "id": 10
            }
        }
        })
     */
    public function login(Request $request, AuthService $service)
    {
        try {
            $rules = [
                'username' => 'required|string',
                'password' => 'required|string|min:6'
            ];

            $messages = [
                'username.required' => '用户名不能为空',
                'password.required' => '密码不能为空',
                'password.min' => '密码至少需要6个字符',
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $service->login($request->username, $request->password);

            // 使用统一返回格式
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }
        } catch (\Exception $e) {
            Log::error('登录失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '用户登录失败', []);
        }
    }

    /**
     * @ApiTitle(用户登出)
     * @ApiSummary(用户登出，清除Token)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/auth/logout)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
        "code": 200,
        "message": "登出成功",
        "data": {}
        })
     */
    public function logout(Request $request, AuthService $service)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '请登录后操作');
            }

            $user = $authResult['user'];
            $result = $service->logout($user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }
        } catch (\Exception $e) {
            Log::error('登出失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '用户登出失败', []);
        }
    }

    /**
     * @ApiTitle(忘记密码)
     * @ApiSummary(用户忘记密码，发送重置邮件)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/auth/forgot-password)
     * @ApiParams(name="email", type="string", required=true, description="用户邮箱")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
        "code": 200,
        "message": "密码重置邮件发送成功",
        "data": {}
        })
     */
    public function forgotPassword(Request $request, AuthService $service)
    {
        try {
            $rules = [
                'email' => 'required|email'
            ];

            $messages = [
                'email.required' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $service->forgotPassword($request->email);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }
        } catch (\Exception $e) {
            Log::error('操作失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '忘记密码失败', []);
        }
    }

    /**
     * @ApiTitle(重置密码)
     * @ApiSummary(使用重置令牌重置用户密码)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/auth/reset-password)
     * @ApiParams(name="token", type="string", required=true, description="重置令牌")
     * @ApiParams(name="new_password", type="string", required=true, description="新密码")
     * @ApiParams(name="password_confirmation", type="string", required=true, description="确认密码")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
        "code": 200,
        "message": "密码重置成功",
        "data": {}
        })
     */
    public function resetPassword(Request $request, AuthService $service)
    {
        try {
            $rules = [
                'token' => 'required|string',
                'new_password' => 'required|string|min:6',
                'password_confirmation' => 'required|string|same:new_password'
            ];

            $messages = [
                'token.required' => '重置令牌不能为空',
                'new_password.required' => '新密码不能为空',
                'new_password.min' => '新密码至少需要6个字符',
                'password_confirmation.required' => '确认密码不能为空',
                'password_confirmation.same' => '两次密码输入不一致'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $service->resetPassword($request->token, $request->new_password);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }
        } catch (\Exception $e) {
            Log::error('密码重置失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '密码重置失败', []);
        }
    }

    /**
     * @ApiTitle(Token验证)
     * @ApiSummary(验证用户Token是否有效)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/auth/verify)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="用户信息")
     * @ApiReturn({
        "code": 200,
        "message": "Token验证成功",
        "data": {
            "id": 11
        }
        })
     */
    public function verify(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, 'Token无效或过期');
            }

            $user = $authResult['user'];
            return $this->successResponse(['id' => $user->id], 'Token验证成功');
        } catch (\Exception $e) {
            Log::error('Token验证失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, 'Token验证失败', []);
        }
    }
}
