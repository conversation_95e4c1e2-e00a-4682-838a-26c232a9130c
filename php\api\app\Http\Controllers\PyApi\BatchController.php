<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\PyApi\AuthService;
use App\Services\PyApi\BatchService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 批量操作控制器
 * 处理批量AI生成、批量导出等操作
 */
class BatchController extends Controller
{
    protected $batchService;

    public function __construct(BatchService $batchService)
    {
        $this->batchService = $batchService;
    }

    /**
     * @ApiTitle(批量图像生成)
     * @ApiSummary(批量生成多张图像)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/batch/images/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="prompts", type="array", required=true, description="提示词数组")
     * @ApiParams(name="style", type="string", required=false, description="统一风格")
     * @ApiParams(name="size", type="string", required=false, description="图像尺寸")
     * @ApiParams(name="quality", type="string", required=false, description="图像质量")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123456",
     *     "total_tasks": 5,
     *     "estimated_time": 900,
     *     "estimated_cost": 50,
     *     "task_ids": [101, 102, 103, 104, 105]
     *   }
     * })
     */
    public function generateImages(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'prompts' => 'required|array|min:1|max:20',
                'prompts.*' => 'required|string|min:1|max:2000',
                'style' => 'sometimes|string|max:50',
                'size' => 'sometimes|string|in:512x512,1024x1024,1024x768,768x1024',
                'quality' => 'sometimes|string|in:standard,high,ultra'
            ];

            $messages = [
                'prompts.required' => '提示词数组不能为空',
                'prompts.array' => '提示词必须是数组格式',
                'prompts.min' => '至少需要1个提示词',
                'prompts.max' => '最多支持20个提示词',
                'prompts.*.required' => '提示词不能为空',
                'prompts.*.string' => '提示词必须是字符串',
                'prompts.*.min' => '提示词至少1个字符',
                'prompts.*.max' => '提示词不能超过2000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $batchData = [
                'type' => 'image',
                'prompts' => $request->prompts,
                'parameters' => [
                    'style' => $request->get('style', 'realistic'),
                    'size' => $request->get('size', '1024x1024'),
                    'quality' => $request->get('quality', 'high')
                ]
            ];

            $result = $this->batchService->createBatchTask($user->id, $batchData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量图像生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量图像生成失败');
        }
    }

    /**
     * @ApiTitle(批量语音合成)
     * @ApiSummary(批量合成多段语音)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/batch/voices/synthesize)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="texts", type="array", required=true, description="文本数组")
     * @ApiParams(name="voice_id", type="integer", required=false, description="音色ID")
     * @ApiParams(name="speed", type="number", required=false, description="语速")
     * @ApiParams(name="pitch", type="number", required=false, description="音调")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量语音合成任务创建成功",
     *   "data": {
     *     "batch_id": "batch_voice_123456",
     *     "total_tasks": 3,
     *     "estimated_time": 180,
     *     "estimated_cost": 15,
     *     "task_ids": [201, 202, 203]
     *   }
     * })
     */
    public function synthesizeVoices(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'texts' => 'required|array|min:1|max:10',
                'texts.*' => 'required|string|min:1|max:5000',
                'voice_id' => 'sometimes|integer|exists:voices,id',
                'speed' => 'sometimes|numeric|min:0.5|max:2.0',
                'pitch' => 'sometimes|numeric|min:0.5|max:2.0'
            ];

            $messages = [
                'texts.required' => '文本数组不能为空',
                'texts.array' => '文本必须是数组格式',
                'texts.min' => '至少需要1段文本',
                'texts.max' => '最多支持10段文本',
                'texts.*.required' => '文本不能为空',
                'texts.*.string' => '文本必须是字符串',
                'texts.*.min' => '文本至少1个字符',
                'texts.*.max' => '文本不能超过5000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $batchData = [
                'type' => 'voice',
                'texts' => $request->texts,
                'parameters' => [
                    'voice_id' => $request->get('voice_id', 1),
                    'speed' => $request->get('speed', 1.0),
                    'pitch' => $request->get('pitch', 1.0)
                ]
            ];

            $result = $this->batchService->createBatchTask($user->id, $batchData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量语音合成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量语音合成失败');
        }
    }

    /**
     * @ApiTitle(批量音乐生成)
     * @ApiSummary(批量生成多段音乐)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/batch/music/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="prompts", type="array", required=true, description="音乐描述数组")
     * @ApiParams(name="duration", type="integer", required=false, description="音乐时长(秒)")
     * @ApiParams(name="genre", type="string", required=false, description="音乐风格")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量音乐生成任务创建成功",
     *   "data": {
     *     "batch_id": "batch_music_123456",
     *     "total_tasks": 2,
     *     "estimated_time": 600,
     *     "estimated_cost": 40,
     *     "task_ids": [301, 302]
     *   }
     * })
     */
    public function generateMusic(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'prompts' => 'required|array|min:1|max:5',
                'prompts.*' => 'required|string|min:1|max:1000',
                'duration' => 'sometimes|integer|min:10|max:300',
                'genre' => 'sometimes|string|max:50'
            ];

            $messages = [
                'prompts.required' => '音乐描述数组不能为空',
                'prompts.array' => '音乐描述必须是数组格式',
                'prompts.min' => '至少需要1个音乐描述',
                'prompts.max' => '最多支持5个音乐描述',
                'prompts.*.required' => '音乐描述不能为空',
                'prompts.*.string' => '音乐描述必须是字符串',
                'prompts.*.min' => '音乐描述至少1个字符',
                'prompts.*.max' => '音乐描述不能超过1000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $batchData = [
                'type' => 'music',
                'prompts' => $request->prompts,
                'parameters' => [
                    'duration' => $request->get('duration', 30),
                    'genre' => $request->get('genre', 'ambient')
                ]
            ];

            $result = $this->batchService->createBatchTask($user->id, $batchData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量音乐生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量音乐生成失败');
        }
    }

    /**
     * @ApiTitle(获取批量任务状态)
     * @ApiSummary(查询批量任务的执行状态)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/batch/{batch_id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="batch_id", type="string", required=true, description="批量任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "batch_id": "batch_123456",
     *     "type": "image",
     *     "status": "processing",
     *     "total_tasks": 5,
     *     "completed_tasks": 3,
     *     "failed_tasks": 1,
     *     "pending_tasks": 1,
     *     "progress": 60,
     *     "estimated_remaining_time": 300,
     *     "created_at": "2024-01-01 12:00:00",
     *     "tasks": [
     *       {
     *         "task_id": 101,
     *         "status": "completed",
     *         "result_url": "https://api.tiptop.cn/files/101.jpg"
     *       }
     *     ]
     *   }
     * })
     */
    public function getBatchStatus($batchId, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->batchService->getBatchStatus($batchId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取批量任务状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取批量任务状态失败');
        }
    }

    /**
     * @ApiTitle(取消批量任务)
     * @ApiSummary(取消正在执行的批量任务)
     * @ApiMethod(DELETE)
     * @ApiRoute(/py-api/batch/{batch_id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="batch_id", type="string", required=true, description="批量任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量任务取消成功",
     *   "data": {
     *     "batch_id": "batch_123456",
     *     "cancelled_tasks": 2,
     *     "refund_points": 20
     *   }
     * })
     */
    public function cancelBatch($batchId, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->batchService->cancelBatch($batchId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('取消批量任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '取消批量任务失败');
        }
    }

    /**
     * @ApiTitle(批量资源生成任务)
     * @ApiSummary(创建批量资源生成任务，严格遵循架构铁律仅提供任务管理)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/batch/resources/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="resources", type="array", required=true, description="资源列表，最多100个")
     * @ApiParams(name="resource_type", type="string", required=true, description="资源类型：image/video/audio")
     * @ApiParams(name="batch_size", type="int", required=false, description="批次大小，默认10")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量任务创建成功",
     *   "data": {
     *       "task_id": "batch_64f8a1b2c3d4e",
     *       "status": "pending",
     *       "total_count": 50,
     *       "created_at": "2025-07-30T11:00:00Z"
     *   }
     *   })
     */
    public function generateResources(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '请登录后操作');
            }

            // 参数验证
            $rules = [
                'resources' => 'required|array|min:1|max:100',
                'resource_type' => 'required|string|in:image,video,audio',
                'batch_size' => 'sometimes|integer|min:1|max:50'
            ];

            $messages = [
                'resources.required' => '资源列表不能为空',
                'resources.array' => '资源列表必须是数组格式',
                'resources.min' => '资源列表至少包含1个资源',
                'resources.max' => '资源列表最多包含100个资源',
                'resource_type.required' => '资源类型不能为空',
                'resource_type.in' => '资源类型必须是：image,video,audio之一',
                'batch_size.integer' => '批次大小必须是整数',
                'batch_size.min' => '批次大小最小为1',
                'batch_size.max' => '批次大小最大为50'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 准备任务数据
            $taskData = [
                'user_id' => $authResult['user_id'],
                'resources' => $request->input('resources'),
                'resource_type' => $request->input('resource_type'),
                'batch_size' => $request->input('batch_size', 10)
            ];

            // 调用批量服务创建任务（严格遵循架构铁律）
            $result = $this->batchService->generateResources($taskData);

            if ($result['code'] === 200) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('批处理任务创建失败', [
                'method' => __METHOD__,
                'user_id' => $authResult['user_id'] ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批处理任务创建失败');
        }
    }

    /**
     * @ApiTitle(批量任务状态查询)
     * @ApiSummary(查询批量任务状态、进度和结果，严格遵循架构铁律仅提供状态查询)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/batch/resources/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="task_id", type="string", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务状态信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "获取任务状态成功",
     *   "data": {
     *       "task_id": "batch_64f8a1b2c3d4e",
     *       "status": "processing",
     *       "progress": 75,
     *       "total_count": 50,
     *       "completed_count": 37,
     *       "failed_count": 1,
     *       "created_at": "2025-07-30T11:00:00Z",
     *       "updated_at": "2025-07-30T11:15:00Z"
     *   }
     *   })
     */
    public function getResourcesStatus(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '请登录后操作');
            }

            // 参数验证
            $rules = [
                'task_id' => 'required|string|min:1'
            ];

            $messages = [
                'task_id.required' => '任务ID不能为空',
                'task_id.string' => '任务ID必须是字符串',
                'task_id.min' => '任务ID不能为空'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 获取任务ID参数
            $taskId = $request->input('task_id');

            // 调用批量服务查询任务状态（严格遵循架构铁律）
            $result = $this->batchService->getResourcesStatus($taskId);

            if ($result['code'] === 200) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('批处理任务结果获取失败', [
                'method' => __METHOD__,
                'user_id' => $authResult['user_id'] ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批处理任务结果获取失败');
        }
    }
}
