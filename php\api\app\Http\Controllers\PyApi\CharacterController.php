<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\PyApi\AuthService;
use App\Models\CharacterLibrary;
use App\Models\CharacterCategory;
use App\Services\PyApi\CharacterService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 角色库管理与绑定系统
 */
class CharacterController extends Controller
{
    protected $characterService;

    public function __construct(CharacterService $characterService)
    {
        $this->characterService = $characterService;
    }

    /**
     * @ApiTitle(角色分类列表)
     * @ApiSummary(获取角色分类列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/categories)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="parent_id", type="int", required=false, description="父分类ID")
     * @ApiParams(name="include_children", type="boolean", required=false, description="是否包含子分类")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="array", required=true, description="分类列表")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "动漫角色",
     *       "slug": "anime",
     *       "description": "来自动漫的角色",
     *       "icon": "https://example.com/icon.png",
     *       "character_count": 50,
     *       "children": []
     *     }
     *   ]
     * })
     */
    public function getCategories(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 正确处理parent_id参数类型转换
            $parentId = $request->get('parent_id');
            if ($parentId !== null && $parentId !== '') {
                $parentId = (int) $parentId;
            } else {
                $parentId = null;
            }

            $includeChildren = $request->boolean('include_children', false);

            $result = $this->characterService->getCategories($parentId, $includeChildren);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取角色分类失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色分类失败', []);
        }
    }

    /**
     * @ApiTitle(角色列表)
     * @ApiSummary(获取角色库列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/list)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="category_id", type="int", required=false, description="分类ID")
     * @ApiParams(name="gender", type="string", required=false, description="性别筛选")
     * @ApiParams(name="is_premium", type="boolean", required=false, description="是否高级角色")
     * @ApiParams(name="is_featured", type="boolean", required=false, description="是否推荐角色")
     * @ApiParams(name="keyword", type="string", required=false, description="搜索关键词")
     * @ApiParams(name="tags", type="string", required=false, description="标签筛选，逗号分隔")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "characters": [
     *       {
     *         "id": 1,
     *         "name": "小樱",
     *         "description": "可爱的魔法少女",
     *         "category": "动漫角色",
     *         "gender": "female",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "is_premium": false,
     *         "rating": 4.5,
     *         "binding_count": 100
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 100,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getLibrary(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $filters = [
                'tab' => $request->get('tab', 'public'),
                'gender' => $request->get('gender'),
                'age_range' => $request->get('age_range'),
                'search' => $request->get('search'),
                'is_premium' => $request->get('is_premium'),
                'is_featured' => $request->get('is_featured'),
                'tags' => $request->get('tags') ? explode(',', $request->get('tags')) : null
            ];

            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $result = $this->characterService->getCharacters($filters, $page, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取角色库失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色库失败', []);
        }
    }

    /**
     * @ApiTitle(角色详情)
     * @ApiSummary(获取角色详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="角色ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="角色详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "name": "小樱",
     *     "description": "可爱的魔法少女",
     *     "category": {
     *       "id": 1,
     *       "name": "动漫角色"
     *     },
     *     "gender": "female",
     *     "age_range": "16-18",
     *     "personality": "活泼开朗，善良勇敢",
     *     "background": "来自卡牌捕获者樱的主角",
     *     "appearance": "粉色短发，绿色眼睛",
     *     "avatar": "https://example.com/avatar.jpg",
     *     "images": ["https://example.com/img1.jpg"],
     *     "voice_config": {},
     *     "style_preferences": {},
     *     "tags": ["魔法", "少女", "可爱"],
     *     "rating": 4.5,
     *     "binding_count": 100,
     *     "is_bound": false
     *   }
     * })
     */
    public function getCharacterDetail(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 🚨 CogniDev修复：类型转换问题，确保$id为int类型
            $characterId = (int) $id;
            if ($characterId <= 0) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '无效的角色ID');
            }

            $user = $authResult['user'];
            $result = $this->characterService->getCharacterDetail($characterId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取角色详情失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色详情失败', []);
        }
    }

    /**
     * @ApiTitle(角色生成)
     * @ApiSummary(使用AI生成角色描述)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="角色生成提示词")
     * @ApiParams(name="gender", type="string", required=false, description="性别：male/female/other")
     * @ApiParams(name="age_range", type="string", required=false, description="年龄范围")
     * @ApiParams(name="personality", type="string", required=false, description="性格特征")
     * @ApiParams(name="platform", type="string", required=false, description="指定AI平台：deepseek/minimax")
     * @ApiParams(name="platform_config", type="object", required=false, description="平台特定配置")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.character_info", type="object", required=false, description="生成的角色信息")
     * @ApiReturnParams (name="data.cost", type="decimal", required=false, description="消耗的积分")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "角色生成成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "completed",
     *     "character_info": {
     *       "name": "艾莉丝",
     *       "description": "一个勇敢的冒险家",
     *       "personality": "勇敢、善良、聪明",
     *       "background": "来自魔法世界的少女",
     *       "appearance": "金色长发，蓝色眼睛",
     *       "skills": ["魔法", "剑术", "治疗"],
     *       "relationships": []
     *     },
     *     "cost": "0.0100"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行
            $rules = [
                'prompt' => 'required|string|min:5|max:1000',
                'gender' => 'sometimes|string|in:male,female,other',
                'age_range' => 'sometimes|string|max:20',
                'personality' => 'sometimes|string|max:500',
                'platform' => 'sometimes|string|in:deepseek,minimax',
                'platform_config' => 'sometimes|array'
            ];

            $messages = [
                'prompt.required' => '角色生成提示词不能为空',
                'prompt.min' => '角色生成提示词至少5个字符',
                'prompt.max' => '角色生成提示词不能超过1000个字符',
                'gender.in' => '性别必须是：male、female、other之一',
                'personality.max' => '性格特征不能超过500个字符',
                'platform.in' => 'AI平台必须是：deepseek、minimax之一',
                'platform_config.array' => '平台配置必须是数组格式'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];

            $generationParams = [
                'gender' => $request->get('gender'),
                'age_range' => $request->get('age_range'),
                'personality' => $request->get('personality'),
                'platform' => $request->get('platform', 'deepseek'),
                'platform_config' => $request->get('platform_config', [])
            ];

            $result = $this->characterService->generateCharacter(
                $user->id,
                $request->prompt,
                null, // project_id removed from dev-api-guidelines-add.mdc
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('生成角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '生成角色失败', []);
        }
    }

    /**
     * @ApiTitle(推荐角色)
     * @ApiSummary(获取个性化推荐角色)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/recommendations)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="limit", type="int", required=false, description="推荐数量，默认10")
     * @ApiParams(name="type", type="string", required=false, description="推荐类型：popular,similar,new")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="推荐数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recommendations": [
     *       {
     *         "id": 1,
     *         "name": "小樱",
     *         "description": "可爱的魔法少女",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "rating": 4.5,
     *         "reason": "基于您的偏好推荐"
     *       }
     *     ],
     *     "recommendation_type": "popular"
     *   }
     * })
     */
    public function getRecommendations(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $limit = min($request->get('limit', 10), 50);
            $type = $request->get('type', 'popular');

            $result = $this->characterService->getRecommendations($user->id, $limit, $type);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message'], $result['code']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data'] ?? null);
            }
        } catch (\Exception $e) {
            Log::error('获取角色推荐失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色推荐失败', []);
        }
    }

    /**
     * @ApiTitle(角色绑定)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/bind)
     * @ApiParams({"character_id": "角色ID", "reason": "绑定原因"})
     * @ApiReturn({"code": 200, "message": "角色绑定成功", "data": {"binding_id": 123}})
     */
    public function bindCharacter(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行，先验证基本参数，再检查资源存在性
            $rules = [
                'character_id' => 'required|integer',
                'reason' => 'required|string|max:200'
            ];

            $messages = [
                'character_id.required' => '角色ID不能为空',
                'character_id.integer' => '角色ID必须是整数',
                'reason.required' => '绑定原因不能为空',
                'reason.max' => '绑定原因不能超过200个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];
            $characterId = $request->input('character_id');
            $reason = $request->input('reason');

            // 手动检查角色存在性，确保返回正确的404状态码
            $character = CharacterLibrary::find($characterId);
            if (!$character) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '角色不存在');
            }

            // 调用角色绑定服务
            $characterService = app(\App\Services\CharacterService::class);
            $result = $characterService->bindCharacter($user->id, $characterId, $reason);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('绑定角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '绑定角色失败', []);
        }
    }

    /**
     * @ApiTitle(获取我的角色绑定)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/my-bindings)
     * @ApiReturn({"code": 200, "message": "success", "data": {"bindings": []}})
     */
    public function getMyBindings(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $filters = [
                'is_favorite' => $request->get('is_favorite'),
                'category_id' => $request->get('category_id'),
                'sort' => $request->get('sort', 'usage')
            ];

            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $characterService = app(\App\Services\CharacterService::class);
            $result = $characterService->getUserBindings($user->id, $filters, $page, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取我的角色绑定失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取我的角色绑定失败', []);
        }
    }

    /**
     * @ApiTitle(更新角色绑定)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/characters/bindings/{id})
     * @ApiParams({"id": "绑定ID"})
     * @ApiReturn({"code": 200, "message": "绑定更新成功", "data": {}})
     */
    public function updateBinding(Request $request, $id)
    {
        try {
            $rules = [
                'binding_name' => 'sometimes|string|max:100',
                'custom_description' => 'sometimes|string|max:1000',
                'custom_config' => 'sometimes|array',
                'is_favorite' => 'sometimes|boolean'
            ];

            // 检查是否至少提供了一个更新参数
            $hasUpdateParams = $request->hasAny(['binding_name', 'custom_description', 'custom_config', 'is_favorite']);
            if (!$hasUpdateParams) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '至少需要提供一个更新参数');
            }

            $messages = [
                'binding_name.max' => '绑定名称不能超过100个字符',
                'custom_description.max' => '自定义描述不能超过1000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 手动检查绑定存在性，确保返回正确的404状态码
            $binding = \App\Models\UserCharacterBinding::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$binding) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '绑定不存在');
            }

            $updateData = $request->only([
                'binding_name', 'custom_description', 'custom_config', 'is_favorite'
            ]);

            $characterService = app(\App\Services\CharacterService::class);
            $result = $characterService->updateBinding($id, $user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('更新角色绑定失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新角色绑定失败', []);
        }
    }

    /**
     * @ApiTitle(解绑角色)
     * @ApiMethod(DELETE)
     * @ApiRoute(/py-api/characters/unbind)
     * @ApiParams({"character_id": "角色ID"})
     * @ApiReturn({"code": 200, "message": "角色解绑成功", "data": {"character_id": 1}})
     */
    public function unbindCharacter(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行，先验证基本参数，再检查资源存在性
            $rules = [
                'character_id' => 'required|integer'
            ];

            $messages = [
                'character_id.required' => '角色ID不能为空',
                'character_id.integer' => '角色ID必须是整数'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];
            $characterId = $request->input('character_id');

            // 手动检查角色存在性，确保返回正确的404状态码
            $character = CharacterLibrary::find($characterId);
            if (!$character) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '角色不存在');
            }

            // 调用角色解绑服务 (使用现有的unbindCharacter方法)
            $characterService = app(\App\Services\CharacterService::class);

            // 先查找绑定ID
            $binding = \App\Models\UserCharacterBinding::where('user_id', $user->id)
                ->where('character_id', $characterId)
                ->first();

            if (!$binding) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '绑定不存在');
            }

            $result = $characterService->unbindCharacter($binding->id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('解绑角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '解绑角色失败', []);
        }
    }
}
