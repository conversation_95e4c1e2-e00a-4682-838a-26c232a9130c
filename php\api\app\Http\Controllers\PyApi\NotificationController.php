<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\PyApi\AuthService;
use App\Services\PyApi\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 通知管理控制器
 * 处理系统通知、用户通知、AI任务完成通知等
 */
class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * @ApiTitle(获取用户通知列表)
     * @ApiSummary(查询用户的通知列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/notifications)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=false, description="通知类型：system,task,social,promotion")
     * @ApiParams(name="status", type="string", required=false, description="通知状态：unread,read,all")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="integer", required=false, description="每页数量，默认20")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "notifications": [
     *       {
     *         "id": 1,
     *         "type": "task",
     *         "title": "AI图像生成完成",
     *         "content": "您的图像生成任务已完成，点击查看结果",
     *         "data": {"task_id": 123, "result_url": "https://api.tiptop.cn/files/123.jpg"},
     *         "status": "unread",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "unread_count": 5,
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 50,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function index(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'type' => 'sometimes|string|in:system,task,social,promotion',
                'status' => 'sometimes|string|in:unread,read,all',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'type' => $request->get('type'),
                'status' => $request->get('status', 'all'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->notificationService->getUserNotifications($user->id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取用户通知列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取用户通知列表失败', []);
        }
    }

    /**
     * @ApiTitle(标记通知为已读)
     * @ApiSummary(标记单个或多个通知为已读状态)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/notifications/mark-read)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="notification_ids", type="array", required=true, description="通知ID数组")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "通知已标记为已读",
     *   "data": {
     *     "updated_count": 3,
     *     "remaining_unread": 2
     *   }
     * })
     */
    public function markAsRead(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'notification_ids' => 'required|array|min:1',
                'notification_ids.*' => 'required|integer|exists:notifications,id'
            ];

            $messages = [
                'notification_ids.required' => '通知ID数组不能为空',
                'notification_ids.array' => '通知ID必须是数组格式',
                'notification_ids.min' => '至少需要1个通知ID',
                'notification_ids.*.required' => '通知ID不能为空',
                'notification_ids.*.integer' => '通知ID必须是整数',
                'notification_ids.*.exists' => '通知不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $this->notificationService->markAsRead($user->id, $request->notification_ids);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('标记通知为已读失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '标记通知为已读失败', []);
        }
    }

    /**
     * @ApiTitle(标记所有通知为已读)
     * @ApiSummary(标记用户所有未读通知为已读状态)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/notifications/mark-all-read)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=false, description="通知类型筛选")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "所有通知已标记为已读",
     *   "data": {
     *     "updated_count": 15
     *   }
     * })
     */
    public function markAllAsRead(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'type' => 'sometimes|string|in:system,task,social,promotion'
            ];

            $this->validateData($request->all(), $rules);

            $type = $request->get('type');

            $result = $this->notificationService->markAllAsRead($user->id, $type, []);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('标记所有通知为已读失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '标记所有通知为已读失败', []);
        }
    }

    /**
     * @ApiTitle(删除通知)
     * @ApiSummary(删除指定的通知)
     * @ApiMethod(DELETE)
     * @ApiRoute(/py-api/notifications/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="通知ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "通知删除成功",
     *   "data": {
     *     "deleted_id": 1
     *   }
     * })
     */
    public function destroy($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->notificationService->deleteNotification($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('删除通知失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '删除通知失败', []);
        }
    }

    /**
     * @ApiTitle(获取通知统计)
     * @ApiSummary(获取用户通知的统计信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/notifications/stats)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "total_count": 100,
     *     "unread_count": 15,
     *     "by_type": {
     *       "system": 5,
     *       "task": 8,
     *       "social": 2,
     *       "promotion": 0
     *     },
     *     "recent_notifications": [
     *       {
     *         "id": 1,
     *         "type": "task",
     *         "title": "AI图像生成完成",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ]
     *   }
     * })
     */
    public function stats(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->notificationService->getNotificationStats($user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取通知统计失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取通知统计失败', []);
        }
    }

    /**
     * @ApiTitle(发送系统通知)
     * @ApiSummary(管理员发送系统通知给指定用户)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/notifications/send)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_ids", type="array", required=true, description="用户ID数组，空数组表示发送给所有用户")
     * @ApiParams(name="title", type="string", required=true, description="通知标题")
     * @ApiParams(name="content", type="string", required=true, description="通知内容")
     * @ApiParams(name="type", type="string", required=false, description="通知类型，默认system")
     * @ApiParams(name="data", type="object", required=false, description="附加数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "通知发送成功",
     *   "data": {
     *     "sent_count": 150,
     *     "notification_id": 123
     *   }
     * })
     */
    public function send(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可发送系统通知');
            }

            $rules = [
                'user_ids' => 'required|array',
                'user_ids.*' => 'sometimes|integer|exists:users,id',
                'title' => 'required|string|max:200',
                'content' => 'required|string|max:2000',
                'type' => 'sometimes|string|in:system,task,social,promotion',
                'data' => 'sometimes|array'
            ];

            $messages = [
                'user_ids.required' => '用户ID数组不能为空',
                'user_ids.array' => '用户ID必须是数组格式',
                'title.required' => '通知标题不能为空',
                'title.max' => '通知标题不能超过200个字符',
                'content.required' => '通知内容不能为空',
                'content.max' => '通知内容不能超过2000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $notificationData = [
                'user_ids' => $request->user_ids,
                'title' => $request->title,
                'content' => $request->content,
                'type' => $request->get('type', 'system'),
                'data' => $request->get('data', []),
                'sender_id' => $user->id
            ];

            $result = $this->notificationService->sendNotification($notificationData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('发送系统通知失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '发送系统通知失败', []);
        }
    }
}
