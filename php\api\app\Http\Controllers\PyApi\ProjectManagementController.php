<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\PyApi\AuthService;
use App\Services\PyApi\ProjectManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 项目管理功能控制器
 * 专注于项目管理、协作、统计分析等高级功能
 * 不包含基础的项目CRUD操作（由ProjectController负责）
 */
class ProjectManagementController extends Controller
{
    protected $projectService;

    public function __construct(ProjectManagementService $projectService)
    {
        $this->projectService = $projectService;
    }

    /**
     * @ApiTitle(创建项目管理任务)
     * @ApiSummary(创建项目管理任务，用于项目进度跟踪)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/project-management/tasks)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams(name="task_name", type="string", required=true, description="任务名称")
     * @ApiParams(name="description", type="string", required=false, description="任务描述")
     * @ApiParams(name="priority", type="string", required=false, description="优先级：low/medium/high")
     * @ApiParams(name="assigned_to", type="int", required=false, description="分配给用户ID")
     * @ApiParams(name="due_date", type="string", required=false, description="截止日期")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "项目管理任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "project_id": 456,
     *     "task_name": "完成第一章故事",
     *     "status": "pending",
     *     "priority": "high",
     *     "assigned_to": 789,
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function createTask(Request $request)
    {
        try {
            $rules = [
                'project_id' => 'required|integer|exists:projects,id',
                'task_name' => 'required|string|min:2|max:200',
                'description' => 'sometimes|string|max:1000',
                'priority' => 'sometimes|string|in:low,medium,high',
                'assigned_to' => 'sometimes|integer|exists:users,id',
                'due_date' => 'sometimes|date|after:today'
            ];

            $messages = [
                'project_id.required' => '项目ID不能为空',
                'project_id.exists' => '项目不存在',
                'task_name.required' => '任务名称不能为空',
                'task_name.min' => '任务名称至少2个字符',
                'task_name.max' => '任务名称不能超过200个字符',
                'priority.in' => '优先级必须是：low、medium、high之一',
                'assigned_to.exists' => '分配的用户不存在',
                'due_date.after' => '截止日期必须是未来时间'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $taskData = [
                'project_id' => $request->project_id,
                'task_name' => $request->task_name,
                'description' => $request->get('description'),
                'priority' => $request->get('priority', 'medium'),
                'assigned_to' => $request->get('assigned_to'),
                'due_date' => $request->get('due_date')
            ];

            $result = $this->projectService->createTask($user->id, $taskData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('项目管理任务创建失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '项目管理任务创建失败');
        }
    }

    /**
     * @ApiTitle(项目协作)
     * @ApiSummary(统一的项目协作管理接口)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/project-management/collaborate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams(name="action", type="string", required=true, description="操作类型：invite/remove/update_role")
     * @ApiParams(name="user_id", type="int", required=true, description="目标用户ID")
     * @ApiParams(name="role", type="string", required=false, description="角色：viewer/editor/admin")
     * @ApiParams(name="permissions", type="array", required=false, description="权限数组")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "协作管理操作成功",
     *   "data": {
     *     "project_id": 123,
     *     "action": "invite",
     *     "user_id": 789,
     *     "role": "editor",
     *     "permissions": ["read", "write", "comment"],
     *     "invite_status": "sent",
     *     "updated_at": "2024-01-01 12:30:00"
     *   }
     * })
     */
    public function collaborate(Request $request)
    {
        try {
            $rules = [
                'project_id' => 'required|integer|exists:projects,id',
                'action' => 'required|string|in:invite,remove,update_role',
                'user_id' => 'required|integer|exists:users,id',
                'role' => 'sometimes|string|in:viewer,editor,admin',
                'permissions' => 'sometimes|array'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $collaborationData = [
                'action' => $request->action,
                'user_id' => $request->user_id,
                'role' => $request->get('role', 'viewer'),
                'permissions' => $request->get('permissions', [])
            ];

            $result = $this->projectService->manageCollaboration($request->project_id, $user->id, $collaborationData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('协作管理失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '协作管理失败');
        }
    }

    /**
     * @ApiTitle(获取项目进度)
     * @ApiSummary(获取项目的进度统计信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/project-management/progress)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="project_id", type="int", required=true, description="项目ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "project_id": 123,
     *     "overall_progress": 65.5,
     *     "tasks": {
     *       "total": 20,
     *       "completed": 13,
     *       "in_progress": 5,
     *       "pending": 2
     *     },
     *     "milestones": {
     *       "total": 5,
     *       "completed": 3,
     *       "upcoming": 2
     *     },
     *     "timeline": {
     *       "start_date": "2024-01-01",
     *       "end_date": "2024-03-01",
     *       "current_phase": "development",
     *       "days_remaining": 45
     *     }
     *   }
     * })
     */
    public function getProgress(Request $request)
    {
        try {
            $rules = [
                'project_id' => 'required|integer|exists:projects,id'
            ];
    
            $this->validateData($request->all(), $rules);
    
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }
    
            $user = $authResult['user'];
            $result = $this->projectService->getProjectProgress($request->project_id, $user->id);
    
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取项目进度失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取项目进度失败');
        }
    }

    /**
     * @ApiTitle(分配项目资源)
     * @ApiSummary(为项目分配资源和人员)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/project-management/assign-resources)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams(name="resource_type", type="string", required=true, description="资源类型：human/equipment/budget")
     * @ApiParams(name="resource_data", type="object", required=true, description="资源数据")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "资源分配成功",
     *   "data": {
     *     "project_id": 123,
     *     "resource_type": "human",
     *     "allocated_resources": [
     *       {
     *         "resource_id": 456,
     *         "resource_name": "高级开发者",
     *         "allocation_percentage": 50,
     *         "start_date": "2024-01-01",
     *         "end_date": "2024-02-01"
     *       }
     *     ],
     *     "total_cost": 15000,
     *     "allocated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function assignResources(Request $request)
    {
        try {
            $rules = [
                'project_id' => 'required|integer|exists:projects,id',
                'resource_type' => 'required|string|in:human,equipment,budget',
                'resource_data' => 'required|array'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->projectService->assignResources(
                $request->project_id,
                $user->id,
                $request->resource_type,
                $request->resource_data
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('资源分配失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '资源分配失败');
        }
    }

    /**
     * @ApiTitle(获取项目统计)
     * @ApiSummary(获取项目的统计信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/project-management/statistics)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="project_id", type="int", required=false, description="项目ID，不传则获取用户所有项目统计")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "project_statistics": {
     *       "total_projects": 15,
     *       "active_projects": 8,
     *       "completed_projects": 5,
     *       "archived_projects": 2
     *     },
     *     "task_statistics": {
     *       "total_tasks": 120,
     *       "completed_tasks": 85,
     *       "in_progress_tasks": 25,
     *       "overdue_tasks": 10
     *     },
     *     "resource_statistics": {
     *       "total_budget": 50000,
     *       "used_budget": 32000,
     *       "team_members": 12,
     *       "active_collaborations": 8
     *     }
     *   }
     * })
     */
    public function getStatistics(Request $request)
    {
        try {
            $rules = [
                'project_id' => 'sometimes|integer|exists:projects,id'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->projectService->getProjectStatistics($user->id, $request->get('project_id'));

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取项目统计失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取项目统计失败');
        }
    }

    /**
     * @ApiTitle(项目里程碑)
     * @ApiSummary(获取项目里程碑信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/project-management/milestones)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="project_id", type="int", required=true, description="项目ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "project_id": 123,
     *     "milestones": [
     *       {
     *         "milestone_id": 1,
     *         "title": "项目启动",
     *         "description": "项目正式启动",
     *         "status": "completed",
     *         "due_date": "2024-01-01",
     *         "completed_date": "2024-01-01",
     *         "progress": 100
     *       },
     *       {
     *         "milestone_id": 2,
     *         "title": "第一阶段完成",
     *         "description": "完成第一阶段开发",
     *         "status": "in_progress",
     *         "due_date": "2024-02-01",
     *         "progress": 75
     *       }
     *     ]
     *   }
     * })
     */
    public function getMilestones(Request $request)
    {
        try {
            $rules = [
                'project_id' => 'required|integer|exists:projects,id'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->projectService->getProjectMilestones($request->project_id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取项目里程碑失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取项目里程碑失败');
        }
    }
}
