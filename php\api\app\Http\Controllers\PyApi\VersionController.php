<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\PyApi\AuthService;
use App\Services\PyApi\VersionControlService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 版本控制与历史管理
 */
class VersionController extends Controller
{
    protected $versionService;

    public function __construct(VersionControlService $versionService)
    {
        $this->versionService = $versionService;
    }

    /**
     * @ApiTitle(创建资源版本)
     * @ApiSummary(为指定资源创建新版本)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/resources/{id}/versions)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="资源ID")
     * @ApiParams(name="version_name", type="string", required=false, description="版本名称")
     * @ApiParams(name="description", type="string", required=false, description="版本描述")
     * @ApiParams(name="generation_config", type="object", required=true, description="新版本生成配置")
     * @ApiParams(name="base_version_id", type="int", required=false, description="基于的版本ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "版本创建成功",
     *   "data": {
     *     "version_id": 123,
     *     "resource_id": 456,
     *     "version_number": "v1.2",
     *     "version_name": "优化版本",
     *     "status": "pending",
     *     "generation_task_id": 789,
     *     "estimated_cost": "0.3000",
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function create(Request $request, $id)
    {
        try {
            $rules = [
                'version_name' => 'sometimes|string|max:100',
                'description' => 'sometimes|string|max:500',
                'generation_config' => 'required|array',
                'generation_config.prompt' => 'required|string|min:5|max:2000',
                'base_version_id' => 'sometimes|integer|exists:resource_versions,id'
            ];

            $messages = [
                'generation_config.required' => '生成配置不能为空',
                'generation_config.prompt.required' => '生成提示词不能为空',
                'generation_config.prompt.min' => '生成提示词至少5个字符',
                'generation_config.prompt.max' => '生成提示词不能超过2000个字符',
                'base_version_id.exists' => '基础版本不存在',
                'version_name.max' => '版本名称不能超过100个字符',
                'description.max' => '版本描述不能超过500个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            
            $versionParams = [
                'version_name' => $request->get('version_name'),
                'description' => $request->get('description'),
                'generation_config' => $request->generation_config,
                'base_version_id' => $request->get('base_version_id')
            ];

            $result = $this->versionService->createVersion(
                $user->id,
                $id,
                $versionParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('创建资源版本失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建资源版本失败');
        }
    }

    /**
     * @ApiTitle(获取版本历史)
     * @ApiSummary(获取指定资源的版本历史列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/resources/{id}/versions)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="资源ID")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     * @ApiParams(name="status", type="string", required=false, description="状态过滤")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "resource_id": 456,
     *     "versions": [
     *       {
     *         "version_id": 123,
     *         "version_number": "v1.2",
     *         "version_name": "优化版本",
     *         "description": "基于用户反馈的优化版本",
     *         "status": "completed",
     *         "file_url": "https://aiapi.tiptop.cn/resources/versions/123.json",
     *         "file_size": "2.5MB",
     *         "cost": "0.3000",
     *         "created_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:02:00",
     *         "is_current": true
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 5,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function list(Request $request, $id)
    {
        try {
            $rules = [
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'status' => 'sometimes|string|in:pending,processing,completed,failed'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];
            
            $filters = [
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20),
                'status' => $request->get('status')
            ];

            $result = $this->versionService->getVersionHistory($user->id, $id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取版本历史失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取版本历史失败');
        }
    }

    /**
     * @ApiTitle(获取版本详情)
     * @ApiSummary(获取指定版本的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/versions/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="版本ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "version_id": 123,
     *     "resource_id": 456,
     *     "version_number": "v1.2",
     *     "version_name": "优化版本",
     *     "description": "基于用户反馈的优化版本",
     *     "status": "completed",
     *     "generation_config": {
     *       "prompt": "生成一个关于...",
     *       "style_id": 1,
     *       "quality": "high"
     *     },
     *     "file_info": {
     *       "file_url": "https://aiapi.tiptop.cn/resources/versions/123.json",
     *       "file_size": "2.5MB",
     *       "file_hash": "abc123def456"
     *     },
     *     "cost": "0.3000",
     *     "processing_time_ms": 45000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:02:00",
     *     "base_version": {
     *       "version_id": 122,
     *       "version_number": "v1.1"
     *     }
     *   }
     * })
     */
    public function show(Request $request, $versionId)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->versionService->getVersionDetail($user->id, $versionId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取版本详情失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取版本详情失败');
        }
    }

    /**
     * @ApiTitle(设置当前版本)
     * @ApiSummary(将指定版本设置为资源的当前版本)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/versions/{id}/set-current)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="版本ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "当前版本设置成功",
     *   "data": {
     *     "version_id": 123,
     *     "resource_id": 456,
     *     "version_number": "v1.2",
     *     "is_current": true
     *   }
     * })
     */
    public function setCurrent(Request $request, $versionId)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->versionService->setCurrentVersion($user->id, $versionId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('设置当前版本失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '设置当前版本失败');
        }
    }

    /**
     * @ApiTitle(删除版本)
     * @ApiSummary(删除指定版本（不能删除当前版本）)
     * @ApiMethod(DELETE)
     * @ApiRoute(/py-api/versions/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="版本ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "版本删除成功",
     *   "data": {
     *     "version_id": 123,
     *     "deleted_files": 1,
     *     "freed_space": "2.5MB"
     *   }
     * })
     */
    public function delete(Request $request, $versionId)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->versionService->deleteVersion($user->id, $versionId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('删除版本失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '删除版本失败');
        }
    }

    /**
     * @ApiTitle(版本比较)
     * @ApiSummary(比较两个版本的差异)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/versions/compare)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="version1_id", type="int", required=true, description="版本1 ID")
     * @ApiParams(name="version2_id", type="int", required=true, description="版本2 ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "版本比较完成",
     *   "data": {
     *     "version1": {
     *       "version_id": 122,
     *       "version_number": "v1.1",
     *       "created_at": "2024-01-01 11:00:00"
     *     },
     *     "version2": {
     *       "version_id": 123,
     *       "version_number": "v1.2",
     *       "created_at": "2024-01-01 12:00:00"
     *     },
     *     "differences": {
     *       "config_changes": [
     *         {
     *           "field": "prompt",
     *           "old_value": "原始提示词",
     *           "new_value": "优化后的提示词"
     *         }
     *       ],
     *       "file_changes": {
     *         "size_diff": "+0.5MB",
     *         "quality_improved": true
     *       }
     *     }
     *   }
     * })
     */
    public function compare(Request $request)
    {
        try {
            $rules = [
                'version1_id' => 'required|integer|exists:resource_versions,id',
                'version2_id' => 'required|integer|exists:resource_versions,id'
            ];

            $messages = [
                'version1_id.required' => '版本1 ID不能为空',
                'version1_id.exists' => '版本1不存在',
                'version2_id.required' => '版本2 ID不能为空',
                'version2_id.exists' => '版本2不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            
            $result = $this->versionService->compareVersions(
                $user->id,
                $request->version1_id,
                $request->version2_id
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('版本比较失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '版本比较失败');
        }
    }
}
