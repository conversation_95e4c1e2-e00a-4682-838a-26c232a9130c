<?php

namespace App\Http\Controllers;

use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;

/**
 * API文档控制器
 * 负责生成和管理API文档，支持动态加载和静态生成
 */
class WebApiDocumentController extends Controller
{
    /**
     * 获取所有API数据（完整数据，用于静态生成）
     */
    public function getApiData()
    {
        $data = $this->parseAllControllers();
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => '获取API数据成功'
        ]);
    }

    /**
     * 获取控制器列表（仅导航信息，用于动态加载）
     */
    public function getControllerList()
    {
        $list = $this->parseControllerList();
        return response()->json([
            'success' => true,
            'data' => $list,
            'message' => '获取控制器列表成功'
        ]);
    }

    /**
     * 获取单个控制器数据（按需加载）
     */
    public function getControllerData($name)
    {
        try {
            $data = $this->parseControllerByName($name);

            if ($data === null) {
                // 添加调试信息
                $fileName = $name . (str_ends_with($name, '.php') ? '' : '.php');
                $possiblePaths = [
                    base_path('app/Http/Controllers/WebApi/' . $fileName),
                    app()->basePath('app/Http/Controllers/WebApi/' . $fileName),
                    '../app/Http/Controllers/WebApi/' . $fileName,
                    __DIR__ . '/../WebApi/' . $fileName
                ];

                $pathStatus = [];
                foreach ($possiblePaths as $path) {
                    $pathStatus[$path] = file_exists($path) ? 'exists' : 'not_found';
                }

                return response()->json([
                    'success' => false,
                    'data' => null,
                    'message' => '控制器不存在',
                    'debug' => [
                        'controller_name' => $name,
                        'attempted_paths' => $pathStatus,
                        'base_path' => base_path(),
                        'current_dir' => getcwd(),
                        'controller_dir' => __DIR__
                    ]
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => '获取控制器数据成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'data' => null,
                'message' => '解析控制器时发生错误: ' . $e->getMessage(),
                'debug' => [
                    'controller_name' => $name,
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'base_path' => base_path(),
                    'current_dir' => getcwd()
                ]
            ], 500);
        }
    }

    /**
     * 创建API文档HTML文件（保持向后兼容）
     */
    public function createApiHtmlFile()
    {
        // 使用多种路径尝试，兼容不同的运行环境
        $possibleDirs = [
            base_path('app/Http/Controllers/WebApi'),
            app()->basePath('app/Http/Controllers/WebApi'),
            '../app/Http/Controllers/WebApi',
            __DIR__ . '/../WebApi'
        ];

        $dirPath = null;
        foreach ($possibleDirs as $dir) {
            if (is_dir($dir)) {
                $dirPath = $dir;
                break;
            }
        }

        if (!$dirPath) {
            return response()->json([
                'success' => false,
                'message' => '控制器目录不存在'
            ], 500);
        }

        $files = scandir($dirPath);
        $arr = [];
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                // 使用多种路径尝试，兼容不同的运行环境
                $possiblePaths = [
                    base_path('app/Http/Controllers/WebApi/' . $file),
                    app()->basePath('app/Http/Controllers/WebApi/' . $file),
                    '../app/Http/Controllers/WebApi/' . $file,
                    __DIR__ . '/../WebApi/' . $file
                ];

                $filePath = null;
                foreach ($possiblePaths as $path) {
                    if (file_exists($path)) {
                        $filePath = $path;
                        break;
                    }
                }

                if (!$filePath) {
                    continue;
                }

                $str = file_get_contents($filePath);
                // 支持单行和多行注释格式
                if (preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\s*([^\n]*)\s*\n\s*\*\/\s*class/", $str, $matches)) {
                    // 多行注释格式
                } else {
                    // 尝试单行注释格式
                    preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\/\s*class/", $str, $matches);
                    if ($matches) {
                        $matches[2] = ''; // 第二行为空
                    }
                }
                if ($matches) {
                    $item = [];
                    $title = isset($matches[1]) ? trim($matches[1]) : '';
                    $subtitle = isset($matches[2]) ? trim($matches[2]) : '';
                    $item['title'] = $title . ($subtitle ? ' ' . $subtitle : '');

                    preg_match_all("/\@(ApiTitle|ApiSummary|ApiMethod|ApiRoute|ApiHeaders|ApiParams|ApiReturnParams|ApiReturn)[^\@^\*]+/", $str, $matches);

                    if (!empty($matches[0])) {
                        $list = [];
                        $apiArr = [];
                        $headerArr = [];
                        $paramArr = [];
                        $returnParamArr = [];

                        foreach ($matches[0] as $value) {
                            if (strpos($value, '@ApiTitle') !== false) {
                                // 保存上一个API的数据
                                if (!empty($apiArr)) {
                                    $apiArr['headers'] = $headerArr;
                                    $apiArr['params'] = $paramArr;
                                    $apiArr['return_params'] = $returnParamArr;
                                    $list[] = $apiArr;
                                }

                                // 重置数组
                                $apiArr = [];
                                $headerArr = [];
                                $paramArr = [];
                                $returnParamArr = [];
                            }

                            // 解析各种API注释
                            $this->parseApiAnnotation($value, $apiArr, $headerArr, $paramArr, $returnParamArr);
                        }

                        // 保存最后一个API的数据
                        if (!empty($apiArr)) {
                            $apiArr['headers'] = $headerArr;
                            $apiArr['params'] = $paramArr;
                            $apiArr['return_params'] = $returnParamArr;
                            $list[] = $apiArr;
                        }

                        $item['list'] = $list;
                    }
                    $arr[] = $item;
                }
            }
        }

        // 生成HTML内容（这里只保留核心逻辑，完整实现会很长）
        $this->generateHtmlFile($arr);
    }

    /**
     * 检查数组是否为关联数组
     */
    private function is_assoc($array)
    {
        return count(array_filter(array_keys($array), 'is_string')) == count($array);
    }

    /**
     * 解析所有控制器信息
     * 提取的核心解析逻辑，支持数据复用
     */
    private function parseAllControllers()
    {
        // 使用多种路径尝试，兼容不同的运行环境
        $possibleDirs = [
            base_path('app/Http/Controllers/WebApi'),
            app()->basePath('app/Http/Controllers/WebApi'),
            '../app/Http/Controllers/WebApi',
            __DIR__ . '/../WebApi'
        ];

        $dirPath = null;
        foreach ($possibleDirs as $dir) {
            if (is_dir($dir)) {
                $dirPath = $dir;
                break;
            }
        }

        if (!$dirPath) {
            return [];
        }

        $files = scandir($dirPath);
        $arr = [];

        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $controllerData = $this->parseControllerFile($file);
                if (!empty($controllerData)) {
                    $arr[] = $controllerData;
                }
            }
        }

        return $arr;
    }

    /**
     * 根据控制器名称解析单个控制器
     */
    private function parseControllerByName($controllerName)
    {
        // 确保文件名格式正确
        $fileName = $controllerName;
        if (!str_ends_with($fileName, '.php')) {
            $fileName .= '.php';
        }

        // 使用多种路径尝试，兼容不同的运行环境
        $possiblePaths = [
            base_path('app/Http/Controllers/WebApi/' . $fileName),
            app()->basePath('app/Http/Controllers/WebApi/' . $fileName),
            '../app/Http/Controllers/WebApi/' . $fileName,
            __DIR__ . '/../WebApi/' . $fileName
        ];

        $filePath = null;
        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                $filePath = $path;
                break;
            }
        }

        if (!$filePath) {
            return null;
        }

        return $this->parseControllerFile($fileName);
    }

    /**
     * 获取控制器列表（仅导航信息）
     */
    private function parseControllerList()
    {
        // 使用多种路径尝试，兼容不同的运行环境
        $possibleDirs = [
            base_path('app/Http/Controllers/WebApi'),
            app()->basePath('app/Http/Controllers/WebApi'),
            '../app/Http/Controllers/WebApi',
            __DIR__ . '/../WebApi'
        ];

        $dirPath = null;
        foreach ($possibleDirs as $dir) {
            if (is_dir($dir)) {
                $dirPath = $dir;
                break;
            }
        }

        if (!$dirPath) {
            return [];
        }

        $files = scandir($dirPath);
        $list = [];

        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                // 使用多种路径尝试，兼容不同的运行环境
                $possiblePaths = [
                    base_path('app/Http/Controllers/WebApi/' . $file),
                    app()->basePath('app/Http/Controllers/WebApi/' . $file),
                    '../app/Http/Controllers/WebApi/' . $file,
                    __DIR__ . '/../WebApi/' . $file
                ];

                $filePath = null;
                foreach ($possiblePaths as $path) {
                    if (file_exists($path)) {
                        $filePath = $path;
                        break;
                    }
                }

                if (!$filePath) {
                    continue;
                }

                $str = file_get_contents($filePath);

                // 解析类注释获取控制器标题
                if (preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\s*([^\n]*)\s*\n\s*\*\/\s*class/", $str, $matches)) {
                    // 多行注释格式
                } else {
                    // 尝试单行注释格式
                    preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\/\s*class/", $str, $matches);
                    if ($matches) {
                        $matches[2] = ''; // 第二行为空
                    }
                }

                if ($matches) {
                    $title = isset($matches[1]) ? trim($matches[1]) : '';
                    $subtitle = isset($matches[2]) ? trim($matches[2]) : '';
                    $fullTitle = $title . ($subtitle ? ' ' . $subtitle : '');

                    // 统计接口数量
                    preg_match_all("/\@ApiTitle[^\@^\*]+/", $str, $apiMatches);
                    $apiCount = count($apiMatches[0]);

                    $list[] = [
                        'file' => $file,
                        'name' => str_replace('.php', '', $file),
                        'title' => $fullTitle,
                        'api_count' => $apiCount
                    ];
                }
            }
        }

        return $list;
    }

    /**
     * 解析单个控制器文件
     */
    private function parseControllerFile($file)
    {
        // 使用多种路径尝试，兼容不同的运行环境
        $possiblePaths = [
            base_path('app/Http/Controllers/WebApi/' . $file),
            app()->basePath('app/Http/Controllers/WebApi/' . $file),
            '../app/Http/Controllers/WebApi/' . $file,
            __DIR__ . '/../WebApi/' . $file
        ];

        $filePath = null;
        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                $filePath = $path;
                break;
            }
        }

        if (!$filePath) {
            return null;
        }

        $str = file_get_contents($filePath);

        // 解析类注释获取控制器标题
        if (preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\s*([^\n]*)\s*\n\s*\*\/\s*class/", $str, $matches)) {
            // 多行注释格式
        } else {
            // 尝试单行注释格式
            preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\/\s*class/", $str, $matches);
            if ($matches) {
                $matches[2] = ''; // 第二行为空
            }
        }

        if (!$matches) {
            return null;
        }

        $item = [];
        $title = isset($matches[1]) ? trim($matches[1]) : '';
        $subtitle = isset($matches[2]) ? trim($matches[2]) : '';
        $item['title'] = $title . ($subtitle ? ' ' . $subtitle : '');
        $item['file'] = $file;
        $item['name'] = str_replace('.php', '', $file);

        // 解析API注释
        preg_match_all("/\@(ApiTitle|ApiSummary|ApiMethod|ApiRoute|ApiHeaders|ApiParams|ApiReturnParams|ApiReturn)[^\@^\*]+/", $str, $matches);

        if (!empty($matches[0])) {
            $list = [];
            $apiArr = [];
            $headerArr = [];
            $paramArr = [];
            $returnParamArr = [];

            foreach ($matches[0] as $value) {
                if (strpos($value, '@ApiTitle') !== false) {
                    // 保存上一个API的数据
                    if (!empty($apiArr)) {
                        $apiArr['headers'] = $headerArr;
                        $apiArr['params'] = $paramArr;
                        $apiArr['return_params'] = $returnParamArr;
                        $list[] = $apiArr;
                    }

                    // 重置数组
                    $apiArr = [];
                    $headerArr = [];
                    $paramArr = [];
                    $returnParamArr = [];
                }

                // 解析各种API注释
                $this->parseApiAnnotation($value, $apiArr, $headerArr, $paramArr, $returnParamArr);
            }

            // 保存最后一个API的数据
            if (!empty($apiArr)) {
                $apiArr['headers'] = $headerArr;
                $apiArr['params'] = $paramArr;
                $apiArr['return_params'] = $returnParamArr;
                $list[] = $apiArr;
            }

            $item['list'] = $list;
        }

        return $item;
    }

    /**
     * 解析API注释
     */
    private function parseApiAnnotation($value, &$apiArr, &$headerArr, &$paramArr, &$returnParamArr)
    {
        // 先清理整个匹配内容
        $value = $this->cleanAnnotationContent($value);

        if (strpos($value, '@ApiTitle') !== false) {
            $content = trim(str_replace('@ApiTitle', '', $value));
            $apiArr['title'] = $this->extractParenthesesContent($content);
        } elseif (strpos($value, '@ApiSummary') !== false) {
            $content = trim(str_replace('@ApiSummary', '', $value));
            $apiArr['summary'] = $this->extractParenthesesContent($content);
        } elseif (strpos($value, '@ApiMethod') !== false) {
            $content = trim(str_replace('@ApiMethod', '', $value));
            $apiArr['method'] = $this->extractParenthesesContent($content);
        } elseif (strpos($value, '@ApiRoute') !== false) {
            $content = trim(str_replace('@ApiRoute', '', $value));
            $apiArr['route'] = $this->extractParenthesesContent($content);
        } elseif (strpos($value, '@ApiHeaders') !== false) {
            $headerStr = trim(str_replace('@ApiHeaders', '', $value));
            $headerArr[] = $this->parseParamString($headerStr);
        } elseif (strpos($value, '@ApiParams') !== false) {
            $paramStr = trim(str_replace('@ApiParams', '', $value));
            $paramArr[] = $this->parseParamString($paramStr);
        } elseif (strpos($value, '@ApiReturnParams') !== false) {
            $returnParamStr = trim(str_replace('@ApiReturnParams', '', $value));
            $returnParamArr[] = $this->parseParamString($returnParamStr);
        } elseif (strpos($value, '@ApiReturn') !== false) {
            $content = trim(str_replace('@ApiReturn', '', $value));
            $apiArr['return'] = $this->parseReturnJson($content);
        }
    }

    /**
     * 解析参数字符串
     */
    private function parseParamString($paramStr)
    {
        // 清理括号
        $paramStr = $this->cleanParentheses($paramStr);

        // 解析格式：name="value", type="value", required=value, description="value"
        $result = [
            'name' => '',
            'type' => '',
            'required' => '',
            'description' => ''
        ];

        // 使用正则表达式解析参数
        if (preg_match('/name="([^"]*)"/', $paramStr, $matches)) {
            $result['name'] = $matches[1];
        }
        if (preg_match('/type="([^"]*)"/', $paramStr, $matches)) {
            $result['type'] = $matches[1];
        }
        if (preg_match('/required=([^,\s]*)/', $paramStr, $matches)) {
            $result['required'] = $matches[1];
        }
        if (preg_match('/description="([^"]*)"/', $paramStr, $matches)) {
            $result['description'] = $matches[1];
        }

        // 特殊处理：如果字段名是 code，添加所有状态码说明
        if ($result['name'] === 'code') {
            $result['description'] = $this->buildCodeDescription($result['description']);
        }

        return $result;
    }

    /**
     * 清理注释内容
     */
    private function cleanAnnotationContent($content)
    {
        // 清理注释符号和换行
        $content = preg_replace('/\*\s*/', '', $content);
        $content = preg_replace('/\s*\n\s*/', ' ', $content);
        return trim($content);
    }

    /**
     * 提取括号内容
     */
    private function extractParenthesesContent($content)
    {
        // 匹配括号内的内容
        if (preg_match('/\s*\(\s*(.*?)\s*\)\s*$/', $content, $matches)) {
            return trim($matches[1]);
        }
        return trim($content);
    }

    /**
     * 清理括号和注释符号（保留向后兼容）
     */
    private function cleanParentheses($content)
    {
        return $this->extractParenthesesContent($content);
    }

    /**
     * 解析返回JSON
     */
    private function parseReturnJson($content)
    {
        // 清理注释符号和多余的空白
        $content = preg_replace('/\*\s*/', '', $content);
        $content = trim($content);

        // 如果内容以括号包围，去掉外层括号
        if (preg_match('/^\s*\(\s*(.*)\s*\)\s*$/s', $content, $matches)) {
            $content = $matches[1];
        }

        // 清理并格式化JSON
        $content = trim($content);

        return $content;
    }

    /**
     * 构建状态码描述
     */
    private function buildCodeDescription($originalDescription)
    {
        // 如果原描述存在，添加分隔符
        if ($originalDescription) {
            $description = $originalDescription . '，';
        } else {
            $description = '状态码。';
        }

        // 获取所有状态码并构建描述
        $codeDescriptions = [];
        foreach (ApiCodeEnum::getMap() as $code => $label) {
            $codeDescriptions[] = $code . '：' . $label;
        }

        $description .= implode('，', $codeDescriptions);

        return $description;
    }

    /**
     * 生成HTML文件（简化版本，保持向后兼容）
     */
    private function generateHtmlFile($data)
    {
        // 这里可以实现HTML生成逻辑
        // 为了保持向后兼容，可以调用原有的HTML生成逻辑
        // 或者重定向到新版本
        echo '<script>window.location.href = "/py-api.html";</script>';
    }
}
