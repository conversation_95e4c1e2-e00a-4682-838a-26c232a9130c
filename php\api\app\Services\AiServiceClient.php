<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

/**
 * AI服务客户端
 * 
 * 🚨 架构边界规范：环境切换机制实现
 * ✅ 本地开发：调用模拟服务（无真实费用）
 * ✅ 生产环境：调用真实AI平台（产生真实费用）
 * 
 * 环境切换通过 AI_SERVICE_MODE 环境变量控制
 */
class AiServiceClient
{
    /**
     * 调用AI服务
     * 
     * @param string $platform 平台名称 (deepseek, liblib, kling, minimax, volcengine)
     * @param array $data 请求数据
     * @param array $options 额外选项
     * @return array
     */
    public static function call($platform, $data, $options = [])
    {
        $serviceMode = Config::get('ai.service_mode', 'mock');
        
        Log::info("AI服务调用", [
            'platform' => $platform,
            'mode' => $serviceMode,
            'data_size' => strlen(json_encode($data))
        ]);
        
        if ($serviceMode === 'mock') {
            return self::callMockService($platform, $data, $options);
        } else {
            return self::callRealService($platform, $data, $options);
        }
    }
    
    /**
     * 调用模拟服务
     * 
     * @param string $platform
     * @param array $data
     * @param array $options
     * @return array
     */
    private static function callMockService($platform, $data, $options = [])
    {
        $platformConfig = Config::get("ai.platforms.{$platform}");
        if (!$platformConfig) {
            throw new \Exception("不支持的AI平台: {$platform}");
        }
        
        $mockConfig = Config::get('ai.mock_service');
        $url = $mockConfig['base_url'] . $platformConfig['mock_endpoint'];
        $timeout = $options['timeout'] ?? $mockConfig['timeout'];
        
        Log::info("调用模拟服务", [
            'platform' => $platform,
            'url' => $url,
            'timeout' => $timeout
        ]);
        
        try {
            $response = Http::timeout($timeout)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'AI-Tool-API/1.0',
                ])
                ->post($url, $data);
            
            if ($response->successful()) {
                $result = $response->json();
                
                Log::info("模拟服务调用成功", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'response_size' => strlen($response->body())
                ]);
                
                return [
                    'success' => true,
                    'data' => $result,
                    'mode' => 'mock',
                    'platform' => $platform
                ];
            } else {
                Log::error("模拟服务调用失败", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'error' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'error' => '模拟服务调用失败',
                    'status' => $response->status(),
                    'mode' => 'mock',
                    'platform' => $platform
                ];
            }
        } catch (\Exception $e) {
            Log::error("模拟服务调用异常", [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => '模拟服务调用异常: ' . $e->getMessage(),
                'mode' => 'mock',
                'platform' => $platform
            ];
        }
    }
    
    /**
     * 调用真实服务
     * 
     * @param string $platform
     * @param array $data
     * @param array $options
     * @return array
     */
    private static function callRealService($platform, $data, $options = [])
    {
        $platformConfig = Config::get("ai.platforms.{$platform}");
        if (!$platformConfig || !isset($platformConfig['real_api'])) {
            throw new \Exception("平台 {$platform} 的真实API配置不存在");
        }
        
        $realApi = $platformConfig['real_api'];
        $url = $realApi['base_url'] . $realApi['endpoint'];
        $timeout = $options['timeout'] ?? Config::get('ai.real_service.timeout', 60);
        
        // 检查API密钥
        if (empty($realApi['api_key'])) {
            throw new \Exception("平台 {$platform} 的API密钥未配置");
        }
        
        Log::warning("调用真实AI服务", [
            'platform' => $platform,
            'url' => $url,
            'timeout' => $timeout,
            'warning' => '这将产生真实费用'
        ]);
        
        try {
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $realApi['api_key'],
                'User-Agent' => 'AI-Tool-API/1.0',
            ];
            
            // 特殊平台的额外头部
            if ($platform === 'minimax' && !empty($realApi['group_id'])) {
                $headers['GroupId'] = $realApi['group_id'];
            }
            
            $response = Http::timeout($timeout)
                ->withHeaders($headers)
                ->post($url, $data);
            
            if ($response->successful()) {
                $result = $response->json();
                
                Log::info("真实AI服务调用成功", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'response_size' => strlen($response->body())
                ]);
                
                return [
                    'success' => true,
                    'data' => $result,
                    'mode' => 'real',
                    'platform' => $platform
                ];
            } else {
                Log::error("真实AI服务调用失败", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'error' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'error' => '真实AI服务调用失败',
                    'status' => $response->status(),
                    'mode' => 'real',
                    'platform' => $platform
                ];
            }
        } catch (\Exception $e) {
            Log::error("真实AI服务调用异常", [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => '真实AI服务调用异常: ' . $e->getMessage(),
                'mode' => 'real',
                'platform' => $platform
            ];
        }
    }
    
    /**
     * 获取当前服务模式
     * 
     * @return string mock|real
     */
    public static function getServiceMode()
    {
        return Config::get('ai.service_mode', 'mock');
    }
    
    /**
     * 检查是否为模拟模式
     * 
     * @return bool
     */
    public static function isMockMode()
    {
        return self::getServiceMode() === 'mock';
    }
    
    /**
     * 获取支持的平台列表
     * 
     * @return array
     */
    public static function getSupportedPlatforms()
    {
        return array_keys(Config::get('ai.platforms', []));
    }
    
    /**
     * 验证平台配置
     * 
     * @param string $platform
     * @return array
     */
    public static function validatePlatformConfig($platform)
    {
        $config = Config::get("ai.platforms.{$platform}");
        if (!$config) {
            return ['valid' => false, 'error' => '平台不存在'];
        }
        
        $serviceMode = self::getServiceMode();
        
        if ($serviceMode === 'real') {
            if (!isset($config['real_api']) || empty($config['real_api']['api_key'])) {
                return ['valid' => false, 'error' => '真实API配置不完整'];
            }
        }
        
        return ['valid' => true, 'mode' => $serviceMode];
    }
}
