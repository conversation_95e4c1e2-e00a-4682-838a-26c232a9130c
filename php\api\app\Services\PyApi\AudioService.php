<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * 音频处理服务
 * 第2D3阶段：音频处理模块
 */
class AudioService
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * 音频混音
     */
    public function mixAudio(int $userId, array $audioUrls, ?int $projectId = null, array $mixConfig = []): array
    {
        try {
            DB::beginTransaction();

            // 计算预估成本
            $estimatedCost = $this->calculateMixCost(count($audioUrls));

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'audio_mix',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建处理任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => null,
                'task_type' => 'audio_mix',
                'platform' => 'internal',
                'model_name' => 'audio_mixer',
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'audio_urls' => $audioUrls,
                    'mix_config' => $mixConfig
                ],
                'generation_params' => $mixConfig,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行混音任务
            $this->executeAudioMix($task);

            Log::info('音频混音任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'audio_count' => count($audioUrls),
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音频混音任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'audio_urls_count' => is_array($audioUrls) ? count($audioUrls) : 0,
                'project_id' => $projectId,
                'mix_config_keys' => is_array($mixConfig) ? array_keys($mixConfig) : [],
            ];

            Log::error('音频混音失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音频混音失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取音频混音状态
     */
    public function getAudioMixStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', 'audio_mix')
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['audio_url'] = $task->output_data['audio_url'] ?? '';
                $data['duration'] = $task->output_data['duration'] ?? 0;
                $data['file_size'] = $task->output_data['file_size'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取音频混音状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取音频混音状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 音频增强
     */
    public function enhanceAudio(int $userId, string $audioUrl, ?int $projectId = null, array $enhanceConfig = []): array
    {
        try {
            DB::beginTransaction();

            // 计算预估成本
            $estimatedCost = $this->calculateEnhanceCost();

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'audio_enhance',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建处理任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => null,
                'task_type' => 'audio_enhance',
                'platform' => 'internal',
                'model_name' => 'audio_enhancer',
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'audio_url' => $audioUrl,
                    'enhance_config' => $enhanceConfig
                ],
                'generation_params' => $enhanceConfig,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行增强任务
            $this->executeAudioEnhance($task);

            Log::info('音频增强任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音频增强任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'audio_url' => $audioUrl,
                'project_id' => $projectId,
                'enhance_config_keys' => is_array($enhanceConfig) ? array_keys($enhanceConfig) : [],
            ];

            Log::error('音频增强失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音频增强失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取音频增强状态
     */
    public function getAudioEnhanceStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', 'audio_enhance')
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['audio_url'] = $task->output_data['audio_url'] ?? '';
                $data['duration'] = $task->output_data['duration'] ?? 0;
                $data['file_size'] = $task->output_data['file_size'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取音频增强状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取音频增强状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 计算混音成本
     */
    private function calculateMixCost(int $audioCount): float
    {
        return round(0.01 * $audioCount, 4); // 每个音频文件0.01积分
    }

    /**
     * 计算增强成本
     */
    private function calculateEnhanceCost(): float
    {
        return 0.08; // 固定成本
    }

    /**
     * 执行音频混音
     */
    private function executeAudioMix(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 模拟音频混音处理
            sleep(2); // 模拟处理时间

            $task->update([
                'status' => AiGenerationTask::STATUS_COMPLETED,
                'output_data' => [
                    'audio_url' => 'https://aiapi.tiptop.cn/audio/mixed/' . $task->id . '.mp3',
                    'duration' => 120,
                    'file_size' => '5.2MB'
                ],
                'completed_at' => Carbon::now(),
                'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
            ]);

            // 确认积分消费
            $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'audio_mix', $task->id);

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'audio_mix_error', $task->id);

            Log::error('音频混音执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 执行音频增强
     */
    private function executeAudioEnhance(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 模拟音频增强处理
            sleep(1); // 模拟处理时间

            $task->update([
                'status' => AiGenerationTask::STATUS_COMPLETED,
                'output_data' => [
                    'audio_url' => 'https://aiapi.tiptop.cn/audio/enhanced/' . $task->id . '.mp3',
                    'duration' => 60,
                    'file_size' => '2.8MB'
                ],
                'completed_at' => Carbon::now(),
                'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
            ]);

            // 确认积分消费
            $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'audio_enhance', $task->id);

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'audio_enhance_error', $task->id);

            Log::error('音频增强执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
