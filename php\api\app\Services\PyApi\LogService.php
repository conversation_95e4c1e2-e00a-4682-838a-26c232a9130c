<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

/**
 * 日志服务类
 * 负责日志的记录、查询、分析和管理
 */
class LogService
{
    private $logChannels = ['single', 'daily', 'slack', 'syslog', 'errorlog'];
    private $logLevels = ['emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug'];
    
    /**
     * 记录日志
     */
    public function writeLog($level, $message, $context = [], $channel = null)
    {
        try {
            if (!in_array($level, $this->logLevels)) {
                throw new \InvalidArgumentException("无效的日志级别: {$level}");
            }
            
            $logData = [
                'timestamp' => now(),
                'level' => $level,
                'message' => $message,
                'context' => $context,
                'user_id' => auth()->id(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'url' => request()->fullUrl(),
                'method' => request()->method()
            ];
            
            if ($channel) {
                Log::channel($channel)->{$level}($message, $logData);
            } else {
                Log::{$level}($message, $logData);
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志记录成功',
                'data' => [
                    'log_id' => uniqid('log_'),
                    'level' => $level,
                    'channel' => $channel ?: 'default',
                    'logged_at' => $logData['timestamp']
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'level' => $level,
                'message' => substr($message, 0, 100),
                'channel' => $channel,
                'context_keys' => is_array($context) ? array_keys($context) : [],
            ];

            Log::error('日志记录失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志记录失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取日志列表
     */
    public function getLogs($params = [])
    {
        try {
            $logPath = storage_path('logs');
            $files = File::files($logPath);
            
            $logs = [];
            foreach ($files as $file) {
                $fileName = $file->getFilename();
                $filePath = $file->getPathname();
                
                // 过滤日志文件
                if (!str_ends_with($fileName, '.log')) {
                    continue;
                }
                
                $logs[] = [
                    'name' => $fileName,
                    'path' => $filePath,
                    'size' => $file->getSize(),
                    'size_human' => $this->formatBytes($file->getSize()),
                    'modified_at' => Carbon::createFromTimestamp($file->getMTime()),
                    'created_at' => Carbon::createFromTimestamp($file->getCTime()),
                    'is_readable' => $file->isReadable(),
                    'is_writable' => $file->isWritable()
                ];
            }
            
            // 排序
            $sortBy = $params['sort_by'] ?? 'modified_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            
            usort($logs, function ($a, $b) use ($sortBy, $sortOrder) {
                $result = $a[$sortBy] <=> $b[$sortBy];
                return $sortOrder === 'desc' ? -$result : $result;
            });
            
            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;
            $offset = ($page - 1) * $perPage;
            $paginatedLogs = array_slice($logs, $offset, $perPage);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志列表获取成功',
                'data' => [
                    'logs' => $paginatedLogs,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => count($logs),
                        'total_pages' => ceil(count($logs) / $perPage)
                    ],
                    'summary' => [
                        'total_files' => count($logs),
                        'total_size' => array_sum(array_column($logs, 'size')),
                        'total_size_human' => $this->formatBytes(array_sum(array_column($logs, 'size')))
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'params' => $params,
            ];

            Log::error('日志列表获取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志列表获取失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 读取日志内容
     */
    public function readLog($fileName, $params = [])
    {
        try {
            $logPath = storage_path('logs/' . $fileName);
            
            if (!File::exists($logPath)) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '日志文件不存在',
                    'data' => null
                ];
            }
            
            $lines = $params['lines'] ?? 100;
            $search = $params['search'] ?? null;
            $level = $params['level'] ?? null;
            $fromTail = $params['from_tail'] ?? true;
            
            $content = File::get($logPath);
            $logLines = explode("\n", $content);
            
            // 过滤空行
            $logLines = array_filter($logLines, function ($line) {
                return !empty(trim($line));
            });
            
            // 搜索过滤
            if ($search) {
                $logLines = array_filter($logLines, function ($line) use ($search) {
                    return stripos($line, $search) !== false;
                });
            }
            
            // 级别过滤
            if ($level) {
                $logLines = array_filter($logLines, function ($line) use ($level) {
                    return stripos($line, strtoupper($level)) !== false;
                });
            }
            
            // 获取指定行数
            if ($fromTail) {
                $logLines = array_slice($logLines, -$lines);
            } else {
                $logLines = array_slice($logLines, 0, $lines);
            }
            
            // 解析日志行
            $parsedLogs = [];
            foreach ($logLines as $index => $line) {
                $parsedLogs[] = $this->parseLogLine($line, $index + 1);
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志内容读取成功',
                'data' => [
                    'file_name' => $fileName,
                    'file_size' => File::size($logPath),
                    'file_size_human' => $this->formatBytes(File::size($logPath)),
                    'total_lines' => count(explode("\n", $content)),
                    'filtered_lines' => count($parsedLogs),
                    'logs' => $parsedLogs,
                    'filters' => [
                        'search' => $search,
                        'level' => $level,
                        'lines' => $lines,
                        'from_tail' => $fromTail
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'fileName' => $fileName,
                'params' => $params,
            ];

            Log::error('日志内容读取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志内容读取失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 分析日志统计
     */
    public function analyzeLog($fileName = null)
    {
        try {
            $logPath = storage_path('logs');
            $files = $fileName ? [$fileName] : array_map(function ($file) {
                return $file->getFilename();
            }, File::files($logPath));
            
            $analysis = [
                'total_entries' => 0,
                'level_stats' => [],
                'hourly_stats' => [],
                'daily_stats' => [],
                'error_patterns' => [],
                'top_ips' => [],
                'top_urls' => [],
                'response_times' => []
            ];
            
            foreach ($files as $file) {
                if (!str_ends_with($file, '.log')) {
                    continue;
                }
                
                $filePath = storage_path('logs/' . $file);
                if (!File::exists($filePath)) {
                    continue;
                }
                
                $content = File::get($filePath);
                $lines = explode("\n", $content);
                
                foreach ($lines as $line) {
                    if (empty(trim($line))) {
                        continue;
                    }
                    
                    $analysis['total_entries']++;
                    $this->analyzeLogLine($line, $analysis);
                }
            }
            
            // 排序统计数据
            arsort($analysis['level_stats']);
            arsort($analysis['top_ips']);
            arsort($analysis['top_urls']);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志分析完成',
                'data' => [
                    'analyzed_files' => $files,
                    'analysis' => $analysis,
                    'analyzed_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'fileName' => $fileName,
            ];

            Log::error('日志分析失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志分析失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 清理日志
     */
    public function cleanLogs($params = [])
    {
        try {
            $logPath = storage_path('logs');
            $daysToKeep = $params['days_to_keep'] ?? 30;
            $maxSizeMB = $params['max_size_mb'] ?? 100;
            $dryRun = $params['dry_run'] ?? false;
            
            $files = File::files($logPath);
            $deletedFiles = [];
            $totalSizeFreed = 0;
            
            foreach ($files as $file) {
                $fileName = $file->getFilename();
                $filePath = $file->getPathname();
                
                if (!str_ends_with($fileName, '.log')) {
                    continue;
                }
                
                $shouldDelete = false;
                $reason = '';
                
                // 检查文件年龄
                $fileAge = Carbon::createFromTimestamp($file->getMTime());
                if ($fileAge->diffInDays(now()) > $daysToKeep) {
                    $shouldDelete = true;
                    $reason = "文件超过 {$daysToKeep} 天";
                }
                
                // 检查文件大小
                $fileSizeMB = $file->getSize() / 1024 / 1024;
                if ($fileSizeMB > $maxSizeMB) {
                    $shouldDelete = true;
                    $reason = "文件大小超过 {$maxSizeMB}MB";
                }
                
                if ($shouldDelete) {
                    $deletedFiles[] = [
                        'name' => $fileName,
                        'size' => $file->getSize(),
                        'size_human' => $this->formatBytes($file->getSize()),
                        'modified_at' => $fileAge,
                        'reason' => $reason
                    ];
                    
                    $totalSizeFreed += $file->getSize();
                    
                    if (!$dryRun) {
                        File::delete($filePath);
                    }
                }
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => $dryRun ? '日志清理预览完成' : '日志清理完成',
                'data' => [
                    'dry_run' => $dryRun,
                    'deleted_files' => $deletedFiles,
                    'total_files_deleted' => count($deletedFiles),
                    'total_size_freed' => $totalSizeFreed,
                    'total_size_freed_human' => $this->formatBytes($totalSizeFreed),
                    'criteria' => [
                        'days_to_keep' => $daysToKeep,
                        'max_size_mb' => $maxSizeMB
                    ],
                    'cleaned_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'params' => $params,
            ];

            Log::error('日志清理失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志清理失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 下载日志文件
     */
    public function downloadLog($fileName)
    {
        try {
            $logPath = storage_path('logs/' . $fileName);
            
            if (!File::exists($logPath)) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '日志文件不存在',
                    'data' => null
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志文件准备下载',
                'data' => [
                    'file_name' => $fileName,
                    'file_path' => $logPath,
                    'file_size' => File::size($logPath),
                    'file_size_human' => $this->formatBytes(File::size($logPath)),
                    'mime_type' => 'text/plain',
                    'download_url' => route('api.logs.download', ['fileName' => $fileName])
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'fileName' => $fileName,
            ];

            Log::error('日志下载准备失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志下载准备失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取日志配置
     */
    public function getLogConfig()
    {
        try {
            $config = config('logging');
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志配置获取成功',
                'data' => [
                    'default_channel' => $config['default'],
                    'deprecations_channel' => $config['deprecations'] ?? null,
                    'channels' => array_keys($config['channels']),
                    'available_levels' => $this->logLevels,
                    'log_path' => storage_path('logs'),
                    'config' => $config
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [];

            Log::error('日志配置获取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志配置获取失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 解析日志行
     */
    private function parseLogLine($line, $lineNumber)
    {
        // Laravel 日志格式: [2023-01-01 12:00:00] local.ERROR: Message {"context":"data"}
        $pattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] (\w+)\.(\w+): (.+)/';
        
        if (preg_match($pattern, $line, $matches)) {
            return [
                'line_number' => $lineNumber,
                'timestamp' => $matches[1],
                'environment' => $matches[2],
                'level' => strtolower($matches[3]),
                'message' => trim($matches[4]),
                'raw_line' => $line
            ];
        }
        
        return [
            'line_number' => $lineNumber,
            'timestamp' => null,
            'environment' => null,
            'level' => 'unknown',
            'message' => $line,
            'raw_line' => $line
        ];
    }
    
    /**
     * 分析日志行
     */
    private function analyzeLogLine($line, &$analysis)
    {
        $parsed = $this->parseLogLine($line, 0);
        
        // 统计级别
        $level = $parsed['level'];
        if (!isset($analysis['level_stats'][$level])) {
            $analysis['level_stats'][$level] = 0;
        }
        $analysis['level_stats'][$level]++;
        
        // 统计时间分布
        if ($parsed['timestamp']) {
            $timestamp = Carbon::parse($parsed['timestamp']);
            $hour = $timestamp->format('H');
            $date = $timestamp->format('Y-m-d');
            
            if (!isset($analysis['hourly_stats'][$hour])) {
                $analysis['hourly_stats'][$hour] = 0;
            }
            $analysis['hourly_stats'][$hour]++;
            
            if (!isset($analysis['daily_stats'][$date])) {
                $analysis['daily_stats'][$date] = 0;
            }
            $analysis['daily_stats'][$date]++;
        }
        
        // 提取IP地址
        if (preg_match('/\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/', $line, $ipMatches)) {
            $ip = $ipMatches[0];
            if (!isset($analysis['top_ips'][$ip])) {
                $analysis['top_ips'][$ip] = 0;
            }
            $analysis['top_ips'][$ip]++;
        }
        
        // 提取URL
        if (preg_match('/"[A-Z]+ ([^"\s]+)/', $line, $urlMatches)) {
            $url = $urlMatches[1];
            if (!isset($analysis['top_urls'][$url])) {
                $analysis['top_urls'][$url] = 0;
            }
            $analysis['top_urls'][$url]++;
        }
    }
    
    /**
     * 获取系统日志
     */
    public function getSystemLogs($filters)
    {
        try {
            // 模拟系统日志数据
            $logs = [
                [
                    'id' => 1,
                    'level' => 'error',
                    'component' => 'ai',
                    'message' => 'AI服务调用失败：连接超时',
                    'context' => [
                        'platform' => 'liblib',
                        'task_id' => 123,
                        'error_code' => 'TIMEOUT'
                    ],
                    'ip' => '*************',
                    'user_agent' => 'TipTop-API/1.0',
                    'created_at' => '2024-01-01 12:00:00'
                ],
                [
                    'id' => 2,
                    'level' => 'info',
                    'component' => 'api',
                    'message' => '用户登录成功',
                    'context' => [
                        'user_id' => 456,
                        'login_method' => 'password'
                    ],
                    'ip' => '*************',
                    'user_agent' => 'Mozilla/5.0...',
                    'created_at' => '2024-01-01 11:30:00'
                ]
            ];

            // 应用过滤器
            if ($filters['level']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['level'] === $filters['level'];
                });
            }

            if ($filters['component']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['component'] === $filters['component'];
                });
            }

            if ($filters['keyword']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return stripos($log['message'], $filters['keyword']) !== false;
                });
            }

            $total = count($logs);
            $perPage = $filters['per_page'] ?? 50;
            $page = $filters['page'] ?? 1;
            $lastPage = ceil($total / $perPage);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '系统日志获取成功',
                'data' => [
                    'logs' => array_values($logs),
                    'summary' => [
                        'total' => $total,
                        'by_level' => [
                            'error' => 15,
                            'warning' => 45,
                            'info' => 890,
                            'debug' => 300
                        ]
                    ],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
            ];

            Log::error('系统日志获取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '系统日志获取失败：',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户操作日志
     */
    public function getUserActionLogs($filters)
    {
        try {
            // 模拟用户操作日志数据
            $logs = [
                [
                    'id' => 1,
                    'user_id' => 123,
                    'username' => 'testuser',
                    'action' => 'generate',
                    'resource' => 'image',
                    'description' => '生成AI图像',
                    'details' => [
                        'prompt' => '一只可爱的小猫',
                        'style' => 'cartoon',
                        'platform' => 'liblib'
                    ],
                    'ip' => '*************',
                    'user_agent' => 'Mozilla/5.0...',
                    'status' => 'success',
                    'created_at' => '2024-01-01 12:00:00'
                ]
            ];

            // 应用过滤器
            if ($filters['user_id']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['user_id'] == $filters['user_id'];
                });
            }

            if ($filters['action']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['action'] === $filters['action'];
                });
            }

            $total = count($logs);
            $perPage = $filters['per_page'] ?? 50;
            $page = $filters['page'] ?? 1;
            $lastPage = ceil($total / $perPage);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '用户操作日志获取成功',
                'data' => [
                    'logs' => array_values($logs),
                    'summary' => [
                        'total' => $total,
                        'by_action' => [
                            'login' => 890,
                            'generate' => 3200,
                            'upload' => 850,
                            'download' => 480
                        ]
                    ],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
            ];

            Log::error('用户操作日志获取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户操作日志获取失败：',
                'data' => null
            ];
        }
    }

    /**
     * 获取AI调用日志
     */
    public function getAiCallLogs($filters)
    {
        try {
            // 模拟AI调用日志数据
            $logs = [
                [
                    'id' => 1,
                    'platform' => 'liblib',
                    'type' => 'image',
                    'task_id' => 123,
                    'user_id' => 456,
                    'request_data' => [
                        'prompt' => '一只可爱的小猫',
                        'style' => 'cartoon'
                    ],
                    'response_data' => [
                        'image_url' => 'https://api.tiptop.cn/files/123.jpg',
                        'generation_time' => 45
                    ],
                    'status' => 'success',
                    'response_time' => 2500,
                    'cost' => 10,
                    'error_message' => null,
                    'created_at' => '2024-01-01 12:00:00'
                ]
            ];

            // 应用过滤器
            if ($filters['platform']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['platform'] === $filters['platform'];
                });
            }

            if ($filters['type']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['type'] === $filters['type'];
                });
            }

            if ($filters['status']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['status'] === $filters['status'];
                });
            }

            $total = count($logs);
            $perPage = $filters['per_page'] ?? 50;
            $page = $filters['page'] ?? 1;
            $lastPage = ceil($total / $perPage);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'AI调用日志获取成功',
                'data' => [
                    'logs' => array_values($logs),
                    'summary' => [
                        'total' => $total,
                        'success_rate' => 96.5,
                        'average_response_time' => 2800,
                        'by_platform' => [
                            'liblib' => ['calls' => 3200, 'success_rate' => 98.1],
                            'deepseek' => ['calls' => 2100, 'success_rate' => 97.8]
                        ]
                    ],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
            ];

            Log::error('AI调用日志获取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'AI调用日志获取失败：',
                'data' => null
            ];
        }
    }

    /**
     * 获取错误日志
     */
    public function getErrorLogs($filters)
    {
        try {
            // 模拟错误日志数据
            $logs = [
                [
                    'id' => 1,
                    'level' => 'error',
                    'component' => 'ai',
                    'message' => 'AI服务调用失败',
                    'exception' => 'ConnectionTimeoutException',
                    'stack_trace' => 'Stack trace...',
                    'context' => [
                        'platform' => 'liblib',
                        'task_id' => 123
                    ],
                    'count' => 5,
                    'first_seen' => '2024-01-01 12:00:00',
                    'last_seen' => '2024-01-01 12:30:00',
                    'resolved' => false,
                    'resolved_at' => null,
                    'resolved_by' => null
                ]
            ];

            // 应用过滤器
            if ($filters['level']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['level'] === $filters['level'];
                });
            }

            if ($filters['component']) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['component'] === $filters['component'];
                });
            }

            if (isset($filters['resolved'])) {
                $logs = array_filter($logs, function($log) use ($filters) {
                    return $log['resolved'] === $filters['resolved'];
                });
            }

            $total = count($logs);
            $perPage = $filters['per_page'] ?? 50;
            $page = $filters['page'] ?? 1;
            $lastPage = ceil($total / $perPage);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '错误日志获取成功',
                'data' => [
                    'logs' => array_values($logs),
                    'summary' => [
                        'total' => $total,
                        'unresolved' => 23,
                        'by_component' => [
                            'ai' => 45,
                            'database' => 12,
                            'api' => 89,
                            'cache' => 10
                        ]
                    ],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
            ];

            Log::error('错误日志获取失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '错误日志获取失败：',
                'data' => null
            ];
        }
    }

    /**
     * 解决错误
     */
    public function resolveError($errorId, $userId, $solution)
    {
        try {
            // 模拟解决错误逻辑
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '错误已标记为解决',
                'data' => [
                    'error_id' => $errorId,
                    'resolved' => true,
                    'resolved_by' => 'admin',
                    'resolved_at' => now(),
                    'solution' => $solution
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'errorId' => $errorId,
                'userId' => $userId,
                'solution' => $solution,
            ];

            Log::error('错误解决失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '错误解决失败：',
                'data' => null
            ];
        }
    }

    /**
     * 导出日志
     */
    public function exportLogs($exportData)
    {
        try {
            $exportId = 'export_' . uniqid();
            
            // 模拟导出任务创建
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志导出任务创建成功',
                'data' => [
                    'export_id' => $exportId,
                    'estimated_time' => 120,
                    'download_url' => null,
                    'status' => 'processing',
                    'type' => $exportData['type'],
                    'format' => $exportData['format'],
                    'created_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $error_context = [
                'exportData' => $exportData,
            ];

            Log::error('日志导出失败：', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志导出失败：',
                'data' => null
            ];
        }
    }

    /**
     * 格式化字节大小
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}