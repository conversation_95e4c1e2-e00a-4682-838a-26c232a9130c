<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\Resource;
use App\Models\Project;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * 资源管理服务
 * 第3A阶段：资源生成管理模块
 */
class ResourceManagementService
{
    protected $pointsService;
    protected $storyService;
    protected $imageService;
    protected $voiceService;
    protected $videoService;
    protected $musicService;
    protected $soundService;

    public function __construct(
        PointsService $pointsService,
        StoryService $storyService,
        ImageService $imageService,
        VoiceService $voiceService,
        VideoService $videoService,
        MusicService $musicService,
        SoundService $soundService
    ) {
        $this->pointsService = $pointsService;
        $this->storyService = $storyService;
        $this->imageService = $imageService;
        $this->voiceService = $voiceService;
        $this->videoService = $videoService;
        $this->musicService = $musicService;
        $this->soundService = $soundService;
    }

    /**
     * 创建资源生成任务
     */
    public function createGenerationTask(int $userId, int $projectId, array $generationParams): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where('user_id', $userId)
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权限访问',
                    'data' => []
                ];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateGenerationCost($generationParams);

            // 检查积分余额
            $businessType = $this->getBusinessTypeByResourceType($generationParams['resource_type']);
            $checkResult = $this->pointsService->checkPoints(
                $userId,
                $estimatedCost,
                $businessType
            );

            if ($checkResult['code'] !== ApiCodeEnum::SUCCESS || !$checkResult['data']['sufficient']) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => $checkResult['data']
                ];
            }

            // 创建资源记录
            $resource = Resource::create([
                'resource_uuid' => Str::uuid(),
                'user_id' => $userId,
                'project_id' => $projectId,
                'resource_type' => $generationParams['resource_type'],
                'status' => Resource::STATUS_PENDING,
                'generation_config' => $generationParams['generation_config'],
                'output_format' => $generationParams['output_format'],
                'quality_level' => $generationParams['quality_level'],
                'batch_size' => $generationParams['batch_size'],
                'estimated_cost' => $estimatedCost,
                'metadata' => [
                    'created_by' => 'resource_management_service',
                    'version' => '1.0'
                ]
            ]);

            // 根据资源类型调用相应的生成服务
            $generationResult = $this->delegateGeneration($resource, $generationParams);

            if ($generationResult['code'] !== ApiCodeEnum::SUCCESS) {
                DB::rollBack();
                return $generationResult;
            }

            // 更新资源记录
            $resource->update([
                'generation_task_id' => $generationResult['data']['task_id'],
                'status' => Resource::STATUS_PROCESSING
            ]);

            DB::commit();

            Log::info('资源生成任务创建成功', [
                'resource_id' => $resource->id,
                'user_id' => $userId,
                'project_id' => $projectId,
                'resource_type' => $generationParams['resource_type'],
                'estimated_cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '资源生成任务创建成功',
                'data' => [
                    'resource_id' => $resource->id,
                    'generation_task_id' => $generationResult['data']['task_id'],
                    'status' => $resource->status,
                    'estimated_cost' => $estimatedCost,
                    'estimated_duration' => $this->getEstimatedDuration($generationParams),
                    'resource_type' => $generationParams['resource_type'],
                    'output_format' => $generationParams['output_format'],
                    'quality_level' => $generationParams['quality_level']
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'project_id' => $projectId,
                'generation_type' => $generationParams['type'] ?? null,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('资源生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '资源生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取资源状态
     */
    public function getResourceStatus(int $resourceId, int $userId): array
    {
        try {
            Log::info('查询资源状态', [
                'resource_id' => $resourceId,
                'user_id' => $userId
            ]);

            $resource = Resource::where('id', $resourceId)
                ->where('user_id', $userId)
                ->first();

            Log::info('资源查询结果', [
                'resource_found' => $resource ? true : false,
                'resource_id' => $resourceId,
                'user_id' => $userId
            ]);

            if (!$resource) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '资源不存在',
                    'data' => []
                ];
            }

            // 简化版本：不依赖生成任务关联
            $generationTasks = [];
            $overallProgress = 100; // 假设已完成资源的进度为100%

            $data = [
                'resource_id' => $resource->id,
                'project_id' => $resource->project_id,
                'resource_type' => $resource->resource_type,
                'status' => $resource->status,
                'progress' => $overallProgress,
                'generation_tasks' => $generationTasks,
                'total_cost' => $resource->actual_cost ?? $resource->estimated_cost,
                'processing_time_ms' => $resource->processing_time_ms,
                'created_at' => $resource->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $resource->completed_at ? $resource->completed_at->format('Y-m-d H:i:s') : null,
                'download_info' => [
                    'available' => $resource->status === Resource::STATUS_COMPLETED,
                    'expires_at' => $resource->completed_at ? $resource->completed_at->addDays(7)->format('Y-m-d H:i:s') : null
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'resource_id' => $resourceId,
                'user_id' => $userId,
            ];

            Log::error('获取资源状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取资源状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取资源列表
     */
    public function getResourceList(int $userId, array $filters): array
    {
        try {
            $query = Resource::where('user_id', $userId);

            // 应用过滤条件
            if (!empty($filters['project_id'])) {
                $query->where('project_id', $filters['project_id']);
            }

            if (!empty($filters['resource_type'])) {
                $query->where('resource_type', $filters['resource_type']);
            }

            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $resources = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $resourceList = [];
            foreach ($resources->items() as $resource) {
                $resourceList[] = [
                    'resource_id' => $resource->id,
                    'project_id' => $resource->project_id,
                    'resource_type' => $resource->resource_type,
                    'status' => $resource->status,
                    'created_at' => $resource->created_at->format('Y-m-d H:i:s'),
                    'file_size' => $resource->file_size ?? 'N/A',
                    'download_count' => $resource->download_count ?? 0
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'resources' => $resourceList,
                    'pagination' => [
                        'current_page' => $resources->currentPage(),
                        'per_page' => $resources->perPage(),
                        'total' => $resources->total(),
                        'last_page' => $resources->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
            ];

            Log::error('获取资源列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取资源列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 删除资源
     */
    public function deleteResource(int $resourceId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $resource = Resource::where('id', $resourceId)
                ->where('user_id', $userId)
                ->first();

            if (!$resource) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '资源不存在',
                    'data' => []
                ];
            }

            // 删除相关文件
            $deletedFiles = 0;
            $freedSpace = 0;

            if ($resource->file_path) {
                if (Storage::exists($resource->file_path)) {
                    $fileSize = Storage::size($resource->file_path);
                    Storage::delete($resource->file_path);
                    $deletedFiles++;
                    $freedSpace += $fileSize;
                }
            }

            // 删除资源记录
            $resource->delete();

            DB::commit();

            Log::info('资源删除成功', [
                'resource_id' => $resourceId,
                'user_id' => $userId,
                'deleted_files' => $deletedFiles,
                'freed_space' => $freedSpace
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '资源删除成功',
                'data' => [
                    'resource_id' => $resourceId,
                    'deleted_files' => $deletedFiles,
                    'freed_space' => $this->formatFileSize($freedSpace)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'resource_id' => $resourceId,
                'user_id' => $userId,
            ];

            Log::error('资源删除失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '资源删除失败',
                'data' => null
            ];
        }
    }

    /**
     * 委托生成任务到相应服务
     */
    private function delegateGeneration(Resource $resource, array $params): array
    {
        $config = $params['generation_config'];
        
        switch ($params['resource_type']) {
            case 'story':
                return $this->storyService->generateStory(
                    $resource->user_id,
                    $config['prompt'],
                    $config['style_id'] ?? null,
                    $resource->project_id,
                    $config
                );
                
            case 'image':
                return $this->imageService->generateImage(
                    $resource->user_id,
                    $config['prompt'],
                    $config['character_id'] ?? null,
                    $resource->project_id,
                    $config
                );
                
            case 'voice':
                return $this->voiceService->synthesizeVoice(
                    $resource->user_id,
                    $config['prompt'] ?? $config['text'] ?? '',
                    $config['character_id'] ?? null,
                    $resource->project_id,
                    $config
                );
                
            case 'video':
                return $this->videoService->generateVideo(
                    $resource->user_id,
                    $config['prompt'],
                    $resource->project_id,
                    $config
                );
                
            case 'music':
                return $this->musicService->generateMusic(
                    $resource->user_id,
                    $config['prompt'],
                    $resource->project_id,
                    $config
                );
                
            case 'sound':
                return $this->soundService->generateSound(
                    $resource->user_id,
                    $config['prompt'],
                    $resource->project_id,
                    $config
                );
                
            default:
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '不支持的资源类型',
                    'data' => []
                ];
        }
    }

    /**
     * 计算生成成本
     */
    private function calculateGenerationCost(array $params): float
    {
        $baseCost = 0.1; // 基础成本
        
        // 质量级别影响成本
        $qualityMultiplier = [
            'low' => 0.5,
            'medium' => 1.0,
            'high' => 1.5,
            'ultra' => 2.0
        ];
        
        $multiplier = $qualityMultiplier[$params['quality_level']] ?? 1.0;
        
        // 批量生成影响成本
        $batchSize = $params['batch_size'] ?? 1;
        
        return round($baseCost * $multiplier * $batchSize, 4);
    }

    /**
     * 获取预估生成时间
     */
    private function getEstimatedDuration(array $params): int
    {
        $baseDuration = [
            'story' => 30,
            'image' => 60,
            'voice' => 45,
            'video' => 180,
            'music' => 120,
            'sound' => 30
        ];
        
        $duration = $baseDuration[$params['resource_type']] ?? 60;
        $batchSize = $params['batch_size'] ?? 1;
        
        return $duration * $batchSize;
    }

    /**
     * 根据资源类型获取业务类型
     */
    private function getBusinessTypeByResourceType(string $resourceType): string
    {
        $mapping = [
            'story' => 'story_generation',
            'image' => 'image_generation',
            'video' => 'video_generation',
            'music' => 'music_generation',
            'sound' => 'sound_generation',
            'character' => 'character_generation'
        ];

        return $mapping[$resourceType] ?? 'story_generation';
    }

    /**
     * 计算任务进度
     */
    private function calculateTaskProgress(AiGenerationTask $task): int
    {
        switch ($task->status) {
            case AiGenerationTask::STATUS_PENDING:
                return 0;
            case AiGenerationTask::STATUS_PROCESSING:
                return 50;
            case AiGenerationTask::STATUS_COMPLETED:
                return 100;
            case AiGenerationTask::STATUS_FAILED:
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . $units[$pow];
    }
}
