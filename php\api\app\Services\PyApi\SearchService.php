<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\CharacterLibrary;
use App\Models\Project;
use App\Models\UserFile;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\Log;

/**
 * 搜索服务
 */
class SearchService
{
    /**
     * 全局搜索
     */
    public function globalSearch(int $userId, string $keyword, string $type = 'all', int $limit = 20): array
    {
        try {
            $startTime = microtime(true);
            $results = [];

            // 根据搜索类型执行搜索
            switch ($type) {
                case 'characters':
                    $results = $this->searchCharacters($keyword, $limit);
                    break;
                
                case 'projects':
                    $results = $this->searchProjects($userId, $keyword, $limit);
                    break;
                
                case 'files':
                    $results = $this->searchFiles($userId, $keyword, $limit);
                    break;
                
                case 'ai_tasks':
                    $results = $this->searchAiTasks($userId, $keyword, $limit);
                    break;
                
                default: // 'all'
                    $results = $this->searchAll($userId, $keyword, $limit);
                    break;
            }

            $searchTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('全局搜索完成', [
                'user_id' => $userId,
                'keyword' => $keyword,
                'type' => $type,
                'results_count' => count($results),
                'search_time' => $searchTime
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'results' => $results,
                    'total' => count($results),
                    'search_time' => $searchTime / 1000, // 转换为秒
                    'keyword' => $keyword,
                    'type' => $type
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'keyword' => $keyword,
                'type' => $type,
                'limit' => $limit,
            ];

            Log::error('全局搜索失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '全局搜索失败',
                'data' => null
            ];
        }
    }

    /**
     * 搜索角色
     */
    private function searchCharacters(string $keyword, int $limit): array
    {
        $characters = CharacterLibrary::active()
            ->search($keyword)
            ->with('category')
            ->limit($limit)
            ->get();

        return $characters->map(function ($character) use ($keyword) {
            return [
                'type' => 'character',
                'id' => $character->id,
                'title' => $character->name,
                'description' => $character->description,
                'category' => $character->category->name,
                'avatar' => $character->avatar,
                'url' => "/characters/{$character->id}",
                'score' => $this->calculateRelevanceScore($keyword, $character->name, $character->description),
                'metadata' => [
                    'rating' => $character->rating,
                    'binding_count' => $character->binding_count,
                    'tags' => $character->tags ?? []
                ]
            ];
        })->sortByDesc('score')->values()->toArray();
    }

    /**
     * 搜索项目
     */
    private function searchProjects(int $userId, string $keyword, int $limit): array
    {
        $projects = Project::where('user_id', $userId)
            ->where(function($query) use ($keyword) {
                $query->where('title', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
            })
            ->limit($limit)
            ->get();

        return $projects->map(function ($project) use ($keyword) {
            return [
                'type' => 'project',
                'id' => $project->id,
                'title' => $project->title,
                'description' => $project->description,
                'url' => "/projects/{$project->id}",
                'score' => $this->calculateRelevanceScore($keyword, $project->title, $project->description),
                'metadata' => [
                    'status' => $project->status,
                    'created_at' => $project->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $project->updated_at->format('Y-m-d H:i:s')
                ]
            ];
        })->sortByDesc('score')->values()->toArray();
    }

    /**
     * 搜索文件
     */
    private function searchFiles(int $userId, string $keyword, int $limit): array
    {
        $files = UserFile::byUser($userId)
            ->search($keyword)
            ->limit($limit)
            ->get();

        return $files->map(function ($file) use ($keyword) {
            return [
                'type' => 'file',
                'id' => $file->id,
                'title' => $file->original_name,
                'description' => "文件类型: {$file->file_type}, 大小: {$file->human_file_size}",
                'url' => "/files/{$file->id}",
                'score' => $this->calculateRelevanceScore($keyword, $file->original_name, $file->filename),
                'metadata' => [
                    'file_type' => $file->file_type,
                    'file_size' => $file->human_file_size,
                    'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                    'download_count' => $file->download_count
                ]
            ];
        })->sortByDesc('score')->values()->toArray();
    }

    /**
     * 搜索AI任务
     */
    private function searchAiTasks(int $userId, string $keyword, int $limit): array
    {
        $tasks = AiGenerationTask::where('user_id', $userId)
            ->where(function($query) use ($keyword) {
                $query->where('task_type', 'like', "%{$keyword}%")
                      ->orWhere('platform', 'like', "%{$keyword}%")
                      ->orWhere('prompt', 'like', "%{$keyword}%");
            })
            ->limit($limit)
            ->get();

        return $tasks->map(function ($task) use ($keyword) {
            // 从input_data中获取prompt
            $prompt = $task->getInputData('prompt', '');

            return [
                'type' => 'ai_task',
                'id' => $task->id,
                'title' => "{$task->task_type} 任务",
                'description' => $prompt ? substr($prompt, 0, 100) . '...' : "AI {$task->task_type} 生成任务",
                'url' => "/ai-tasks/{$task->id}",
                'score' => $this->calculateRelevanceScore($keyword, $task->task_type, $prompt),
                'metadata' => [
                    'task_type' => $task->task_type,
                    'platform' => $task->platform,
                    'status' => $task->status,
                    'cost' => $task->cost,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s')
                ]
            ];
        })->sortByDesc('score')->values()->toArray();
    }

    /**
     * 搜索所有类型
     */
    private function searchAll(int $userId, string $keyword, int $limit): array
    {
        $perTypeLimit = max(1, intval($limit / 4)); // 每种类型分配的限制数量
        
        $results = [];
        
        // 搜索角色
        $characterResults = $this->searchCharacters($keyword, $perTypeLimit);
        $results = array_merge($results, $characterResults);
        
        // 搜索项目
        $projectResults = $this->searchProjects($userId, $keyword, $perTypeLimit);
        $results = array_merge($results, $projectResults);
        
        // 搜索文件
        $fileResults = $this->searchFiles($userId, $keyword, $perTypeLimit);
        $results = array_merge($results, $fileResults);
        
        // 搜索AI任务
        $taskResults = $this->searchAiTasks($userId, $keyword, $perTypeLimit);
        $results = array_merge($results, $taskResults);

        // 按相关性得分排序并限制结果数量
        usort($results, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });

        return array_slice($results, 0, $limit);
    }

    /**
     * 计算相关性得分
     */
    private function calculateRelevanceScore(string $keyword, string $title, string $description): float
    {
        $keyword = strtolower($keyword);
        $title = strtolower($title);
        $description = strtolower($description);

        $score = 0;

        // 标题完全匹配
        if ($title === $keyword) {
            $score += 1.0;
        }
        // 标题包含关键词
        elseif (str_contains($title, $keyword)) {
            $score += 0.8;
        }
        // 标题部分匹配
        else {
            $titleWords = explode(' ', $title);
            $keywordWords = explode(' ', $keyword);
            
            foreach ($keywordWords as $keywordWord) {
                foreach ($titleWords as $titleWord) {
                    if (str_contains($titleWord, $keywordWord)) {
                        $score += 0.3;
                    }
                }
            }
        }

        // 描述包含关键词
        if (str_contains($description, $keyword)) {
            $score += 0.4;
        }
        // 描述部分匹配
        else {
            $descWords = explode(' ', $description);
            $keywordWords = explode(' ', $keyword);
            
            foreach ($keywordWords as $keywordWord) {
                foreach ($descWords as $descWord) {
                    if (str_contains($descWord, $keywordWord)) {
                        $score += 0.1;
                    }
                }
            }
        }

        return min($score, 1.0); // 最大得分为1.0
    }

    /**
     * 高亮搜索关键词
     */
    private function highlightKeyword(string $text, string $keyword): string
    {
        return str_ireplace($keyword, "<mark>{$keyword}</mark>", $text);
    }

    /**
     * 获取搜索建议
     */
    public function getSearchSuggestions(string $keyword, int $limit = 10): array
    {
        try {
            $suggestions = [];

            // 从角色名称中获取建议
            $characters = CharacterLibrary::active()
                ->where('name', 'like', "{$keyword}%")
                ->limit($limit)
                ->pluck('name')
                ->toArray();

            $suggestions = array_merge($suggestions, $characters);

            // 去重并限制数量
            $suggestions = array_unique($suggestions);
            $suggestions = array_slice($suggestions, 0, $limit);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'suggestions' => array_values($suggestions),
                    'keyword' => $keyword
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'keyword' => $keyword,
                'limit' => $limit,
            ];

            Log::error('获取搜索建议失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取搜索建议失败',
                'data' => null
            ];
        }
    }
}
