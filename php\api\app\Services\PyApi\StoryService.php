<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\StyleLibrary;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 故事生成服务
 * 第2D1阶段：文本生成模块
 */
class StoryService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成故事
     */
    public function generateStory(int $userId, string $prompt, ?int $styleId = null, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取风格信息
            $style = null;
            if ($styleId) {
                $style = StyleLibrary::find($styleId);
                if (!$style) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '风格不存在',
                        'data' => []
                    ];
                }
            }

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'deepseek';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_TEXT_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的故事生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '故事生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildStoryPrompt($prompt, $style, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateStoryCost($model, $enhancedPrompt, $generationParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'story_generation',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_STORY_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'style_id' => $styleId,
                    'length' => $generationParams['length'] ?? 'medium',
                    'genre' => $generationParams['genre'] ?? null
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeStoryGeneration($task);

            Log::info('故事生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '故事生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'style_id' => $styleId,
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('故事生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '故事生成失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取故事生成状态
     */
    public function getStoryStatus(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', AiGenerationTask::TYPE_STORY_GENERATION)
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'platform' => $task->platform,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['story_content'] = $task->output_data['story_content'] ?? '';
                $data['scenes'] = $task->output_data['scenes'] ?? [];
                $data['characters'] = $task->output_data['characters'] ?? [];
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取故事状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取故事状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 构建故事提示词
     */
    private function buildStoryPrompt(string $prompt, ?StyleLibrary $style, array $params): string
    {
        $enhancedPrompt = $prompt;

        // 添加风格信息
        if ($style) {
            $enhancedPrompt .= "\n\n风格要求：" . $style->description;
            if ($style->prompt_template) {
                $enhancedPrompt = str_replace('{prompt}', $prompt, $style->prompt_template);
            }
        }

        // 添加长度要求
        $length = $params['length'] ?? 'medium';
        $lengthMap = [
            'short' => '简短故事（500-800字）',
            'medium' => '中等长度故事（800-1500字）',
            'long' => '长篇故事（1500-3000字）'
        ];
        $enhancedPrompt .= "\n\n长度要求：" . $lengthMap[$length];

        // 添加类型要求
        if (!empty($params['genre'])) {
            $enhancedPrompt .= "\n\n故事类型：" . $params['genre'];
        }

        // 添加分镜要求
        $enhancedPrompt .= "\n\n请生成完整的故事内容，并提供分镜场景描述和主要角色信息。";

        return $enhancedPrompt;
    }

    /**
     * 计算故事生成成本
     */
    private function calculateStoryCost(AiModelConfig $model, string $prompt, array $params): float
    {
        $baseTokens = strlen($prompt) / 4; // 估算token数
        $length = $params['length'] ?? 'medium';
        
        $multiplier = [
            'short' => 1.0,
            'medium' => 1.5,
            'long' => 2.5
        ][$length] ?? 1.5;

        $estimatedTokens = $baseTokens * $multiplier;
        return round($estimatedTokens * $model->cost_per_token, 4);
    }

    /**
     * 执行故事生成
     */
    private function executeStoryGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'story_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'story_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'story_generation_error', $task->id);

            Log::error('故事生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            // 🚨 架构边界：使用 AiServiceClient 调用AI服务
            $response = \App\Services\AiServiceClient::call($task->platform, [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->input_data['enhanced_prompt']
                    ]
                ],
                'temperature' => 0.8,
                'max_tokens' => 2000
            ]);

            if ($response['success']) {
                $data = $response['data'];
                $content = $data['choices'][0]['message']['content'] ?? '';

                return [
                    'success' => true,
                    'data' => [
                        'story_content' => $content,
                        'scenes' => $this->extractScenes($content),
                        'characters' => $this->extractCharacters($content),
                        'mode' => $response['mode']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . ($response['error'] ?? '未知错误')
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 提取场景信息
     */
    private function extractScenes(string $content): array
    {
        // 简单的场景提取逻辑，实际可以更复杂
        $scenes = [];
        $lines = explode("\n", $content);
        $sceneNumber = 1;

        foreach ($lines as $line) {
            if (preg_match('/场景|镜头|分镜/', $line)) {
                $scenes[] = [
                    'scene_number' => $sceneNumber++,
                    'description' => trim($line),
                    'duration' => '30秒'
                ];
            }
        }

        return $scenes;
    }

    /**
     * 提取角色信息
     */
    private function extractCharacters(string $content): array
    {
        // 简单的角色提取逻辑
        $characters = [];
        
        if (preg_match_all('/([^，。！？\s]{2,6})(?:是|为|乃).*?(?:的|之).*?(?:角色|人物|主角|配角)/', $content, $matches)) {
            foreach ($matches[1] as $name) {
                $characters[] = [
                    'name' => $name,
                    'description' => '从故事中提取的角色',
                    'type' => 'story_character'
                ];
            }
        }

        return array_unique($characters, SORT_REGULAR);
    }
}
