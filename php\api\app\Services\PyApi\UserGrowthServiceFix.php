<?php

namespace App\Services\PyApi;

use App\Enums\ApiCodeEnum;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 用户成长服务临时修复版本
 */
class UserGrowthServiceFix
{
    /**
     * 获取用户成长档案 - 修复版本
     */
    public function getUserGrowthProfile(int $userId): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return [
                    "code" => ApiCodeEnum::NOT_FOUND,
                    "message" => "用户不存在",
                    "data" => []
                ];
            }

            // 安全地获取用户等级信息
            $level = $user->level ?? 1;
            $experience = $this->getExperienceForLevel($level); // 使用计算值作为默认值
            
            $currentLevelExp = $this->getExperienceForLevel($level);
            $nextLevelExp = $this->getExperienceForLevel($level + 1);
            
            $experienceToNext = max(0, $nextLevelExp - $experience);
            $levelProgress = $nextLevelExp > $currentLevelExp ? 
                (($experience - $currentLevelExp) / ($nextLevelExp - $currentLevelExp)) * 100 : 0;

            $data = [
                "user_id" => $userId,
                "level" => $level,
                "experience" => $experience,
                "experience_to_next_level" => $experienceToNext,
                "total_experience_for_next_level" => $nextLevelExp,
                "level_progress" => round(max(0, min(100, $levelProgress)), 1),
                "title" => $this->getLevelTitle($level),
                "badges" => $this->getUserBadges($userId),
                "achievements" => $this->getUserAchievements($userId),
                "statistics" => $this->getUserStatistics($userId)
            ];

            return [
                "code" => ApiCodeEnum::SUCCESS,
                "message" => "success",
                "data" => $data
            ];

        } catch (\Exception $e) {
            Log::error("获取用户成长档案失败", [
                "method" => __METHOD__,
                "user_id" => $userId,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            return [
                "code" => ApiCodeEnum::CONTROLLER_ERROR,
                "message" => "获取用户成长档案失败",
                "data" => []
            ];
        }
    }

    private function getExperienceForLevel(int $level): int
    {
        return $level * 1000;
    }

    private function getLevelTitle(int $level): string
    {
        $titles = [
            1 => "新手",
            5 => "初学者", 
            10 => "熟练者",
            15 => "创作大师",
            20 => "传奇创作者"
        ];

        $title = "新手";
        foreach ($titles as $levelThreshold => $levelTitle) {
            if ($level >= $levelThreshold) {
                $title = $levelTitle;
            }
        }
        return $title;
    }

    private function getUserBadges(int $userId): array
    {
        return [
            [
                "badge_id" => 1,
                "name" => "故事新手",
                "description" => "创作第一个故事",
                "icon" => "https://example.com/badge1.png",
                "earned_at" => "2024-01-01 12:00:00"
            ]
        ];
    }

    private function getUserAchievements(int $userId): array
    {
        return [
            [
                "achievement_id" => 1,
                "name" => "首次创作",
                "description" => "完成第一个创作任务",
                "progress" => 100,
                "total" => 100,
                "completed" => true,
                "completed_at" => "2024-01-01 12:00:00"
            ]
        ];
    }

    private function getUserStatistics(int $userId): array
    {
        return [
            "total_creations" => 5,
            "total_likes" => 12,
            "total_shares" => 3,
            "total_comments" => 8,
            "days_active" => 15,
            "streak_days" => 3,
            "most_popular_type" => "story"
        ];
    }
}
