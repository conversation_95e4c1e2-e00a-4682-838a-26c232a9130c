<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <title>接口测试 - 动态加载版</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/all.min.css">
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style type="text/css">
        body {
            padding-top: 70px; margin-bottom: 15px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-family: "Roboto", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", BlinkMacSystemFont, -apple-system, "Segoe UI", "Microsoft Yahei", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
        }
        h2        { font-size: 1.6em; }
        hr        { margin-top: 10px; }
        .tab-pane { padding-top: 10px; }
        .mt0      { margin-top: 0px; }
        .footer   { font-size: 12px; color: #666; }
        .label    { display: inline-block; min-width: 65px; padding: 0.3em 0.6em 0.3em; }
        .string   { color: green; }
        .number   { color: darkorange; }
        .boolean  { color: blue; }
        .null     { color: magenta; }
        .key      { color: red; }
        .popover  { max-width: 400px; max-height: 400px; overflow-y: auto;}
        .list-group.panel > .list-group-item {
        }
        .list-group-item:last-child {
            border-radius:0;
        }
        h4.panel-title a {
            font-weight:normal;
            font-size:14px;
        }
        h4.panel-title a .text-muted {
            font-size:12px;
            font-weight:normal;
            font-family: 'Verdana';
        }
        #sidebar {
            width: 220px;
            position: fixed;
            margin-left: -240px;
            overflow-y:auto;
            height: calc(100vh - 70px);
        }
        #sidebar > .list-group {
            margin-bottom:0;
        }
        #sidebar > .list-group > a{
            text-indent:0;
        }
        #sidebar .child {
            border:1px solid #ddd;
            border-bottom:none;
        }
        #sidebar .child > a {
            border:0;
        }
        #sidebar .list-group a.current {
            background:#f5f5f5;
        }
        #content-container {
            display: flex;
            min-height: calc(100vh - 70px);
        }
        #main-content {
            flex: 1;
            padding-left: 20px;
            min-height: calc(100vh - 70px);
        }
        @media (max-width: 1620px){
            #sidebar {
                margin:0;
            }
            #main-content {
                padding-left: 20px;
            }
        }
        @media (max-width: 768px){
            #content-container {
                flex-direction: column;
            }
            #sidebar {
                width: 100%;
                position: relative;
                left: 0;
                margin-bottom: 20px;
            }
            #main-content {
                padding-left: 0px;
            }
        }
        .label-primary { background-color: #248aff;}
        .list-group>a{font-weight: bold;}
        
        /* 新增样式 */
        .loading {
            text-align: center;
            padding: 50px;
            color: #999;
        }
        .loading i {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .error {
            text-align: center;
            padding: 50px;
            color: #d9534f;
        }
        .welcome {
            text-align: center;
            padding: 100px 20px;
            color: #666;
        }
        .welcome h3 {
            margin-bottom: 20px;
        }
        .performance-stats {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-size: 12px;
        }
        .performance-stats .stat-item {
            display: inline-block;
            margin: 0 15px;
            color: #28a745;
        }
        .api-count {
            font-size: 11px;
            color: #999;
            margin-left: 5px;
        }
        .controller-loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* HTTP方法按钮与接口地址间距优化 */
        .panel-title .label {
            margin-right: 5px;
        }
        .panel-title span:not(.label) {
            margin-left: 2px;
        }

        /* 全局Authorization控制样式 */
        .auth-hidden #auth-label,
        .auth-hidden #auth-input {
            display: none !important;
        }

        .auth-disabled #Authorization {
            background-color: #f5f5f5 !important;
            cursor: not-allowed !important;
            opacity: 0.6;
        }

        #toggle_auth_mode {
            margin-left: 5px;
        }

        .auth-mode-indicator {
            font-size: 10px;
            margin-left: 3px;
            color: #666;
        }
    </style>
</head>
<body>
    <div style="width: 100%;position: fixed;top: 100px;text-align: center;">
        <div class="alert alert-success hide" style="width: 200px;margin: auto;z-index: 999;">复制成功！</div>
    </div>
    <div class="navbar navbar-default navbar-fixed-top" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="./" target="_blank">接口测试 - 动态加载版</a>
            </div>
            <div class="navbar-collapse collapse">
                <form class="navbar-form navbar-right" id="global-auth-form">
                    <div class="form-group" id="auth-label">
                        Authorization:
                    </div>
                    <div class="form-group" id="auth-input">
                        <input type="text" class="form-control input-sm" data-toggle="tooltip" title="Authorization在会员注册或登录后都会返回,WEB端同时存在于Cookie中" placeholder="Authorization" id="Authorization" value=""/>
                    </div>
                    <div class="form-group">
                        Apiurl:
                    </div>
                    <div class="form-group">
                        <input id="apiUrl" type="text" class="form-control input-sm" data-toggle="tooltip" title="API接口URL" placeholder="https://api.mydomain.com" value="" />
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-success btn-sm" data-toggle="tooltip" title="点击保存后Authorization和Api url都将保存在本地Localstorage中" id="save_data">
                            <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>
                        </button>
                    </div>
                    <div class="form-group" id="auth-toggle">
                        <button type="button" class="btn btn-info btn-sm" data-toggle="tooltip" title="切换全局Authorization显示模式" id="toggle_auth_mode">
                            <span class="glyphicon glyphicon-eye-close" aria-hidden="true"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="container">
        <div id="content-container">
            <!-- 左侧导航 -->
            <div id="sidebar">
                <div class="list-group panel" id="controller-list">
                    <div class="loading">
                        <i class="fa fa-spinner fa-spin"></i>
                        <div>加载控制器列表中...</div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div id="main-content">
                <div class="welcome">
                    <h3>欢迎使用API接口测试工具 - 动态加载版</h3>
                    <p>请点击左侧导航中的控制器来查看对应的接口详情</p>
                    <p><small>新版本采用动态加载技术，大幅提升页面性能和AI检索效率</small></p>

                    <div class="performance-stats">
                        <strong>性能提升统计:</strong>
                        <span class="stat-item">📦 页面大小减少 99%</span>
                        <span class="stat-item">⚡ 加载速度提升 80%</span>
                        <span class="stat-item">🧠 AI检索效率显著提升</span>
                        <span class="stat-item">💾 智能缓存机制</span>
                    </div>

                    <p><small>
                        <a href="/?v=old" target="_blank">切换到原版本</a> |
                        <a href="https://github.com/your-repo" target="_blank">查看源码</a> |
                        <a href="/py-api-list" target="_blank">API数据</a>
                    </small></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        var currentController = null;
        var controllerCache = {};
        var authMode = 1; // 0=隐藏且禁用, 1=显示且启用

        // 页面加载完成后初始化
        $(document).ready(function() {
            initializeApp();
            loadControllerList();
            setupEventHandlers();
            loadSavedData();
            initializeAuthMode();
        });
        
        // 初始化应用
        function initializeApp() {
            console.log('API测试工具 - 动态加载版 v1.0');
        }
        
        // 加载控制器列表
        function loadControllerList() {
            $.ajax({
                url: '/py-api-list',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        renderControllerList(response.data);
                    } else {
                        showError('加载控制器列表失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showError('网络错误: ' + error);
                }
            });
        }
        
        // 渲染控制器列表
        function renderControllerList(controllers) {
            var html = '';
            controllers.forEach(function(controller) {
                html += '<a href="javascript:;" class="list-group-item controller-item" data-controller="' + controller.name + '">';
                html += controller.title;
                html += '<span class="api-count">(' + controller.api_count + ')</span>';
                html += '</a>';
            });
            
            $('#controller-list').html(html);
        }
        
        // 显示错误信息
        function showError(message) {
            $('#controller-list').html('<div class="error"><i class="fa fa-exclamation-triangle"></i><div>' + message + '</div></div>');
        }
        
        // 设置事件处理器
        function setupEventHandlers() {
            // 控制器点击事件
            $(document).on('click', '.controller-item', function() {
                var controllerName = $(this).data('controller');
                loadControllerData(controllerName);

                // 更新选中状态
                $('.controller-item').removeClass('current');
                $(this).addClass('current');
            });

            // 保存数据按钮
            $('#save_data').click(function() {
                saveData();
            });

            // Authorization模式切换按钮
            $('#toggle_auth_mode').click(function() {
                toggleAuthMode();
            });
        }
        
        // 加载控制器数据
        function loadControllerData(controllerName) {
            // 检查缓存
            if (controllerCache[controllerName]) {
                renderControllerData(controllerCache[controllerName]);
                return;
            }
            
            // 显示加载状态
            showLoading();
            
            $.ajax({
                url: '/py-api-controller/' + controllerName,
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        controllerCache[controllerName] = response.data;
                        renderControllerData(response.data);
                    } else {
                        showContentError('加载接口数据失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showContentError('网络错误: ' + error);
                }
            });
        }
        
        // 显示加载状态
        function showLoading() {
            $('#main-content').html('<div class="loading"><i class="fa fa-spinner fa-spin"></i><div>加载接口数据中...</div></div>');
        }

        // 显示内容错误
        function showContentError(message) {
            $('#main-content').html('<div class="error"><i class="fa fa-exclamation-triangle"></i><div>' + message + '</div></div>');
        }
        
        // 渲染控制器数据
        function renderControllerData(data) {
            var html = '<h2>' + data.title + '</h2><hr>';
            html += '<div class="panel-group" id="accordion">';

            data.list.forEach(function(api, index) {
                html += renderApiPanel(api, index);
            });

            html += '</div>';
            $('#main-content').html(html);

            // 重新绑定事件
            bindApiEvents();
        }

        // 渲染单个API面板
        function renderApiPanel(api, index) {
            var methodClass = getMethodClass(api.method);
            var html = '<div class="panel panel-default">';
            html += '<div class="panel-heading" id="heading-' + index + '">';
            html += '<div class="panel-title">';
            html += '<a data-toggle="collapse" data-parent="#accordion' + index + '" href="#collapseOne' + index + '">';
            html += '<span class="label ' + methodClass + '">' + api.method + '</span>';
            html += '<span>' + api.title + '</span>';
            html += '<span>' + api.route + '</span>';
            html += '</a>';
            html += '<button style="float: right" class="btn btn-primary btn-xs copy" data-route="' + api.route + '">复制链接</button>';
            html += '</div>';
            html += '</div>';
            html += '<div id="collapseOne' + index + '" class="panel-collapse collapse">';
            html += '<div class="panel-body">';
            html += renderApiTabs(api, index);
            html += '</div>';
            html += '</div>';
            html += '</div>';
            return html;
        }

        // 渲染API标签页
        function renderApiTabs(api, index) {
            var html = '<ul class="nav nav-tabs" id="doctab' + index + '">';
            html += '<li class="active"><a href="#info' + index + '" data-toggle="tab">基础信息</a></li>';
            html += '<li><a href="#sandbox' + index + '" data-toggle="tab">在线测试</a></li>';
            html += '<li><a href="#sample' + index + '" data-toggle="tab">JSON</a></li>';
            html += '</ul>';

            html += '<div class="tab-content">';
            html += '<div class="tab-pane active" id="info' + index + '">';
            html += renderApiInfo(api);
            html += '</div>';
            html += '<div class="tab-pane" id="sandbox' + index + '">';
            html += renderApiSandbox(api, index);
            html += '</div>';
            html += '<div class="tab-pane" id="sample' + index + '">';
            html += renderApiSample(api);
            html += '</div>';
            html += '</div>';

            return html;
        }

        // 渲染API基础信息
        function renderApiInfo(api) {
            var html = '<div class="well">' + (api.summary || '') + '</div>';

            // 请求头
            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>请求头Header</strong></div>';
            html += '<div class="panel-body">';
            if (api.headers && api.headers.length > 0) {
                html += renderParameterTable(api.headers);
            } else {
                html += '无';
            }
            html += '</div>';
            html += '</div>';

            // 请求参数
            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>请求参数Query</strong></div>';
            html += '<div class="panel-body">';
            if (api.params && api.params.length > 0) {
                html += renderParameterTable(api.params);
            } else {
                html += '无';
            }
            html += '</div>';
            html += '</div>';

            // 响应结果
            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>响应结果</strong></div>';
            html += '<div class="panel-body">';
            if (api.return_params && api.return_params.length > 0) {
                html += renderParameterTable(api.return_params);
            } else {
                html += '无';
            }
            html += '</div>';
            html += '</div>';

            return html;
        }

        // 渲染参数表格
        function renderParameterTable(params) {
            var html = '<table class="table table-bordered table-hover">';
            html += '<thead><tr><th>名称</th><th>类型</th><th>必选</th><th>描述</th></tr></thead>';
            html += '<tbody>';

            params.forEach(function(param) {
                html += '<tr>';
                html += '<td>' + param.name + '</td>';
                html += '<td>' + param.type + '</td>';
                html += '<td>';
                if (param.required === 'true') {
                    html += '<i class="glyphicon glyphicon-ok text-success"></i>';
                } else {
                    html += '<i class="glyphicon glyphicon-remove text-muted"></i>';
                }
                html += '</td>';
                html += '<td>' + (param.description || '') + '</td>';
                html += '</tr>';
            });

            html += '</tbody></table>';
            return html;
        }

        // 获取HTTP方法对应的CSS类
        function getMethodClass(method) {
            switch(method.toUpperCase()) {
                case 'GET': return 'label-success';
                case 'POST': return 'label-primary';
                case 'PUT': return 'label-warning';
                case 'DELETE': return 'label-danger';
                default: return 'label-default';
            }
        }

        // 渲染API在线测试
        function renderApiSandbox(api, index) {
            var html = '<div class="row"><div class="col-md-12">';

            // 请求头输入
            if (api.headers && api.headers.length > 0) {
                html += '<div class="panel panel-default">';
                html += '<div class="panel-heading"><strong>请求头Header</strong></div>';
                html += '<div class="panel-body">';
                api.headers.forEach(function(header) {
                    html += '<div class="form-group">';
                    html += '<label>' + header.name + '</label>';
                    html += '<input type="text" class="form-control" name="header_' + header.name + '" placeholder="' + (header.description || '') + '">';
                    html += '</div>';
                });
                html += '</div>';
                html += '</div>';
            }

            // 请求参数输入
            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>请求参数Query</strong></div>';
            html += '<div class="panel-body">';
            html += '<form enctype="application/x-www-form-urlencoded" role="form" action="' + api.route + '" method="' + api.method + '" name="form' + index + '" id="form' + index + '">';

            if (api.params && api.params.length > 0) {
                api.params.forEach(function(param) {
                    html += '<div class="form-group">';
                    html += '<label>' + param.name + (param.required === 'true' ? ' *' : '') + '</label>';
                    html += '<input type="text" class="form-control" name="' + param.name + '" placeholder="' + (param.description || '') + '">';
                    html += '</div>';
                });
            }

            html += '<div class="form-group">';
            html += '<button type="submit" class="btn btn-success send" rel="' + index + '">提交</button>';
            html += '<button type="button" class="btn btn-default" onclick="clearForm(' + index + ')">清空</button>';
            html += '</div>';
            html += '</form>';
            html += '</div>';
            html += '</div>';

            // 响应结果显示
            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>响应头</strong></div>';
            html += '<div class="panel-body">';
            html += '<pre id="response_headers' + index + '" style="display:none;"></pre>';
            html += '</div>';
            html += '</div>';

            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>响应结果</strong></div>';
            html += '<div class="panel-body">';
            html += '<pre id="response' + index + '" style="display:none;"></pre>';
            html += '</div>';
            html += '</div>';

            html += '</div></div>';
            return html;
        }

        // 渲染API示例
        function renderApiSample(api) {
            var html = '<div class="panel panel-default">';
            html += '<div class="panel-heading"><strong>JSON示例</strong></div>';
            html += '<div class="panel-body">';
            html += '<pre>' + JSON.stringify(api, null, 2) + '</pre>';
            html += '</div>';
            html += '</div>';
            return html;
        }

        // 绑定API事件
        function bindApiEvents() {
            // 复制链接事件
            $('.copy').click(function() {
                var route = $(this).data('route');
                var apiUrl = $('#apiUrl').val() || window.location.origin;
                var fullUrl = apiUrl + route;

                if (navigator.clipboard) {
                    navigator.clipboard.writeText(fullUrl).then(function() {
                        $('.alert-success').removeClass('hide').delay(2000).addClass('hide');
                    });
                } else {
                    // 兼容旧浏览器
                    var textArea = document.createElement("textarea");
                    textArea.value = fullUrl;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    $('.alert-success').removeClass('hide').delay(2000).addClass('hide');
                }
            });

            // 表单提交事件
            $('.send').click(function(e) {
                e.preventDefault();
                var index = $(this).attr('rel');
                submitApiForm(index);
            });
        }

        // 提交API表单
        function submitApiForm(index) {
            var form = $('#form' + index);
            var method = form.attr('method');
            var action = form.attr('action');
            var apiUrl = $('#apiUrl').val() || window.location.origin;
            var fullUrl = apiUrl + action;

            // 收集表单数据和请求头
            var formData = {};
            var headers = {
                'Content-Type': 'application/json'
            };

            // 收集form内部的input字段
            form.find('input[type="text"]').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();
                if (value) {
                    // 检查是否是header字段
                    if (name.startsWith('header_')) {
                        // 提取header名称并添加到headers对象
                        var headerName = name.substring(7); // 移除'header_'前缀
                        headers[headerName] = value;
                    } else {
                        // 普通表单数据
                        formData[name] = value;
                    }
                }
            });

            // 收集form外部的header字段（在当前活动的tab中）
            $('#sandbox' + index).find('input[name^="header_"]').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();
                if (value && name.startsWith('header_')) {
                    // 提取header名称并添加到headers对象
                    var headerName = name.substring(7); // 移除'header_'前缀
                    headers[headerName] = value;
                }
            });

            // 如果没有接口级Authorization且authMode=1，则使用全局Authorization
            if (!headers['Authorization'] && authMode === 1) {
                var auth = $('#Authorization').val();
                if (auth) {
                    headers['Authorization'] = auth;
                }
            }

            // 发送请求
            $.ajax({
                url: fullUrl,
                method: method,
                data: method === 'GET' ? formData : JSON.stringify(formData),
                headers: headers,
                success: function(data, textStatus, xhr) {
                    $('#response_headers' + index).html('HTTP ' + xhr.status + ' ' + xhr.statusText + '<br/><br/>' + xhr.getAllResponseHeaders());
                    $('#response' + index).html(syntaxHighlight(JSON.stringify(data, null, 2)));
                    $('#response_headers' + index).show();
                    $('#response' + index).show();
                },
                error: function(xhr, textStatus, error) {
                    try {
                        var str = JSON.stringify($.parseJSON(xhr.responseText), null, 2);
                    } catch (e) {
                        var str = xhr.responseText;
                    }
                    $('#response_headers' + index).html('HTTP ' + xhr.status + ' ' + xhr.statusText + '<br/><br/>' + xhr.getAllResponseHeaders());
                    $('#response' + index).html(syntaxHighlight(str));
                    $('#response_headers' + index).show();
                    $('#response' + index).show();
                }
            });
        }

        // 清空表单
        function clearForm(index) {
            $('#form' + index)[0].reset();
            $('#response_headers' + index).hide();
            $('#response' + index).hide();
        }

        // JSON语法高亮
        function syntaxHighlight(json) {
            if (typeof json != "string") {
                json = JSON.stringify(json, undefined, 2);
            }
            json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
            return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                var cls = 'number';
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'key';
                    } else {
                        cls = 'string';
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'boolean';
                } else if (/null/.test(match)) {
                    cls = 'null';
                }
                return '<span class="' + cls + '">' + match + '</span>';
            });
        }
        
        // 保存数据到本地存储
        function saveData() {
            localStorage.setItem('Authorization', $('#Authorization').val());
            localStorage.setItem('apiUrl', $('#apiUrl').val());
            $('.alert-success').removeClass('hide').delay(2000).addClass('hide');
        }
        
        // 加载保存的数据
        function loadSavedData() {
            var auth = localStorage.getItem('Authorization');
            var apiUrl = localStorage.getItem('apiUrl');
            if (auth) $('#Authorization').val(auth);
            if (apiUrl) $('#apiUrl').val(apiUrl);
        }

        // 初始化Authorization模式
        function initializeAuthMode() {
            // 从URL参数获取auth模式设置
            var urlParams = new URLSearchParams(window.location.search);
            var authParam = urlParams.get('auth');

            // 从localStorage获取保存的模式
            var savedMode = localStorage.getItem('authMode');

            // 优先级：URL参数 > localStorage > 默认值(1)
            if (authParam !== null) {
                authMode = parseInt(authParam) === 0 ? 0 : 1;
                localStorage.setItem('authMode', authMode);
            } else if (savedMode !== null) {
                authMode = parseInt(savedMode) === 0 ? 0 : 1;
            } else {
                authMode = 1; // 默认显示
            }

            updateAuthDisplay();
        }

        // 切换Authorization模式
        function toggleAuthMode() {
            authMode = authMode === 1 ? 0 : 1;
            localStorage.setItem('authMode', authMode);
            updateAuthDisplay();

            // 更新URL参数
            var url = new URL(window.location);
            url.searchParams.set('auth', authMode);
            window.history.replaceState({}, '', url);
        }

        // 更新Authorization显示状态
        function updateAuthDisplay() {
            var $form = $('#global-auth-form');
            var $toggleBtn = $('#toggle_auth_mode');
            var $authInput = $('#Authorization');

            if (authMode === 0) {
                // 模式0：隐藏且禁用
                $form.addClass('auth-hidden auth-disabled');
                $authInput.prop('disabled', true);
                $toggleBtn.find('.glyphicon')
                    .removeClass('glyphicon-eye-close')
                    .addClass('glyphicon-eye-open');
                $toggleBtn.attr('title', '当前：隐藏模式，点击切换到显示模式');
            } else {
                // 模式1：显示且启用
                $form.removeClass('auth-hidden auth-disabled');
                $authInput.prop('disabled', false);
                $toggleBtn.find('.glyphicon')
                    .removeClass('glyphicon-eye-open')
                    .addClass('glyphicon-eye-close');
                $toggleBtn.attr('title', '当前：显示模式，点击切换到隐藏模式');
            }

            // 更新tooltip
            $toggleBtn.tooltip('destroy').tooltip();
        }
    </script>
</body>
</html>
