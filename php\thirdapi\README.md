# 第三方服务集成模拟返回数据服务 API 文档

## 📋 项目概述

第三方服务集成模拟返回数据服务是一个专门为本地开发设计的模拟服务，它按照真实第三方服务的API接口文档要求，接收和模拟返回处理结果，支持"工具API接口服务"的本地开发工作。

### 🎯 核心特性

- **完全兼容**: 严格按照真实第三方服务API文档实现
- **智能模拟**: 支持成功、失败、超时等多种状态模拟
- **参数验证**: 完整的参数格式和业务规则验证
- **性能监控**: 内置性能监控和日志记录
- **无缝切换**: 生产环境可通过配置直接切换到真实服务

### 🏗️ 支持的第三方服务

1. **微信服务** - OAuth登录、微信支付
2. **支付宝支付** - 统一收单、退款查询
3. **短信服务** - 阿里云短信、腾讯云短信
4. **邮件服务** - SMTP、SendCloud、模板邮件

## 🚀 快速开始

### 环境要求

- PHP 7.4+
- 支持JSON和XML处理
- 可写的logs和cache目录

### 访问地址

```
开发环境: https://thirdapi.tiptop.cn/
生产环境: 通过配置切换到真实第三方服务
```

### 基础配置

服务配置文件位于 `config/config.php`，可以调整：

- 成功率模拟 (success_rate)
- 网络延迟模拟 (delay_range)
- 服务启用状态 (enabled)

## 📚 API 接口文档

### 通用响应格式

#### 成功响应
```json
{
    "code": 0,
    "message": "成功",
    "timestamp": 1691234567,
    "data": {
        // 具体数据
    }
}
```

#### 错误响应
```json
{
    "code": 1001,
    "message": "参数错误",
    "timestamp": 1691234567,
    "error": "INVALID_PARAMS",
    "data": {
        // 错误详情（可选）
    }
}
```

### 🔐 微信服务 API

#### 1. OAuth授权登录

**获取授权码**
```http
GET /wechat/oauth/authorize?appid={appid}&redirect_uri={redirect_uri}&response_type=code&scope=snsapi_userinfo&state={state}
```

**获取访问令牌**
```http
GET /wechat/oauth/access_token?appid={appid}&secret={secret}&code={code}&grant_type=authorization_code
```

**获取用户信息**
```http
GET /wechat/oauth/userinfo?access_token={access_token}&openid={openid}
```

#### 2. 微信支付

**统一下单**
```http
POST /wechat/pay/unifiedorder
Content-Type: application/xml

<xml>
    <appid>wx_app_id</appid>
    <mch_id>mch_id</mch_id>
    <nonce_str>random_string</nonce_str>
    <sign>signature</sign>
    <body>商品描述</body>
    <out_trade_no>order_number</out_trade_no>
    <total_fee>100</total_fee>
    <spbill_create_ip>127.0.0.1</spbill_create_ip>
    <notify_url>https://api.tiptop.cn/notify</notify_url>
    <trade_type>JSAPI</trade_type>
</xml>
```

**查询订单**
```http
POST /wechat/pay/orderquery
Content-Type: application/xml

<xml>
    <appid>wx_app_id</appid>
    <mch_id>mch_id</mch_id>
    <transaction_id>微信订单号</transaction_id>
    <nonce_str>random_string</nonce_str>
    <sign>signature</sign>
</xml>
```

### 💰 支付宝支付 API

#### 统一收单交易创建
```http
POST /alipay/trade/create
Content-Type: application/json

{
    "app_id": "2021001234567890",
    "method": "alipay.trade.create",
    "charset": "utf-8",
    "sign_type": "RSA2",
    "sign": "signature",
    "timestamp": "2023-08-01 12:00:00",
    "version": "1.0",
    "biz_content": "{\"out_trade_no\":\"ORDER123\",\"total_amount\":\"100.00\",\"subject\":\"商品标题\"}"
}
```

#### 统一收单交易查询
```http
POST /alipay/trade/query
Content-Type: application/json

{
    "app_id": "2021001234567890",
    "method": "alipay.trade.query",
    "charset": "utf-8",
    "sign_type": "RSA2",
    "sign": "signature",
    "timestamp": "2023-08-01 12:00:00",
    "version": "1.0",
    "biz_content": "{\"out_trade_no\":\"ORDER123\"}"
}
```

### 📱 短信服务 API

#### 阿里云短信发送
```http
POST /sms/aliyun/send
Content-Type: application/json

{
    "PhoneNumbers": "13800138000",
    "SignName": "AI视频创作工具",
    "TemplateCode": "SMS_123456789",
    "TemplateParam": "{\"code\":\"123456\"}"
}
```

#### 腾讯云短信发送
```http
POST /sms/tencent/send
Content-Type: application/json

{
    "PhoneNumberSet": ["+8613800138000"],
    "TemplateId": "123456",
    "SmsSdkAppId": "1400000000",
    "SignName": "AI视频创作工具",
    "TemplateParamSet": ["123456"]
}
```

#### 短信验证码验证
```http
POST /sms/verify
Content-Type: application/json

{
    "phone": "13800138000",
    "code": "123456"
}
```

### 📧 邮件服务 API

#### SMTP邮件发送
```http
POST /email/smtp/send
Content-Type: application/json

{
    "to": ["<EMAIL>"],
    "subject": "邮件主题",
    "content": "邮件内容",
    "from": "<EMAIL>"
}
```

#### SendCloud邮件发送
```http
POST /email/sendcloud/send
Content-Type: application/json

{
    "apiUser": "api_user",
    "apiKey": "api_key",
    "to": "<EMAIL>",
    "subject": "邮件主题",
    "html": "<h1>邮件内容</h1>",
    "from": "<EMAIL>"
}
```

#### 模板邮件发送
```http
POST /email/template/send
Content-Type: application/json

{
    "to": ["<EMAIL>"],
    "template": "verification",
    "variables": {
        "code": "123456"
    }
}
```

### 🔧 系统管理 API

#### 健康状态检查
```http
GET /system/health
```

#### 获取系统配置
```http
GET /system/config
```

#### 获取API接口列表
```http
GET /system/routes
```

#### 获取性能统计
```http
GET /system/metrics
```

## 🎛️ 模拟配置说明

### 成功率配置
每个服务都可以配置成功率，用于模拟真实环境中的失败情况：

```php
'success_rate' => 95, // 95%成功率
```

### 延迟配置
模拟网络延迟，单位为毫秒：

```php
'delay_range' => [100, 500], // 100-500ms随机延迟
```

### 错误模拟
系统会根据参数验证结果和成功率配置，模拟以下错误：

- **参数错误**: 必需参数缺失、格式错误
- **业务错误**: 签名验证失败、权限不足
- **系统错误**: 服务不可用、网络超时

## 📊 日志和监控

### 日志文件
- `logs/thirdapi-YYYY-MM-DD.log` - 主日志文件
- `logs/error-YYYY-MM-DD.log` - 错误日志文件

### 性能监控
- 请求响应时间
- 内存使用情况
- 成功/失败统计
- 慢请求监控

## 🔄 生产环境切换

在生产环境中，通过修改"工具API接口服务"的配置，可以直接切换到真实的第三方服务：

```php
// 开发环境
'third_party_base_url' => 'https://thirdapi.tiptop.cn/',

// 生产环境
'wechat_api_url' => 'https://api.weixin.qq.com/',
'alipay_gateway' => 'https://openapi.alipay.com/gateway.do',
'aliyun_sms_endpoint' => 'https://dysmsapi.aliyuncs.com/',
// ...
```

## ⚠️ 注意事项

1. **仅用于开发环境**: 本服务仅用于本地开发和测试
2. **数据安全**: 模拟数据不包含真实用户信息
3. **API兼容性**: 严格按照官方API文档实现，确保兼容性
4. **性能考虑**: 生产环境必须切换到真实服务

## 🆘 故障排除

### 常见问题

1. **服务无法访问**
   - 检查域名解析: `thirdapi.tiptop.cn`
   - 检查SSL证书配置
   - 检查Nginx配置

2. **参数验证失败**
   - 检查请求格式是否正确
   - 检查必需参数是否完整
   - 查看错误日志获取详细信息

3. **模拟失败率过高**
   - 调整配置文件中的 `success_rate`
   - 检查网络延迟配置是否合理

### 联系支持

如有问题，请查看日志文件或联系开发团队。
