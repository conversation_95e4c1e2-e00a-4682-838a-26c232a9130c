<?php
/**
 * 第三方服务集成模拟返回数据服务 - 配置文件
 *
 * 🚨 架构边界规范：
 * ✅ 本服务仅进行模拟，不会向真实第三方平台发起任何网络请求
 * ✅ 严格按照真实第三方平台API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实用户数据，不执行真实业务操作
 *
 * 环境切换：
 * - 本地开发：工具API → 第三方模拟服务 → 模拟响应（无真实调用）
 * - 生产环境：工具API → 真实第三方平台 → 真实响应（真实调用）
 */

// 基础配置
define('THIRD_API_VERSION', '1.0.0');
define('THIRD_API_NAME', '第三方服务集成模拟返回数据服务');

// 日志配置
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARN, ERROR
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_FILES', 30); // 保留30天的日志文件

// 缓存配置
define('CACHE_ENABLED', true);
define('CACHE_PATH', __DIR__ . '/../cache/');
define('CACHE_TTL', 3600); // 缓存1小时

// 性能配置
define('PERFORMANCE_MONITORING', true);
define('SLOW_REQUEST_THRESHOLD', 1000); // 慢请求阈值（毫秒）

// 第三方服务模拟配置
$thirdPartyConfig = [
    // 微信服务配置 - 🚨 仅模拟，不发起真实请求
    'wechat' => [
        'name' => '微信服务',
        'description' => '微信OAuth登录、微信支付等服务模拟',
        'business_types' => ['oauth_login', 'payment', 'user_info'],
        'real_api' => [
            'app_id' => env('WECHAT_APP_ID', ''),
            'app_secret' => env('WECHAT_APP_SECRET', ''),
            'mch_id' => env('WECHAT_MCH_ID', ''),
            'api_key' => env('WECHAT_API_KEY', ''),
            'timeout' => 30
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://thirdapi.tiptop.cn',
            'success_rate' => 95, // 95%成功率
            'delay_range' => [100, 500], // 模拟网络延迟100-500毫秒
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ],
        'oauth' => [
            'authorize_url' => 'https://open.weixin.qq.com/connect/oauth2/authorize',
            'access_token_url' => 'https://api.weixin.qq.com/sns/oauth2/access_token',
            'userinfo_url' => 'https://api.weixin.qq.com/sns/userinfo',
            'scope' => 'snsapi_userinfo'
        ],
        'pay' => [
            'unifiedorder_url' => 'https://api.mch.weixin.qq.com/pay/unifiedorder',
            'orderquery_url' => 'https://api.mch.weixin.qq.com/pay/orderquery',
            'notify_url' => 'https://api.mch.weixin.qq.com/pay/notify',
            'refund_url' => 'https://api.mch.weixin.qq.com/secapi/pay/refund'
        ]
    ],
    
    // 支付宝服务配置 - 🚨 仅模拟，不发起真实请求
    'alipay' => [
        'name' => '支付宝服务',
        'description' => '支付宝统一收单、退款查询等服务模拟',
        'business_types' => ['payment', 'refund', 'query', 'notify'],
        'real_api' => [
            'app_id' => env('ALIPAY_APP_ID', ''),
            'private_key' => env('ALIPAY_PRIVATE_KEY', ''),
            'public_key' => env('ALIPAY_PUBLIC_KEY', ''),
            'gateway_url' => 'https://openapi.alipay.com/gateway.do',
            'timeout' => 30
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://thirdapi.tiptop.cn',
            'success_rate' => 98, // 98%成功率
            'delay_range' => [80, 300], // 模拟网络延迟80-300毫秒
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ]
    ],
    
    // 短信服务配置 - 🚨 仅模拟，不发起真实请求
    'sms' => [
        'name' => '短信服务',
        'description' => '阿里云、腾讯云短信服务模拟',
        'business_types' => ['verification_code', 'notification', 'marketing'],
        'real_api' => [
            'aliyun' => [
                'access_key_id' => env('ALIYUN_ACCESS_KEY_ID', ''),
                'access_key_secret' => env('ALIYUN_ACCESS_KEY_SECRET', ''),
                'sign_name' => env('ALIYUN_SMS_SIGN_NAME', 'AI视频创作工具'),
                'template_code' => env('ALIYUN_SMS_TEMPLATE_CODE', ''),
                'timeout' => 30
            ],
            'tencent' => [
                'secret_id' => env('TENCENT_SECRET_ID', ''),
                'secret_key' => env('TENCENT_SECRET_KEY', ''),
                'sdk_app_id' => env('TENCENT_SMS_SDK_APP_ID', ''),
                'sign_name' => env('TENCENT_SMS_SIGN_NAME', 'AI视频创作工具'),
                'template_id' => env('TENCENT_SMS_TEMPLATE_ID', ''),
                'timeout' => 30
            ]
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://thirdapi.tiptop.cn',
            'success_rate' => 96, // 96%成功率
            'delay_range' => [200, 800], // 模拟网络延迟200-800毫秒
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ]
    ],
    
    // 邮件服务配置 - 🚨 仅模拟，不发起真实请求
    'email' => [
        'name' => '邮件服务',
        'description' => 'SMTP、SendCloud等邮件服务模拟',
        'business_types' => ['verification_email', 'notification_email', 'marketing_email'],
        'real_api' => [
            'smtp' => [
                'host' => env('SMTP_HOST', ''),
                'port' => env('SMTP_PORT', 587),
                'username' => env('SMTP_USERNAME', ''),
                'password' => env('SMTP_PASSWORD', ''),
                'encryption' => env('SMTP_ENCRYPTION', 'tls'),
                'from_email' => env('SMTP_FROM_EMAIL', '<EMAIL>'),
                'from_name' => env('SMTP_FROM_NAME', 'AI视频创作工具'),
                'timeout' => 30
            ],
            'sendcloud' => [
                'api_user' => env('SENDCLOUD_API_USER', ''),
                'api_key' => env('SENDCLOUD_API_KEY', ''),
                'from_email' => env('SENDCLOUD_FROM_EMAIL', '<EMAIL>'),
                'from_name' => env('SENDCLOUD_FROM_NAME', 'AI视频创作工具'),
                'timeout' => 30
            ]
        ],
        'mock_api' => [
            'enabled' => true,
            'base_url' => 'https://thirdapi.tiptop.cn',
            'success_rate' => 99, // 99%成功率
            'delay_range' => [300, 1000], // 模拟网络延迟300-1000毫秒
            'simulate_real_behavior' => true,
            'no_real_requests' => true // 🚨 确认：不发起真实请求
        ]
    ]
];

// 模拟响应数据配置
$mockResponseConfig = [
    // 微信用户信息模拟数据
    'wechat_userinfo' => [
        'openid' => 'mock_openid_',
        'nickname' => ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
        'sex' => [1, 2], // 1男性，2女性
        'province' => ['北京', '上海', '广东', '浙江', '江苏', '四川', '湖北', '湖南'],
        'city' => ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '成都市', '武汉市'],
        'country' => '中国',
        'headimgurl' => 'https://thirdqq.qlogo.cn/mmopen/mock_avatar_'
    ],
    
    // 支付订单模拟数据
    'payment_orders' => [
        'trade_status' => ['TRADE_SUCCESS', 'TRADE_CLOSED', 'WAIT_BUYER_PAY', 'TRADE_FINISHED'],
        'payment_methods' => ['balance', 'bankCard', 'creditCard', 'debitCard'],
        'banks' => ['ICBC', 'CCB', 'ABC', 'BOC', 'CMB', 'SPDB', 'CIB', 'CMBC']
    ],
    
    // 短信验证码模拟数据
    'sms_codes' => [
        'length' => 6,
        'expire_time' => 300, // 5分钟过期
        'daily_limit' => 10 // 每日限制发送次数
    ],
    
    // 邮件模拟数据
    'email_templates' => [
        'verification' => [
            'subject' => '【AI视频创作工具】邮箱验证',
            'template' => '您的验证码是：{code}，有效期5分钟。'
        ],
        'password_reset' => [
            'subject' => '【AI视频创作工具】密码重置',
            'template' => '您的密码重置验证码是：{code}，有效期10分钟。'
        ],
        'notification' => [
            'subject' => '【AI视频创作工具】系统通知',
            'template' => '尊敬的用户，{content}'
        ]
    ]
];

// 错误码配置
$errorCodes = [
    // 通用错误码
    'SUCCESS' => ['code' => 0, 'message' => '成功'],
    'INVALID_PARAMS' => ['code' => 1001, 'message' => '参数错误'],
    'MISSING_PARAMS' => ['code' => 1002, 'message' => '缺少必需参数'],
    'INVALID_SIGNATURE' => ['code' => 1003, 'message' => '签名验证失败'],
    'SERVICE_UNAVAILABLE' => ['code' => 1004, 'message' => '服务暂时不可用'],
    'RATE_LIMIT_EXCEEDED' => ['code' => 1005, 'message' => '请求频率超限'],
    
    // 微信相关错误码
    'WECHAT_INVALID_CODE' => ['code' => 2001, 'message' => '微信授权码无效'],
    'WECHAT_ACCESS_TOKEN_EXPIRED' => ['code' => 2002, 'message' => '微信访问令牌已过期'],
    'WECHAT_PAYMENT_FAILED' => ['code' => 2003, 'message' => '微信支付失败'],
    'WECHAT_REFUND_FAILED' => ['code' => 2004, 'message' => '微信退款失败'],
    
    // 支付宝相关错误码
    'ALIPAY_INVALID_PARAMS' => ['code' => 3001, 'message' => '支付宝参数错误'],
    'ALIPAY_PAYMENT_FAILED' => ['code' => 3002, 'message' => '支付宝支付失败'],
    'ALIPAY_REFUND_FAILED' => ['code' => 3003, 'message' => '支付宝退款失败'],
    
    // 短信相关错误码
    'SMS_SEND_FAILED' => ['code' => 4001, 'message' => '短信发送失败'],
    'SMS_CODE_EXPIRED' => ['code' => 4002, 'message' => '短信验证码已过期'],
    'SMS_CODE_INVALID' => ['code' => 4003, 'message' => '短信验证码无效'],
    'SMS_DAILY_LIMIT' => ['code' => 4004, 'message' => '短信发送次数超出日限制'],
    
    // 邮件相关错误码
    'EMAIL_SEND_FAILED' => ['code' => 5001, 'message' => '邮件发送失败'],
    'EMAIL_INVALID_ADDRESS' => ['code' => 5002, 'message' => '邮箱地址无效'],
    'EMAIL_TEMPLATE_NOT_FOUND' => ['code' => 5003, 'message' => '邮件模板不存在']
];

// 模拟业务状态配置
$mockBusinessStates = [
    'payment_states' => ['pending', 'processing', 'success', 'failed', 'cancelled', 'refunded'],
    'oauth_states' => ['pending', 'authorized', 'failed', 'expired'],
    'sms_states' => ['sending', 'sent', 'failed', 'delivered'],
    'email_states' => ['queued', 'sending', 'sent', 'failed', 'delivered', 'bounced'],
    'progress_steps' => [
        'initializing' => '初始化中...',
        'validating' => '验证参数中...',
        'processing' => '处理中...',
        'completing' => '完成中...'
    ],
    'failure_reasons' => [
        'network_error' => '网络错误',
        'service_unavailable' => '服务不可用',
        'invalid_credentials' => '凭证无效',
        'quota_exceeded' => '配额超限',
        'timeout' => '请求超时',
        'system_error' => '系统错误'
    ]
];

// 第三方服务错误码配置 - 模拟真实平台的错误响应
$thirdPartyErrorCodes = [
    // 通用错误码
    'SUCCESS' => ['code' => 0, 'message' => '成功'],
    'INVALID_PARAMS' => ['code' => 1001, 'message' => '参数错误'],
    'MISSING_PARAMS' => ['code' => 1002, 'message' => '缺少必需参数'],
    'INVALID_SIGNATURE' => ['code' => 1003, 'message' => '签名验证失败'],
    'SERVICE_UNAVAILABLE' => ['code' => 1004, 'message' => '服务暂时不可用'],
    'RATE_LIMIT_EXCEEDED' => ['code' => 1005, 'message' => '请求频率超限'],

    // 微信特定错误码
    'WECHAT_INVALID_CODE' => ['code' => 40029, 'message' => 'code无效'],
    'WECHAT_ACCESS_TOKEN_EXPIRED' => ['code' => 42001, 'message' => 'access_token超时'],
    'WECHAT_PAYMENT_FAILED' => ['code' => 'ORDERPAID', 'message' => '商户订单已支付'],
    'WECHAT_REFUND_FAILED' => ['code' => 'NOTENOUGH', 'message' => '余额不足'],

    // 支付宝特定错误码
    'ALIPAY_INVALID_PARAMS' => ['code' => 'INVALID_PARAMETER', 'message' => '缺少必选参数'],
    'ALIPAY_PAYMENT_FAILED' => ['code' => 'ACQ.TRADE_HAS_SUCCESS', 'message' => '交易已被支付'],
    'ALIPAY_REFUND_FAILED' => ['code' => 'ACQ.TRADE_NOT_EXIST', 'message' => '交易不存在'],

    // 短信特定错误码
    'SMS_SEND_FAILED' => ['code' => 'isv.SMS_SIGNATURE_ILLEGAL', 'message' => '短信签名不合法'],
    'SMS_CODE_EXPIRED' => ['code' => 'isv.SMS_TEMPLATE_ILLEGAL', 'message' => '短信模板不合法'],
    'SMS_DAILY_LIMIT' => ['code' => 'isv.DAY_LIMIT_CONTROL', 'message' => '触发日发送限额'],

    // 邮件特定错误码
    'EMAIL_SEND_FAILED' => ['code' => 'SMTP_ERROR', 'message' => 'SMTP发送失败'],
    'EMAIL_INVALID_ADDRESS' => ['code' => 'INVALID_EMAIL', 'message' => '邮箱地址格式错误'],
    'EMAIL_TEMPLATE_NOT_FOUND' => ['code' => 'TEMPLATE_NOT_FOUND', 'message' => '邮件模板不存在']
];

// 导出配置到全局变量
$GLOBALS['thirdapi_config'] = [
    'platforms' => $thirdPartyConfig,
    'mock_response_data' => $mockResponseConfig,
    'error_codes' => $thirdPartyErrorCodes,
    'mock_business_states' => $mockBusinessStates
];
