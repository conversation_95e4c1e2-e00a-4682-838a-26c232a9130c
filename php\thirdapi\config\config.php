<?php
/**
 * 第三方服务集成模拟返回数据服务 - 配置文件
 * 定义所有第三方服务的模拟配置参数
 */

// 基础配置
define('THIRD_API_VERSION', '1.0.0');
define('THIRD_API_NAME', '第三方服务集成模拟返回数据服务');

// 日志配置
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARN, ERROR
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_FILES', 30); // 保留30天的日志文件

// 缓存配置
define('CACHE_ENABLED', true);
define('CACHE_PATH', __DIR__ . '/../cache/');
define('CACHE_TTL', 3600); // 缓存1小时

// 性能配置
define('PERFORMANCE_MONITORING', true);
define('SLOW_REQUEST_THRESHOLD', 1000); // 慢请求阈值（毫秒）

// 第三方服务模拟配置
$thirdPartyConfig = [
    // 微信服务配置
    'wechat' => [
        'enabled' => true,
        'app_id' => 'mock_wx_app_id_123456',
        'app_secret' => 'mock_wx_app_secret_abcdef',
        'mch_id' => 'mock_wx_mch_id_789012',
        'api_key' => 'mock_wx_api_key_ghijkl',
        'success_rate' => 95, // 成功率百分比
        'delay_range' => [100, 500], // 延迟范围（毫秒）
        'oauth' => [
            'authorize_url' => 'https://open.weixin.qq.com/connect/oauth2/authorize',
            'access_token_url' => 'https://api.weixin.qq.com/sns/oauth2/access_token',
            'userinfo_url' => 'https://api.weixin.qq.com/sns/userinfo',
            'scope' => 'snsapi_userinfo'
        ],
        'pay' => [
            'unifiedorder_url' => 'https://api.mch.weixin.qq.com/pay/unifiedorder',
            'orderquery_url' => 'https://api.mch.weixin.qq.com/pay/orderquery',
            'notify_url' => 'https://api.mch.weixin.qq.com/pay/notify',
            'refund_url' => 'https://api.mch.weixin.qq.com/secapi/pay/refund'
        ]
    ],
    
    // 支付宝服务配置
    'alipay' => [
        'enabled' => true,
        'app_id' => 'mock_alipay_app_id_2021001234567890',
        'private_key' => 'mock_alipay_private_key',
        'public_key' => 'mock_alipay_public_key',
        'success_rate' => 98, // 成功率百分比
        'delay_range' => [80, 300], // 延迟范围（毫秒）
        'gateway_url' => 'https://openapi.alipay.com/gateway.do',
        'notify_url' => 'https://openapi.alipay.com/gateway.do'
    ],
    
    // 短信服务配置
    'sms' => [
        'enabled' => true,
        'providers' => [
            'aliyun' => [
                'access_key_id' => 'mock_aliyun_access_key_id',
                'access_key_secret' => 'mock_aliyun_access_key_secret',
                'sign_name' => 'AI视频创作工具',
                'template_code' => 'SMS_123456789',
                'success_rate' => 96,
                'delay_range' => [200, 800]
            ],
            'tencent' => [
                'secret_id' => 'mock_tencent_secret_id',
                'secret_key' => 'mock_tencent_secret_key',
                'sdk_app_id' => 'mock_tencent_sdk_app_id',
                'sign_name' => 'AI视频创作工具',
                'template_id' => 'mock_template_id',
                'success_rate' => 94,
                'delay_range' => [150, 600]
            ]
        ]
    ],
    
    // 邮件服务配置
    'email' => [
        'enabled' => true,
        'providers' => [
            'smtp' => [
                'host' => 'smtp.example.com',
                'port' => 587,
                'username' => 'mock_smtp_username',
                'password' => 'mock_smtp_password',
                'encryption' => 'tls',
                'from_email' => '<EMAIL>',
                'from_name' => 'AI视频创作工具',
                'success_rate' => 99,
                'delay_range' => [300, 1000]
            ],
            'sendcloud' => [
                'api_user' => 'mock_sendcloud_api_user',
                'api_key' => 'mock_sendcloud_api_key',
                'from_email' => '<EMAIL>',
                'from_name' => 'AI视频创作工具',
                'success_rate' => 97,
                'delay_range' => [200, 700]
            ]
        ]
    ]
];

// 模拟响应数据配置
$mockResponseConfig = [
    // 微信用户信息模拟数据
    'wechat_userinfo' => [
        'openid' => 'mock_openid_',
        'nickname' => ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
        'sex' => [1, 2], // 1男性，2女性
        'province' => ['北京', '上海', '广东', '浙江', '江苏', '四川', '湖北', '湖南'],
        'city' => ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '成都市', '武汉市'],
        'country' => '中国',
        'headimgurl' => 'https://thirdqq.qlogo.cn/mmopen/mock_avatar_'
    ],
    
    // 支付订单模拟数据
    'payment_orders' => [
        'trade_status' => ['TRADE_SUCCESS', 'TRADE_CLOSED', 'WAIT_BUYER_PAY', 'TRADE_FINISHED'],
        'payment_methods' => ['balance', 'bankCard', 'creditCard', 'debitCard'],
        'banks' => ['ICBC', 'CCB', 'ABC', 'BOC', 'CMB', 'SPDB', 'CIB', 'CMBC']
    ],
    
    // 短信验证码模拟数据
    'sms_codes' => [
        'length' => 6,
        'expire_time' => 300, // 5分钟过期
        'daily_limit' => 10 // 每日限制发送次数
    ],
    
    // 邮件模拟数据
    'email_templates' => [
        'verification' => [
            'subject' => '【AI视频创作工具】邮箱验证',
            'template' => '您的验证码是：{code}，有效期5分钟。'
        ],
        'password_reset' => [
            'subject' => '【AI视频创作工具】密码重置',
            'template' => '您的密码重置验证码是：{code}，有效期10分钟。'
        ],
        'notification' => [
            'subject' => '【AI视频创作工具】系统通知',
            'template' => '尊敬的用户，{content}'
        ]
    ]
];

// 错误码配置
$errorCodes = [
    // 通用错误码
    'SUCCESS' => ['code' => 0, 'message' => '成功'],
    'INVALID_PARAMS' => ['code' => 1001, 'message' => '参数错误'],
    'MISSING_PARAMS' => ['code' => 1002, 'message' => '缺少必需参数'],
    'INVALID_SIGNATURE' => ['code' => 1003, 'message' => '签名验证失败'],
    'SERVICE_UNAVAILABLE' => ['code' => 1004, 'message' => '服务暂时不可用'],
    'RATE_LIMIT_EXCEEDED' => ['code' => 1005, 'message' => '请求频率超限'],
    
    // 微信相关错误码
    'WECHAT_INVALID_CODE' => ['code' => 2001, 'message' => '微信授权码无效'],
    'WECHAT_ACCESS_TOKEN_EXPIRED' => ['code' => 2002, 'message' => '微信访问令牌已过期'],
    'WECHAT_PAYMENT_FAILED' => ['code' => 2003, 'message' => '微信支付失败'],
    'WECHAT_REFUND_FAILED' => ['code' => 2004, 'message' => '微信退款失败'],
    
    // 支付宝相关错误码
    'ALIPAY_INVALID_PARAMS' => ['code' => 3001, 'message' => '支付宝参数错误'],
    'ALIPAY_PAYMENT_FAILED' => ['code' => 3002, 'message' => '支付宝支付失败'],
    'ALIPAY_REFUND_FAILED' => ['code' => 3003, 'message' => '支付宝退款失败'],
    
    // 短信相关错误码
    'SMS_SEND_FAILED' => ['code' => 4001, 'message' => '短信发送失败'],
    'SMS_CODE_EXPIRED' => ['code' => 4002, 'message' => '短信验证码已过期'],
    'SMS_CODE_INVALID' => ['code' => 4003, 'message' => '短信验证码无效'],
    'SMS_DAILY_LIMIT' => ['code' => 4004, 'message' => '短信发送次数超出日限制'],
    
    // 邮件相关错误码
    'EMAIL_SEND_FAILED' => ['code' => 5001, 'message' => '邮件发送失败'],
    'EMAIL_INVALID_ADDRESS' => ['code' => 5002, 'message' => '邮箱地址无效'],
    'EMAIL_TEMPLATE_NOT_FOUND' => ['code' => 5003, 'message' => '邮件模板不存在']
];

// 导出配置到全局变量
$GLOBALS['thirdPartyConfig'] = $thirdPartyConfig;
$GLOBALS['mockResponseConfig'] = $mockResponseConfig;
$GLOBALS['errorCodes'] = $errorCodes;
