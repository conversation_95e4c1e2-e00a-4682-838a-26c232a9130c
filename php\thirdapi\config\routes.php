<?php
/**
 * 第三方服务集成模拟返回数据服务 - 路由配置
 * 定义所有第三方服务API接口的路由规则
 */

$routes = [
    // ==================== 微信登录 API 路由 ====================
    
    // 微信OAuth授权登录 - 获取授权码
    [
        'method' => 'GET',
        'path' => 'wechat/oauth/authorize',
        'controller' => 'WechatController',
        'action' => 'oauthAuthorize',
        'description' => '微信OAuth授权登录 - 获取授权码'
    ],
    
    // 微信OAuth - 通过授权码获取访问令牌
    [
        'method' => 'GET',
        'path' => 'wechat/oauth/access_token',
        'controller' => 'WechatController',
        'action' => 'getAccessToken',
        'description' => '微信OAuth - 通过授权码获取访问令牌'
    ],
    
    // 微信OAuth - 刷新访问令牌
    [
        'method' => 'GET',
        'path' => 'wechat/oauth/refresh_token',
        'controller' => 'WechatController',
        'action' => 'refreshAccessToken',
        'description' => '微信OAuth - 刷新访问令牌'
    ],
    
    // 微信OAuth - 获取用户信息
    [
        'method' => 'GET',
        'path' => 'wechat/oauth/userinfo',
        'controller' => 'WechatController',
        'action' => 'getUserInfo',
        'description' => '微信OAuth - 获取用户信息'
    ],
    
    // 微信OAuth - 验证访问令牌
    [
        'method' => 'GET',
        'path' => 'wechat/oauth/auth',
        'controller' => 'WechatController',
        'action' => 'validateAccessToken',
        'description' => '微信OAuth - 验证访问令牌'
    ],
    
    // ==================== 微信支付 API 路由 ====================
    
    // 微信支付 - 统一下单
    [
        'method' => 'POST',
        'path' => 'wechat/pay/unifiedorder',
        'controller' => 'WechatController',
        'action' => 'unifiedOrder',
        'description' => '微信支付 - 统一下单'
    ],
    
    // 微信支付 - 查询订单
    [
        'method' => 'POST',
        'path' => 'wechat/pay/orderquery',
        'controller' => 'WechatController',
        'action' => 'orderQuery',
        'description' => '微信支付 - 查询订单'
    ],
    
    // 微信支付 - 关闭订单
    [
        'method' => 'POST',
        'path' => 'wechat/pay/closeorder',
        'controller' => 'WechatController',
        'action' => 'closeOrder',
        'description' => '微信支付 - 关闭订单'
    ],
    
    // 微信支付 - 申请退款
    [
        'method' => 'POST',
        'path' => 'wechat/pay/refund',
        'controller' => 'WechatController',
        'action' => 'refund',
        'description' => '微信支付 - 申请退款'
    ],
    
    // 微信支付 - 查询退款
    [
        'method' => 'POST',
        'path' => 'wechat/pay/refundquery',
        'controller' => 'WechatController',
        'action' => 'refundQuery',
        'description' => '微信支付 - 查询退款'
    ],
    
    // 微信支付 - 支付结果通知
    [
        'method' => 'POST',
        'path' => 'wechat/pay/notify',
        'controller' => 'WechatController',
        'action' => 'payNotify',
        'description' => '微信支付 - 支付结果通知'
    ],
    
    // ==================== 支付宝支付 API 路由 ====================
    
    // 支付宝 - 统一收单交易创建
    [
        'method' => 'POST',
        'path' => 'alipay/trade/create',
        'controller' => 'AlipayController',
        'action' => 'tradeCreate',
        'description' => '支付宝 - 统一收单交易创建'
    ],
    
    // 支付宝 - 统一收单交易支付
    [
        'method' => 'POST',
        'path' => 'alipay/trade/pay',
        'controller' => 'AlipayController',
        'action' => 'tradePay',
        'description' => '支付宝 - 统一收单交易支付'
    ],
    
    // 支付宝 - 统一收单交易查询
    [
        'method' => 'POST',
        'path' => 'alipay/trade/query',
        'controller' => 'AlipayController',
        'action' => 'tradeQuery',
        'description' => '支付宝 - 统一收单交易查询'
    ],
    
    // 支付宝 - 统一收单交易关闭
    [
        'method' => 'POST',
        'path' => 'alipay/trade/close',
        'controller' => 'AlipayController',
        'action' => 'tradeClose',
        'description' => '支付宝 - 统一收单交易关闭'
    ],
    
    // 支付宝 - 统一收单交易退款
    [
        'method' => 'POST',
        'path' => 'alipay/trade/refund',
        'controller' => 'AlipayController',
        'action' => 'tradeRefund',
        'description' => '支付宝 - 统一收单交易退款'
    ],
    
    // 支付宝 - 统一收单退款查询
    [
        'method' => 'POST',
        'path' => 'alipay/trade/fastpay/refund/query',
        'controller' => 'AlipayController',
        'action' => 'refundQuery',
        'description' => '支付宝 - 统一收单退款查询'
    ],
    
    // 支付宝 - 支付结果通知
    [
        'method' => 'POST',
        'path' => 'alipay/notify',
        'controller' => 'AlipayController',
        'action' => 'payNotify',
        'description' => '支付宝 - 支付结果通知'
    ],
    
    // ==================== 短信服务 API 路由 ====================
    
    // 阿里云短信 - 发送短信
    [
        'method' => 'POST',
        'path' => 'sms/aliyun/send',
        'controller' => 'SmsController',
        'action' => 'aliyunSend',
        'description' => '阿里云短信 - 发送短信'
    ],
    
    // 腾讯云短信 - 发送短信
    [
        'method' => 'POST',
        'path' => 'sms/tencent/send',
        'controller' => 'SmsController',
        'action' => 'tencentSend',
        'description' => '腾讯云短信 - 发送短信'
    ],
    
    // 短信验证码验证
    [
        'method' => 'POST',
        'path' => 'sms/verify',
        'controller' => 'SmsController',
        'action' => 'verifyCode',
        'description' => '短信验证码验证'
    ],
    
    // 短信发送状态查询
    [
        'method' => 'POST',
        'path' => 'sms/status/query',
        'controller' => 'SmsController',
        'action' => 'queryStatus',
        'description' => '短信发送状态查询'
    ],
    
    // ==================== 邮件服务 API 路由 ====================
    
    // SMTP邮件发送
    [
        'method' => 'POST',
        'path' => 'email/smtp/send',
        'controller' => 'EmailController',
        'action' => 'smtpSend',
        'description' => 'SMTP邮件发送'
    ],
    
    // SendCloud邮件发送
    [
        'method' => 'POST',
        'path' => 'email/sendcloud/send',
        'controller' => 'EmailController',
        'action' => 'sendcloudSend',
        'description' => 'SendCloud邮件发送'
    ],
    
    // 邮件模板发送
    [
        'method' => 'POST',
        'path' => 'email/template/send',
        'controller' => 'EmailController',
        'action' => 'templateSend',
        'description' => '邮件模板发送'
    ],
    
    // 邮件发送状态查询
    [
        'method' => 'POST',
        'path' => 'email/status/query',
        'controller' => 'EmailController',
        'action' => 'queryStatus',
        'description' => '邮件发送状态查询'
    ],
    
    // ==================== 系统管理 API 路由 ====================
    
    // 系统状态检查
    [
        'method' => 'GET',
        'path' => 'system/health',
        'controller' => 'SystemController',
        'action' => 'healthCheck',
        'description' => '系统健康状态检查'
    ],
    
    // 服务配置信息
    [
        'method' => 'GET',
        'path' => 'system/config',
        'controller' => 'SystemController',
        'action' => 'getConfig',
        'description' => '获取服务配置信息'
    ],
    
    // API接口列表
    [
        'method' => 'GET',
        'path' => 'system/routes',
        'controller' => 'SystemController',
        'action' => 'listRoutes',
        'description' => '获取所有API接口列表'
    ],
    
    // 性能统计
    [
        'method' => 'GET',
        'path' => 'system/metrics',
        'controller' => 'SystemController',
        'action' => 'getMetrics',
        'description' => '获取系统性能统计'
    ]
];
