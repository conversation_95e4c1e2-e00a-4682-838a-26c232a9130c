<?php
/**
 * 支付宝服务控制器
 * 模拟支付宝支付API接口
 */

class AlipayController
{
    private $logger;
    private $config;
    private $mockConfig;
    private $service = 'alipay';
    
    public function __construct()
    {
        global $thirdPartyConfig, $mockResponseConfig;
        $this->logger = new Logger();
        $this->config = $thirdPartyConfig[$this->service];
        $this->mockConfig = $mockResponseConfig;
    }
    
    /**
     * 支付宝 - 统一收单交易创建
     * POST /alipay/trade/create
     */
    public function tradeCreate()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['app_id', 'method', 'charset', 'sign_type', 'sign', 'timestamp', 'version', 'biz_content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 解析业务参数
            $bizContent = json_decode($data['biz_content'], true);
            if (!$bizContent) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'biz_content格式错误');
            }
            
            // 验证业务参数
            HttpHelper::validateRequiredParams($bizContent, ['out_trade_no', 'total_amount', 'subject']);
            
            // 验证方法名
            if ($data['method'] !== 'alipay.trade.create') {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'method参数错误');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAlipayErrorResponse('SYSTEM_ERROR', '系统繁忙');
            }
            
            // 生成交易号
            $tradeNo = date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric');
            
            $response = [
                'alipay_trade_create_response' => [
                    'code' => '10000',
                    'msg' => 'Success',
                    'out_trade_no' => $bizContent['out_trade_no'],
                    'trade_no' => $tradeNo
                ],
                'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'trade_create', $data, ['code' => '10000'], $duration);
            
            return HttpHelper::successResponse($response, '交易创建成功');
            
        } catch (Exception $e) {
            $this->logger->error("支付宝交易创建异常: " . $e->getMessage());
            return HttpHelper::errorResponse('ALIPAY_TRADE_CREATE_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 支付宝 - 统一收单交易支付
     * POST /alipay/trade/pay
     */
    public function tradePay()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['app_id', 'method', 'charset', 'sign_type', 'sign', 'timestamp', 'version', 'biz_content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 解析业务参数
            $bizContent = json_decode($data['biz_content'], true);
            if (!$bizContent) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'biz_content格式错误');
            }
            
            // 验证业务参数
            HttpHelper::validateRequiredParams($bizContent, ['out_trade_no', 'scene', 'auth_code', 'total_amount', 'subject']);
            
            // 验证方法名
            if ($data['method'] !== 'alipay.trade.pay') {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'method参数错误');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAlipayErrorResponse('SYSTEM_ERROR', '系统繁忙');
            }
            
            // 生成交易号
            $tradeNo = date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric');
            $buyerUserId = 'mock_buyer_' . HttpHelper::generateRandomString(16, 'numeric');
            
            $response = [
                'alipay_trade_pay_response' => [
                    'code' => '10000',
                    'msg' => 'Success',
                    'trade_no' => $tradeNo,
                    'out_trade_no' => $bizContent['out_trade_no'],
                    'buyer_logon_id' => '138****1234',
                    'buyer_pay_amount' => $bizContent['total_amount'],
                    'buyer_user_id' => $buyerUserId,
                    'card_balance' => '0.00',
                    'discountable_amount' => '0.00',
                    'gmt_payment' => date('Y-m-d H:i:s'),
                    'invoice_amount' => $bizContent['total_amount'],
                    'point_amount' => '0.00',
                    'receipt_amount' => $bizContent['total_amount'],
                    'total_amount' => $bizContent['total_amount'],
                    'trade_status' => 'TRADE_SUCCESS'
                ],
                'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'trade_pay', $data, ['code' => '10000'], $duration);
            
            return HttpHelper::successResponse($response, '支付成功');
            
        } catch (Exception $e) {
            $this->logger->error("支付宝交易支付异常: " . $e->getMessage());
            return HttpHelper::errorResponse('ALIPAY_TRADE_PAY_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 支付宝 - 统一收单交易查询
     * POST /alipay/trade/query
     */
    public function tradeQuery()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['app_id', 'method', 'charset', 'sign_type', 'sign', 'timestamp', 'version', 'biz_content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 解析业务参数
            $bizContent = json_decode($data['biz_content'], true);
            if (!$bizContent) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'biz_content格式错误');
            }
            
            // 必须提供trade_no或out_trade_no其中之一
            if (empty($bizContent['trade_no']) && empty($bizContent['out_trade_no'])) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', '缺少trade_no或out_trade_no');
            }
            
            // 验证方法名
            if ($data['method'] !== 'alipay.trade.query') {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'method参数错误');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAlipayErrorResponse('SYSTEM_ERROR', '系统繁忙');
            }
            
            // 生成模拟交易状态
            $tradeStatuses = ['WAIT_BUYER_PAY', 'TRADE_CLOSED', 'TRADE_SUCCESS', 'TRADE_FINISHED'];
            $tradeStatus = $tradeStatuses[array_rand($tradeStatuses)];
            
            $response = [
                'alipay_trade_query_response' => [
                    'code' => '10000',
                    'msg' => 'Success',
                    'trade_no' => $bizContent['trade_no'] ?? (date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric')),
                    'out_trade_no' => $bizContent['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER'),
                    'buyer_logon_id' => '138****1234',
                    'trade_status' => $tradeStatus,
                    'total_amount' => '100.00',
                    'trans_currency' => 'CNY',
                    'settle_currency' => 'CNY',
                    'settle_amount' => '100.00',
                    'pay_currency' => 'CNY',
                    'pay_amount' => '100.00',
                    'settle_trans_rate' => '1',
                    'trans_pay_rate' => '1'
                ],
                'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
            ];
            
            // 根据交易状态添加时间字段
            if (in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                $response['alipay_trade_query_response']['send_pay_date'] = date('Y-m-d H:i:s');
            }
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'trade_query', $data, ['code' => '10000'], $duration);
            
            return HttpHelper::successResponse($response, '查询成功');
            
        } catch (Exception $e) {
            $this->logger->error("支付宝交易查询异常: " . $e->getMessage());
            return HttpHelper::errorResponse('ALIPAY_TRADE_QUERY_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 支付宝 - 统一收单交易关闭
     * POST /alipay/trade/close
     */
    public function tradeClose()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['app_id', 'method', 'charset', 'sign_type', 'sign', 'timestamp', 'version', 'biz_content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 解析业务参数
            $bizContent = json_decode($data['biz_content'], true);
            if (!$bizContent) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'biz_content格式错误');
            }

            // 必须提供trade_no或out_trade_no其中之一
            if (empty($bizContent['trade_no']) && empty($bizContent['out_trade_no'])) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', '缺少trade_no或out_trade_no');
            }

            // 验证方法名
            if ($data['method'] !== 'alipay.trade.close') {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'method参数错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAlipayErrorResponse('SYSTEM_ERROR', '系统繁忙');
            }

            $response = [
                'alipay_trade_close_response' => [
                    'code' => '10000',
                    'msg' => 'Success',
                    'trade_no' => $bizContent['trade_no'] ?? (date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric')),
                    'out_trade_no' => $bizContent['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER')
                ],
                'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'trade_close', $data, ['code' => '10000'], $duration);

            return HttpHelper::successResponse($response, '交易关闭成功');

        } catch (Exception $e) {
            $this->logger->error("支付宝交易关闭异常: " . $e->getMessage());
            return HttpHelper::errorResponse('ALIPAY_TRADE_CLOSE_ERROR', $e->getMessage());
        }
    }

    /**
     * 支付宝 - 统一收单交易退款
     * POST /alipay/trade/refund
     */
    public function tradeRefund()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['app_id', 'method', 'charset', 'sign_type', 'sign', 'timestamp', 'version', 'biz_content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 解析业务参数
            $bizContent = json_decode($data['biz_content'], true);
            if (!$bizContent) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'biz_content格式错误');
            }

            // 验证业务参数
            HttpHelper::validateRequiredParams($bizContent, ['refund_amount']);

            // 必须提供trade_no或out_trade_no其中之一
            if (empty($bizContent['trade_no']) && empty($bizContent['out_trade_no'])) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', '缺少trade_no或out_trade_no');
            }

            // 验证方法名
            if ($data['method'] !== 'alipay.trade.refund') {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'method参数错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAlipayErrorResponse('SYSTEM_ERROR', '系统繁忙');
            }

            $response = [
                'alipay_trade_refund_response' => [
                    'code' => '10000',
                    'msg' => 'Success',
                    'trade_no' => $bizContent['trade_no'] ?? (date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric')),
                    'out_trade_no' => $bizContent['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER'),
                    'buyer_logon_id' => '138****1234',
                    'fund_change' => 'Y',
                    'refund_fee' => $bizContent['refund_amount'],
                    'refund_currency' => 'CNY',
                    'gmt_refund_pay' => date('Y-m-d H:i:s'),
                    'refund_detail_item_list' => [
                        [
                            'fund_channel' => 'ALIPAYACCOUNT',
                            'amount' => $bizContent['refund_amount'],
                            'real_amount' => $bizContent['refund_amount']
                        ]
                    ]
                ],
                'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'trade_refund', $data, ['code' => '10000'], $duration);

            return HttpHelper::successResponse($response, '退款成功');

        } catch (Exception $e) {
            $this->logger->error("支付宝交易退款异常: " . $e->getMessage());
            return HttpHelper::errorResponse('ALIPAY_TRADE_REFUND_ERROR', $e->getMessage());
        }
    }

    /**
     * 支付宝 - 统一收单退款查询
     * POST /alipay/trade/fastpay/refund/query
     */
    public function refundQuery()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['app_id', 'method', 'charset', 'sign_type', 'sign', 'timestamp', 'version', 'biz_content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 解析业务参数
            $bizContent = json_decode($data['biz_content'], true);
            if (!$bizContent) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'biz_content格式错误');
            }

            // 验证业务参数
            HttpHelper::validateRequiredParams($bizContent, ['out_request_no']);

            // 必须提供trade_no或out_trade_no其中之一
            if (empty($bizContent['trade_no']) && empty($bizContent['out_trade_no'])) {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', '缺少trade_no或out_trade_no');
            }

            // 验证方法名
            if ($data['method'] !== 'alipay.trade.fastpay.refund.query') {
                return HttpHelper::errorResponse('ALIPAY_INVALID_PARAMS', 'method参数错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAlipayErrorResponse('SYSTEM_ERROR', '系统繁忙');
            }

            $response = [
                'alipay_trade_fastpay_refund_query_response' => [
                    'code' => '10000',
                    'msg' => 'Success',
                    'trade_no' => $bizContent['trade_no'] ?? (date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric')),
                    'out_trade_no' => $bizContent['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER'),
                    'out_request_no' => $bizContent['out_request_no'],
                    'total_amount' => '100.00',
                    'refund_amount' => '100.00',
                    'refund_status' => 'REFUND_SUCCESS',
                    'refund_royaltys' => [],
                    'gmt_refund_pay' => date('Y-m-d H:i:s'),
                    'refund_detail_item_list' => [
                        [
                            'fund_channel' => 'ALIPAYACCOUNT',
                            'amount' => '100.00',
                            'real_amount' => '100.00'
                        ]
                    ]
                ],
                'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'refund_query', $data, ['code' => '10000'], $duration);

            return HttpHelper::successResponse($response, '退款查询成功');

        } catch (Exception $e) {
            $this->logger->error("支付宝退款查询异常: " . $e->getMessage());
            return HttpHelper::errorResponse('ALIPAY_REFUND_QUERY_ERROR', $e->getMessage());
        }
    }

    /**
     * 支付宝 - 支付结果通知
     * POST /alipay/notify
     */
    public function payNotify()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'pay_notify', $data, ['code' => 0], $duration);

            // 支付宝异步通知需要返回success
            return 'success';

        } catch (Exception $e) {
            $this->logger->error("支付宝支付通知异常: " . $e->getMessage());
            return 'fail';
        }
    }

    /**
     * 生成支付宝错误响应
     */
    private function generateAlipayErrorResponse($subCode, $subMsg)
    {
        $response = [
            'alipay_trade_create_response' => [
                'code' => '40004',
                'msg' => 'Business Failed',
                'sub_code' => $subCode,
                'sub_msg' => $subMsg
            ],
            'sign' => 'mock_alipay_sign_' . HttpHelper::generateRandomString(32)
        ];

        return HttpHelper::errorResponse('ALIPAY_BUSINESS_ERROR', $subMsg, $response);
    }
}
