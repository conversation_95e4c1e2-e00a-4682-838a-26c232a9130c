<?php
/**
 * 邮件服务控制器
 *
 * 🚨 架构边界规范：
 * ✅ 本控制器仅进行模拟，不会向真实邮件平台发起任何网络请求
 * ✅ 严格按照邮件平台官方API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实用户数据，不执行真实业务操作
 *
 * 业务职责：SMTP、SendCloud等邮件服务模拟
 * 支持功能：验证邮件、通知邮件、营销邮件等
 */

class EmailController
{
    private $logger;
    private $config;
    private $mockData;
    private $errorCodes;
    private $service = 'email';

    public function __construct()
    {
        global $thirdapi_config;
        $this->logger = new Logger();
        $this->config = $thirdapi_config['platforms'][$this->service];
        $this->mockData = $thirdapi_config['mock_response_data'];
        $this->errorCodes = $thirdapi_config['error_codes'];
    }
    
    /**
     * SMTP邮件发送
     * POST /email/smtp/send
     */
    public function smtpSend()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['to', 'subject', 'content'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 验证邮箱格式
            $toEmails = is_array($data['to']) ? $data['to'] : [$data['to']];
            foreach ($toEmails as $email) {
                HttpHelper::validateParamFormat($email, 'email', 'to');
            }
            
            // 验证发件人邮箱（如果提供）
            if (!empty($data['from'])) {
                HttpHelper::validateParamFormat($data['from'], 'email', 'from');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            $provider = 'smtp';
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('EMAIL_SEND_FAILED', 'SMTP服务器连接失败');
            }
            
            // 生成邮件ID
            $messageId = 'mock_smtp_' . date('YmdHis') . '_' . HttpHelper::generateRandomString(16);
            
            $response = [
                'message_id' => $messageId,
                'status' => 'sent',
                'to' => $toEmails,
                'subject' => $data['subject'],
                'sent_time' => date('Y-m-d H:i:s'),
                'provider' => 'smtp'
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'smtp_send', $data, ['status' => 'sent'], $duration);
            
            return HttpHelper::successResponse($response, '邮件发送成功');
            
        } catch (Exception $e) {
            $this->logger->error("SMTP邮件发送异常: " . $e->getMessage());
            return HttpHelper::errorResponse('EMAIL_SEND_FAILED', $e->getMessage());
        }
    }
    
    /**
     * SendCloud邮件发送
     * POST /email/sendcloud/send
     */
    public function sendcloudSend()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['apiUser', 'apiKey', 'to', 'subject', 'html'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 验证邮箱格式
            $toEmails = explode(';', $data['to']);
            foreach ($toEmails as $email) {
                HttpHelper::validateParamFormat(trim($email), 'email', 'to');
            }
            
            // 验证发件人邮箱
            if (!empty($data['from'])) {
                HttpHelper::validateParamFormat($data['from'], 'email', 'from');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            $provider = 'sendcloud';
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateSendCloudErrorResponse(40001, 'API用户不存在');
            }
            
            // 生成邮件ID
            $emailId = 'mock_sendcloud_' . date('YmdHis') . '_' . HttpHelper::generateRandomString(16);
            
            $response = [
                'result' => true,
                'statusCode' => 200,
                'message' => '请求成功',
                'info' => [
                    'emailIdList' => [$emailId]
                ]
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'sendcloud_send', $data, ['result' => true], $duration);
            
            return HttpHelper::successResponse($response, '邮件发送成功');
            
        } catch (Exception $e) {
            $this->logger->error("SendCloud邮件发送异常: " . $e->getMessage());
            return HttpHelper::errorResponse('EMAIL_SEND_FAILED', $e->getMessage());
        }
    }
    
    /**
     * 邮件模板发送
     * POST /email/template/send
     */
    public function templateSend()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['to', 'template', 'variables'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 验证邮箱格式
            $toEmails = is_array($data['to']) ? $data['to'] : [$data['to']];
            foreach ($toEmails as $email) {
                HttpHelper::validateParamFormat($email, 'email', 'to');
            }
            
            // 验证模板是否存在
            $templates = $this->mockConfig['email_templates'];
            if (!isset($templates[$data['template']])) {
                return HttpHelper::errorResponse('EMAIL_TEMPLATE_NOT_FOUND', '邮件模板不存在');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('EMAIL_SEND_FAILED', '邮件发送失败');
            }
            
            // 获取模板信息
            $template = $templates[$data['template']];
            
            // 替换模板变量
            $subject = $template['subject'];
            $content = $template['template'];
            
            if (is_array($data['variables'])) {
                foreach ($data['variables'] as $key => $value) {
                    $content = str_replace('{' . $key . '}', $value, $content);
                    $subject = str_replace('{' . $key . '}', $value, $subject);
                }
            }
            
            // 生成邮件ID
            $messageId = 'mock_template_' . date('YmdHis') . '_' . HttpHelper::generateRandomString(16);
            
            $response = [
                'message_id' => $messageId,
                'status' => 'sent',
                'to' => $toEmails,
                'template' => $data['template'],
                'subject' => $subject,
                'content' => $content,
                'sent_time' => date('Y-m-d H:i:s'),
                'provider' => 'template'
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'template_send', $data, ['status' => 'sent'], $duration);
            
            return HttpHelper::successResponse($response, '模板邮件发送成功');
            
        } catch (Exception $e) {
            $this->logger->error("模板邮件发送异常: " . $e->getMessage());
            return HttpHelper::errorResponse('EMAIL_SEND_FAILED', $e->getMessage());
        }
    }
    
    /**
     * 邮件发送状态查询
     * POST /email/status/query
     */
    public function queryStatus()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['message_id'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 生成模拟状态
            $statuses = ['delivered', 'bounced', 'opened', 'clicked', 'unsubscribed', 'spam'];
            $status = $statuses[array_rand($statuses)];
            
            $response = [
                'message_id' => $data['message_id'],
                'status' => $status,
                'delivered_time' => $status === 'delivered' ? date('Y-m-d H:i:s') : null,
                'opened_time' => in_array($status, ['opened', 'clicked']) ? date('Y-m-d H:i:s') : null,
                'clicked_time' => $status === 'clicked' ? date('Y-m-d H:i:s') : null,
                'bounce_reason' => $status === 'bounced' ? '邮箱不存在' : null,
                'spam_reason' => $status === 'spam' ? '被标记为垃圾邮件' : null
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'query_status', $data, ['status' => $status], $duration);
            
            return HttpHelper::successResponse($response, '状态查询成功');
            
        } catch (Exception $e) {
            $this->logger->error("邮件状态查询异常: " . $e->getMessage());
            return HttpHelper::errorResponse('EMAIL_QUERY_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 生成SendCloud错误响应
     */
    private function generateSendCloudErrorResponse($code, $message)
    {
        $response = [
            'result' => false,
            'statusCode' => $code,
            'message' => $message,
            'info' => []
        ];
        
        return HttpHelper::errorResponse('EMAIL_SEND_FAILED', $message, $response);
    }
}
