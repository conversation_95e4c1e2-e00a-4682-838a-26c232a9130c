<?php
/**
 * 短信服务控制器
 *
 * 🚨 架构边界规范：
 * ✅ 本控制器仅进行模拟，不会向真实短信平台发起任何网络请求
 * ✅ 严格按照短信平台官方API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实用户数据，不执行真实业务操作
 *
 * 业务职责：阿里云、腾讯云短信服务模拟
 * 支持功能：验证码发送、通知短信、营销短信等
 */

class SmsController
{
    private $logger;
    private $config;
    private $mockData;
    private $errorCodes;
    private $service = 'sms';

    public function __construct()
    {
        global $thirdapi_config;
        $this->logger = new Logger();
        $this->config = $thirdapi_config['platforms'][$this->service];
        $this->mockData = $thirdapi_config['mock_response_data'];
        $this->errorCodes = $thirdapi_config['error_codes'];
    }
    
    /**
     * 阿里云短信 - 发送短信
     * POST /sms/aliyun/send
     */
    public function aliyunSend()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['PhoneNumbers', 'SignName', 'TemplateCode'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 验证手机号格式
            $phoneNumbers = explode(',', $data['PhoneNumbers']);
            foreach ($phoneNumbers as $phone) {
                HttpHelper::validateParamFormat(trim($phone), 'phone', 'PhoneNumbers');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            $provider = 'aliyun';
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateAliyunErrorResponse('isv.BUSINESS_LIMIT_CONTROL', '业务限流');
            }
            
            // 生成请求ID
            $requestId = 'mock_aliyun_' . HttpHelper::generateRandomString(32);
            $bizId = date('YmdHis') . HttpHelper::generateRandomString(16, 'numeric');
            
            $response = [
                'Message' => 'OK',
                'RequestId' => $requestId,
                'BizId' => $bizId,
                'Code' => 'OK'
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'aliyun_send', $data, ['Code' => 'OK'], $duration);
            
            return HttpHelper::successResponse($response, '短信发送成功');
            
        } catch (Exception $e) {
            $this->logger->error("阿里云短信发送异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SMS_SEND_FAILED', $e->getMessage());
        }
    }
    
    /**
     * 腾讯云短信 - 发送短信
     * POST /sms/tencent/send
     */
    public function tencentSend()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['PhoneNumberSet', 'TemplateId', 'SmsSdkAppId', 'SignName'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 验证手机号格式
            if (!is_array($data['PhoneNumberSet'])) {
                return HttpHelper::errorResponse('SMS_INVALID_PARAMS', 'PhoneNumberSet必须是数组');
            }
            
            foreach ($data['PhoneNumberSet'] as $phone) {
                // 腾讯云支持国际号码，这里简化验证
                if (empty($phone)) {
                    return HttpHelper::errorResponse('SMS_INVALID_PARAMS', '手机号不能为空');
                }
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            $provider = 'tencent';
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateTencentErrorResponse('FailedOperation.ContainSensitiveWord', '内容包含敏感词');
            }
            
            // 生成响应数据
            $sendStatusSet = [];
            foreach ($data['PhoneNumberSet'] as $phone) {
                $sendStatusSet[] = [
                    'SerialNo' => 'mock_serial_' . HttpHelper::generateRandomString(16),
                    'PhoneNumber' => $phone,
                    'Fee' => 1,
                    'SessionContext' => $data['SessionContext'] ?? '',
                    'Code' => 'Ok',
                    'Message' => 'send success',
                    'IsoCode' => 'CN'
                ];
            }
            
            $response = [
                'SendStatusSet' => $sendStatusSet,
                'RequestId' => 'mock_tencent_' . HttpHelper::generateRandomString(32)
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'tencent_send', $data, ['Code' => 'Ok'], $duration);
            
            return HttpHelper::successResponse($response, '短信发送成功');
            
        } catch (Exception $e) {
            $this->logger->error("腾讯云短信发送异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SMS_SEND_FAILED', $e->getMessage());
        }
    }
    
    /**
     * 短信验证码验证
     * POST /sms/verify
     */
    public function verifyCode()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['phone', 'code'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 验证手机号格式
            HttpHelper::validateParamFormat($data['phone'], 'phone', 'phone');
            
            // 验证验证码格式
            if (!preg_match('/^\d{4,8}$/', $data['code'])) {
                return HttpHelper::errorResponse('SMS_CODE_INVALID', '验证码格式错误');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 模拟验证结果（90%成功率）
            $isValid = rand(1, 100) <= 90;
            
            if (!$isValid) {
                return HttpHelper::errorResponse('SMS_CODE_INVALID', '验证码错误或已过期');
            }
            
            $response = [
                'phone' => $data['phone'],
                'code' => $data['code'],
                'verified' => true,
                'verify_time' => date('Y-m-d H:i:s')
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'verify_code', $data, ['verified' => true], $duration);
            
            return HttpHelper::successResponse($response, '验证码验证成功');
            
        } catch (Exception $e) {
            $this->logger->error("短信验证码验证异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SMS_VERIFY_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 短信发送状态查询
     * POST /sms/status/query
     */
    public function queryStatus()
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            $requiredParams = ['biz_id'];
            HttpHelper::validateRequiredParams($data, $requiredParams);
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 生成模拟状态
            $statuses = ['DELIVERED', 'FAILED', 'UNKNOWN'];
            $status = $statuses[array_rand($statuses)];
            
            $response = [
                'biz_id' => $data['biz_id'],
                'phone' => $data['phone'] ?? '138****1234',
                'status' => $status,
                'receive_time' => date('Y-m-d H:i:s'),
                'error_code' => $status === 'FAILED' ? 'DELIVRD' : null,
                'error_message' => $status === 'FAILED' ? '用户手机号不存在' : null
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'query_status', $data, ['status' => $status], $duration);
            
            return HttpHelper::successResponse($response, '状态查询成功');
            
        } catch (Exception $e) {
            $this->logger->error("短信状态查询异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SMS_QUERY_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 生成阿里云错误响应
     */
    private function generateAliyunErrorResponse($code, $message)
    {
        $response = [
            'Message' => $message,
            'RequestId' => 'mock_aliyun_' . HttpHelper::generateRandomString(32),
            'Code' => $code
        ];
        
        return HttpHelper::errorResponse('SMS_SEND_FAILED', $message, $response);
    }
    
    /**
     * 生成腾讯云错误响应
     */
    private function generateTencentErrorResponse($code, $message)
    {
        $response = [
            'Response' => [
                'Error' => [
                    'Code' => $code,
                    'Message' => $message
                ],
                'RequestId' => 'mock_tencent_' . HttpHelper::generateRandomString(32)
            ]
        ];
        
        return HttpHelper::errorResponse('SMS_SEND_FAILED', $message, $response);
    }
}
