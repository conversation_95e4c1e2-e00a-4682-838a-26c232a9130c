<?php
/**
 * 系统管理控制器
 * 提供系统状态、配置、监控等功能
 */

class SystemController
{
    private $logger;
    private $performanceMonitor;
    
    public function __construct()
    {
        $this->logger = new Logger();
        $this->performanceMonitor = new PerformanceMonitor();
    }
    
    /**
     * 系统健康状态检查
     * GET /system/health
     */
    public function healthCheck()
    {
        $startTime = microtime(true);
        
        try {
            global $thirdPartyConfig;
            
            $health = [
                'status' => 'healthy',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => THIRD_API_VERSION,
                'uptime' => $this->getUptime(),
                'services' => []
            ];
            
            // 检查各个服务状态
            foreach ($thirdPartyConfig as $service => $config) {
                if ($config['enabled']) {
                    $health['services'][$service] = [
                        'status' => 'active',
                        'success_rate' => $config['success_rate'] . '%',
                        'delay_range' => $config['delay_range'][0] . '-' . $config['delay_range'][1] . 'ms'
                    ];
                } else {
                    $health['services'][$service] = [
                        'status' => 'disabled'
                    ];
                }
            }
            
            // 检查系统资源
            $systemMetrics = $this->performanceMonitor->getSystemMetrics();
            $health['system'] = [
                'memory_usage' => $systemMetrics['current_memory_usage'],
                'peak_memory' => $systemMetrics['peak_memory_usage'],
                'php_version' => $systemMetrics['php_version']
            ];
            
            // 检查日志目录
            $logPath = defined('LOG_PATH') ? LOG_PATH : __DIR__ . '/../logs/';
            $health['logs'] = [
                'path' => $logPath,
                'writable' => is_writable($logPath),
                'today_stats' => $this->logger->getLogStats()
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->info("系统健康检查", [
                'status' => $health['status'],
                'duration_ms' => round($duration * 1000, 2)
            ]);
            
            return HttpHelper::successResponse($health, '系统状态正常');
            
        } catch (Exception $e) {
            $this->logger->error("系统健康检查异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SYSTEM_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 获取服务配置信息
     * GET /system/config
     */
    public function getConfig()
    {
        try {
            global $thirdPartyConfig;
            
            $config = [
                'service_name' => THIRD_API_NAME,
                'version' => THIRD_API_VERSION,
                'environment' => 'development', // 模拟环境
                'services' => []
            ];
            
            // 返回服务配置（隐藏敏感信息）
            foreach ($thirdPartyConfig as $service => $serviceConfig) {
                $config['services'][$service] = [
                    'enabled' => $serviceConfig['enabled'],
                    'success_rate' => $serviceConfig['success_rate'],
                    'delay_range' => $serviceConfig['delay_range']
                ];
                
                // 隐藏敏感配置
                if (isset($serviceConfig['app_secret'])) {
                    $config['services'][$service]['app_id'] = $serviceConfig['app_id'] ?? 'mock_app_id';
                }
            }
            
            $this->logger->info("获取系统配置");
            
            return HttpHelper::successResponse($config, '配置获取成功');
            
        } catch (Exception $e) {
            $this->logger->error("获取系统配置异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SYSTEM_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 获取所有API接口列表
     * GET /system/routes
     */
    public function listRoutes()
    {
        try {
            global $routes;
            
            $routeList = [
                'total' => count($routes),
                'routes' => []
            ];
            
            // 按服务分组
            $groupedRoutes = [];
            foreach ($routes as $route) {
                $pathParts = explode('/', $route['path']);
                $service = $pathParts[0] ?? 'system';
                
                if (!isset($groupedRoutes[$service])) {
                    $groupedRoutes[$service] = [];
                }
                
                $groupedRoutes[$service][] = [
                    'method' => $route['method'],
                    'path' => '/' . $route['path'],
                    'description' => $route['description'],
                    'controller' => $route['controller'],
                    'action' => $route['action']
                ];
            }
            
            $routeList['routes'] = $groupedRoutes;
            
            $this->logger->info("获取API接口列表", ['total_routes' => count($routes)]);
            
            return HttpHelper::successResponse($routeList, '接口列表获取成功');
            
        } catch (Exception $e) {
            $this->logger->error("获取API接口列表异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SYSTEM_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 获取系统性能统计
     * GET /system/metrics
     */
    public function getMetrics()
    {
        try {
            // 获取系统指标
            $systemMetrics = $this->performanceMonitor->getSystemMetrics();
            
            // 获取今日日志统计
            $logStats = $this->logger->getLogStats();
            
            // 获取最近7天的日志统计
            $weeklyStats = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $weeklyStats[$date] = $this->logger->getLogStats($date);
            }
            
            $metrics = [
                'system' => $systemMetrics,
                'logs' => [
                    'today' => $logStats,
                    'weekly' => $weeklyStats
                ],
                'performance' => [
                    'slow_request_threshold' => defined('SLOW_REQUEST_THRESHOLD') ? SLOW_REQUEST_THRESHOLD : 1000,
                    'cache_enabled' => defined('CACHE_ENABLED') ? CACHE_ENABLED : false,
                    'performance_monitoring' => defined('PERFORMANCE_MONITORING') ? PERFORMANCE_MONITORING : false
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            $this->logger->info("获取系统性能统计");
            
            return HttpHelper::successResponse($metrics, '性能统计获取成功');
            
        } catch (Exception $e) {
            $this->logger->error("获取系统性能统计异常: " . $e->getMessage());
            return HttpHelper::errorResponse('SYSTEM_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 获取系统运行时间
     */
    private function getUptime()
    {
        // 简化的运行时间计算
        $startTime = $_SERVER['REQUEST_TIME'] ?? time();
        $uptime = time() - $startTime;
        
        $days = floor($uptime / 86400);
        $hours = floor(($uptime % 86400) / 3600);
        $minutes = floor(($uptime % 3600) / 60);
        $seconds = $uptime % 60;
        
        return sprintf('%d天 %d小时 %d分钟 %d秒', $days, $hours, $minutes, $seconds);
    }
}
