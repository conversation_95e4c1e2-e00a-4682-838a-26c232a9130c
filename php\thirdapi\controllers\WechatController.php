<?php
/**
 * 微信服务控制器
 * 模拟微信登录和支付API接口
 */

class WechatController
{
    private $logger;
    private $config;
    private $mockConfig;
    private $service = 'wechat';

    public function __construct()
    {
        global $thirdPartyConfig, $mockResponseConfig;
        $this->logger = new Logger();
        $this->config = $thirdPartyConfig[$this->service];
        $this->mockConfig = $mockResponseConfig;
    }
    
    // ==================== 微信登录相关接口 ====================
    
    /**
     * 微信OAuth授权登录 - 获取授权码
     * GET /wechat/oauth/authorize
     */
    public function oauthAuthorize()
    {
        $startTime = microtime(true);
        
        try {
            $params = $_GET;
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($params, ['appid', 'redirect_uri', 'response_type', 'scope']);
            
            // 验证参数值
            if ($params['response_type'] !== 'code') {
                return HttpHelper::errorResponse('WECHAT_INVALID_PARAMS', 'response_type必须为code');
            }
            
            if (!in_array($params['scope'], ['snsapi_base', 'snsapi_userinfo'])) {
                return HttpHelper::errorResponse('WECHAT_INVALID_PARAMS', 'scope参数无效');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('WECHAT_SERVICE_ERROR', '微信服务暂时不可用', null, 503);
            }
            
            // 生成授权码
            $code = 'mock_wx_code_' . HttpHelper::generateRandomString(16);
            $state = $params['state'] ?? '';
            
            // 构建重定向URL
            $redirectUrl = $params['redirect_uri'] . '?code=' . $code;
            if ($state) {
                $redirectUrl .= '&state=' . urlencode($state);
            }
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'oauth_authorize', $params, ['code' => 0], $duration);
            
            // 返回重定向响应
            return HttpHelper::successResponse([
                'redirect_url' => $redirectUrl,
                'code' => $code,
                'state' => $state
            ], '授权成功');
            
        } catch (Exception $e) {
            $this->logger->error("微信OAuth授权异常: " . $e->getMessage());
            return HttpHelper::errorResponse('WECHAT_OAUTH_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 微信OAuth - 通过授权码获取访问令牌
     * GET /wechat/oauth/access_token
     */
    public function getAccessToken()
    {
        $startTime = microtime(true);
        
        try {
            $params = $_GET;
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($params, ['appid', 'secret', 'code', 'grant_type']);
            
            // 验证grant_type
            if ($params['grant_type'] !== 'authorization_code') {
                return HttpHelper::errorResponse('WECHAT_INVALID_PARAMS', 'grant_type必须为authorization_code');
            }
            
            // 验证授权码格式
            if (!preg_match('/^mock_wx_code_/', $params['code'])) {
                return HttpHelper::errorResponse('WECHAT_INVALID_CODE', '授权码无效');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('WECHAT_SERVICE_ERROR', '微信服务暂时不可用', null, 503);
            }
            
            // 生成访问令牌
            $accessToken = 'mock_wx_access_token_' . HttpHelper::generateRandomString(32);
            $refreshToken = 'mock_wx_refresh_token_' . HttpHelper::generateRandomString(32);
            $openid = 'mock_openid_' . HttpHelper::generateRandomString(16);
            
            $response = [
                'access_token' => $accessToken,
                'expires_in' => 7200,
                'refresh_token' => $refreshToken,
                'openid' => $openid,
                'scope' => 'snsapi_userinfo'
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'get_access_token', $params, ['code' => 0], $duration);
            
            return HttpHelper::successResponse($response, '获取访问令牌成功');
            
        } catch (Exception $e) {
            $this->logger->error("微信获取访问令牌异常: " . $e->getMessage());
            return HttpHelper::errorResponse('WECHAT_ACCESS_TOKEN_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 微信OAuth - 刷新访问令牌
     * GET /wechat/oauth/refresh_token
     */
    public function refreshAccessToken()
    {
        $startTime = microtime(true);
        
        try {
            $params = $_GET;
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($params, ['appid', 'grant_type', 'refresh_token']);
            
            // 验证grant_type
            if ($params['grant_type'] !== 'refresh_token') {
                return HttpHelper::errorResponse('WECHAT_INVALID_PARAMS', 'grant_type必须为refresh_token');
            }
            
            // 验证refresh_token格式
            if (!preg_match('/^mock_wx_refresh_token_/', $params['refresh_token'])) {
                return HttpHelper::errorResponse('WECHAT_ACCESS_TOKEN_EXPIRED', '刷新令牌无效或已过期');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('WECHAT_SERVICE_ERROR', '微信服务暂时不可用', null, 503);
            }
            
            // 生成新的访问令牌
            $accessToken = 'mock_wx_access_token_' . HttpHelper::generateRandomString(32);
            $refreshToken = 'mock_wx_refresh_token_' . HttpHelper::generateRandomString(32);
            $openid = 'mock_openid_' . HttpHelper::generateRandomString(16);
            
            $response = [
                'access_token' => $accessToken,
                'expires_in' => 7200,
                'refresh_token' => $refreshToken,
                'openid' => $openid,
                'scope' => 'snsapi_userinfo'
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'refresh_access_token', $params, ['code' => 0], $duration);
            
            return HttpHelper::successResponse($response, '刷新访问令牌成功');
            
        } catch (Exception $e) {
            $this->logger->error("微信刷新访问令牌异常: " . $e->getMessage());
            return HttpHelper::errorResponse('WECHAT_ACCESS_TOKEN_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 微信OAuth - 获取用户信息
     * GET /wechat/oauth/userinfo
     */
    public function getUserInfo()
    {
        $startTime = microtime(true);
        
        try {
            $params = $_GET;
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($params, ['access_token', 'openid']);
            
            // 验证access_token格式
            if (!preg_match('/^mock_wx_access_token_/', $params['access_token'])) {
                return HttpHelper::errorResponse('WECHAT_ACCESS_TOKEN_EXPIRED', '访问令牌无效或已过期');
            }
            
            // 验证openid格式
            if (!preg_match('/^mock_openid_/', $params['openid'])) {
                return HttpHelper::errorResponse('WECHAT_INVALID_PARAMS', 'openid无效');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('WECHAT_SERVICE_ERROR', '微信服务暂时不可用', null, 503);
            }
            
            // 生成模拟用户信息
            $userInfo = $this->generateMockUserInfo($params['openid']);
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'get_user_info', $params, ['code' => 0], $duration);
            
            return HttpHelper::successResponse($userInfo, '获取用户信息成功');
            
        } catch (Exception $e) {
            $this->logger->error("微信获取用户信息异常: " . $e->getMessage());
            return HttpHelper::errorResponse('WECHAT_USERINFO_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 微信OAuth - 验证访问令牌
     * GET /wechat/oauth/auth
     */
    public function validateAccessToken()
    {
        $startTime = microtime(true);
        
        try {
            $params = $_GET;
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($params, ['access_token', 'openid']);
            
            // 验证access_token格式
            if (!preg_match('/^mock_wx_access_token_/', $params['access_token'])) {
                return HttpHelper::errorResponse('WECHAT_ACCESS_TOKEN_EXPIRED', '访问令牌无效或已过期');
            }
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return HttpHelper::errorResponse('WECHAT_SERVICE_ERROR', '微信服务暂时不可用', null, 503);
            }
            
            $response = [
                'errcode' => 0,
                'errmsg' => 'ok'
            ];
            
            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'validate_access_token', $params, ['code' => 0], $duration);
            
            return HttpHelper::successResponse($response, '访问令牌验证成功');
            
        } catch (Exception $e) {
            $this->logger->error("微信验证访问令牌异常: " . $e->getMessage());
            return HttpHelper::errorResponse('WECHAT_ACCESS_TOKEN_ERROR', $e->getMessage());
        }
    }
    
    // ==================== 微信支付相关接口 ====================

    /**
     * 微信支付 - 统一下单
     * POST /wechat/pay/unifiedorder
     */
    public function unifiedOrder()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['appid', 'mch_id', 'nonce_str', 'sign', 'body', 'out_trade_no', 'total_fee', 'spbill_create_ip', 'notify_url', 'trade_type'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 验证签名（简化验证）
            if (!$this->verifyWechatSign($data)) {
                return $this->generateWechatErrorXml('SIGNERROR', '签名错误');
            }

            // 验证交易类型
            $validTradeTypes = ['JSAPI', 'NATIVE', 'APP', 'MWEB'];
            if (!in_array($data['trade_type'], $validTradeTypes)) {
                return $this->generateWechatErrorXml('PARAM_ERROR', '交易类型无效');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateWechatErrorXml('SYSTEMERROR', '系统错误');
            }

            // 生成预支付交易会话标识
            $prepayId = 'wx' . date('YmdHis') . HttpHelper::generateRandomString(16);

            $responseData = [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK',
                'appid' => $data['appid'],
                'mch_id' => $data['mch_id'],
                'nonce_str' => HttpHelper::generateNonce(),
                'sign' => 'mock_sign_' . HttpHelper::generateRandomString(32),
                'result_code' => 'SUCCESS',
                'prepay_id' => $prepayId,
                'trade_type' => $data['trade_type']
            ];

            // 根据交易类型添加特定字段
            if ($data['trade_type'] === 'NATIVE') {
                $responseData['code_url'] = 'weixin://wxpay/bizpayurl?pr=' . $prepayId;
            } elseif ($data['trade_type'] === 'MWEB') {
                $responseData['mweb_url'] = 'https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=' . $prepayId;
            }

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'unified_order', $data, ['return_code' => 'SUCCESS'], $duration);

            // 返回XML格式响应
            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($responseData);

        } catch (Exception $e) {
            $this->logger->error("微信统一下单异常: " . $e->getMessage());
            return $this->generateWechatErrorXml('PARAM_ERROR', $e->getMessage());
        }
    }

    /**
     * 微信支付 - 查询订单
     * POST /wechat/pay/orderquery
     */
    public function orderQuery()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['appid', 'mch_id', 'nonce_str', 'sign'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 必须提供transaction_id或out_trade_no其中之一
            if (empty($data['transaction_id']) && empty($data['out_trade_no'])) {
                return $this->generateWechatErrorXml('PARAM_ERROR', '缺少transaction_id或out_trade_no');
            }

            // 验证签名
            if (!$this->verifyWechatSign($data)) {
                return $this->generateWechatErrorXml('SIGNERROR', '签名错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateWechatErrorXml('SYSTEMERROR', '系统错误');
            }

            // 生成模拟订单状态
            $tradeStates = ['SUCCESS', 'REFUND', 'NOTPAY', 'CLOSED', 'REVOKED', 'USERPAYING', 'PAYERROR'];
            $tradeState = $tradeStates[array_rand($tradeStates)];

            $responseData = [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK',
                'appid' => $data['appid'],
                'mch_id' => $data['mch_id'],
                'nonce_str' => HttpHelper::generateNonce(),
                'sign' => 'mock_sign_' . HttpHelper::generateRandomString(32),
                'result_code' => 'SUCCESS',
                'openid' => 'mock_openid_' . HttpHelper::generateRandomString(16),
                'is_subscribe' => 'Y',
                'trade_type' => 'JSAPI',
                'trade_state' => $tradeState,
                'bank_type' => 'CMC',
                'total_fee' => $data['total_fee'] ?? '100',
                'settlement_total_fee' => $data['total_fee'] ?? '100',
                'fee_type' => 'CNY',
                'cash_fee' => $data['total_fee'] ?? '100',
                'cash_fee_type' => 'CNY',
                'transaction_id' => $data['transaction_id'] ?? ('wx' . date('YmdHis') . HttpHelper::generateRandomString(10)),
                'out_trade_no' => $data['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER'),
                'attach' => $data['attach'] ?? '',
                'time_end' => date('YmdHis')
            ];

            // 根据交易状态添加描述
            $tradeStateDesc = [
                'SUCCESS' => '支付成功',
                'REFUND' => '转入退款',
                'NOTPAY' => '未支付',
                'CLOSED' => '已关闭',
                'REVOKED' => '已撤销（刷卡支付）',
                'USERPAYING' => '用户支付中',
                'PAYERROR' => '支付失败'
            ];
            $responseData['trade_state_desc'] = $tradeStateDesc[$tradeState];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'order_query', $data, ['return_code' => 'SUCCESS'], $duration);

            // 返回XML格式响应
            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($responseData);

        } catch (Exception $e) {
            $this->logger->error("微信查询订单异常: " . $e->getMessage());
            return $this->generateWechatErrorXml('PARAM_ERROR', $e->getMessage());
        }
    }

    /**
     * 微信支付 - 关闭订单
     * POST /wechat/pay/closeorder
     */
    public function closeOrder()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['appid', 'mch_id', 'out_trade_no', 'nonce_str', 'sign'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 验证签名
            if (!$this->verifyWechatSign($data)) {
                return $this->generateWechatErrorXml('SIGNERROR', '签名错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateWechatErrorXml('SYSTEMERROR', '系统错误');
            }

            $responseData = [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK',
                'appid' => $data['appid'],
                'mch_id' => $data['mch_id'],
                'nonce_str' => HttpHelper::generateNonce(),
                'sign' => 'mock_sign_' . HttpHelper::generateRandomString(32),
                'result_code' => 'SUCCESS',
                'result_msg' => 'OK'
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'close_order', $data, ['return_code' => 'SUCCESS'], $duration);

            // 返回XML格式响应
            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($responseData);

        } catch (Exception $e) {
            $this->logger->error("微信关闭订单异常: " . $e->getMessage());
            return $this->generateWechatErrorXml('PARAM_ERROR', $e->getMessage());
        }
    }

    /**
     * 生成模拟用户信息
     */
    private function generateMockUserInfo($openid)
    {
        $config = $this->mockConfig['wechat_userinfo'];

        return [
            'openid' => $openid,
            'nickname' => $config['nickname'][array_rand($config['nickname'])],
            'sex' => $config['sex'][array_rand($config['sex'])],
            'province' => $config['province'][array_rand($config['province'])],
            'city' => $config['city'][array_rand($config['city'])],
            'country' => $config['country'],
            'headimgurl' => $config['headimgurl'] . HttpHelper::generateRandomString(16) . '.jpg',
            'privilege' => [],
            'unionid' => 'mock_unionid_' . HttpHelper::generateRandomString(16)
        ];
    }

    /**
     * 验证微信签名（简化版）
     */
    private function verifyWechatSign($data)
    {
        // 在模拟环境中，简化签名验证
        return isset($data['sign']) && !empty($data['sign']);
    }

    /**
     * 微信支付 - 申请退款
     * POST /wechat/pay/refund
     */
    public function refund()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['appid', 'mch_id', 'nonce_str', 'sign', 'out_refund_no', 'total_fee', 'refund_fee'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 必须提供transaction_id或out_trade_no其中之一
            if (empty($data['transaction_id']) && empty($data['out_trade_no'])) {
                return $this->generateWechatErrorXml('PARAM_ERROR', '缺少transaction_id或out_trade_no');
            }

            // 验证签名
            if (!$this->verifyWechatSign($data)) {
                return $this->generateWechatErrorXml('SIGNERROR', '签名错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateWechatErrorXml('SYSTEMERROR', '系统错误');
            }

            $responseData = [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK',
                'appid' => $data['appid'],
                'mch_id' => $data['mch_id'],
                'nonce_str' => HttpHelper::generateNonce(),
                'sign' => 'mock_sign_' . HttpHelper::generateRandomString(32),
                'result_code' => 'SUCCESS',
                'transaction_id' => $data['transaction_id'] ?? ('wx' . date('YmdHis') . HttpHelper::generateRandomString(10)),
                'out_trade_no' => $data['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER'),
                'out_refund_no' => $data['out_refund_no'],
                'refund_id' => 'wx_refund_' . date('YmdHis') . HttpHelper::generateRandomString(10),
                'refund_channel' => 'ORIGINAL',
                'refund_fee' => $data['refund_fee'],
                'settlement_refund_fee' => $data['refund_fee'],
                'total_fee' => $data['total_fee'],
                'settlement_total_fee' => $data['total_fee'],
                'fee_type' => 'CNY',
                'cash_fee' => $data['total_fee'],
                'cash_refund_fee' => $data['refund_fee']
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'refund', $data, ['return_code' => 'SUCCESS'], $duration);

            // 返回XML格式响应
            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($responseData);

        } catch (Exception $e) {
            $this->logger->error("微信申请退款异常: " . $e->getMessage());
            return $this->generateWechatErrorXml('PARAM_ERROR', $e->getMessage());
        }
    }

    /**
     * 微信支付 - 查询退款
     * POST /wechat/pay/refundquery
     */
    public function refundQuery()
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            $requiredParams = ['appid', 'mch_id', 'nonce_str', 'sign'];
            HttpHelper::validateRequiredParams($data, $requiredParams);

            // 必须提供四个查询参数中的一个
            $queryParams = ['transaction_id', 'out_trade_no', 'out_refund_no', 'refund_id'];
            $hasQueryParam = false;
            foreach ($queryParams as $param) {
                if (!empty($data[$param])) {
                    $hasQueryParam = true;
                    break;
                }
            }

            if (!$hasQueryParam) {
                return $this->generateWechatErrorXml('PARAM_ERROR', '缺少查询参数');
            }

            // 验证签名
            if (!$this->verifyWechatSign($data)) {
                return $this->generateWechatErrorXml('SIGNERROR', '签名错误');
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->service)) {
                return $this->generateWechatErrorXml('SYSTEMERROR', '系统错误');
            }

            // 生成模拟退款状态
            $refundStatus = ['SUCCESS', 'REFUNDCLOSE', 'PROCESSING', 'CHANGE'];
            $status = $refundStatus[array_rand($refundStatus)];

            $responseData = [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK',
                'appid' => $data['appid'],
                'mch_id' => $data['mch_id'],
                'nonce_str' => HttpHelper::generateNonce(),
                'sign' => 'mock_sign_' . HttpHelper::generateRandomString(32),
                'result_code' => 'SUCCESS',
                'transaction_id' => $data['transaction_id'] ?? ('wx' . date('YmdHis') . HttpHelper::generateRandomString(10)),
                'out_trade_no' => $data['out_trade_no'] ?? HttpHelper::generateOrderNumber('ORDER'),
                'total_fee' => '100',
                'settlement_total_fee' => '100',
                'fee_type' => 'CNY',
                'cash_fee' => '100',
                'refund_count' => '1',
                'out_refund_no_0' => $data['out_refund_no'] ?? ('REFUND' . date('YmdHis')),
                'refund_id_0' => $data['refund_id'] ?? ('wx_refund_' . date('YmdHis') . HttpHelper::generateRandomString(10)),
                'refund_channel_0' => 'ORIGINAL',
                'refund_fee_0' => '100',
                'settlement_refund_fee_0' => '100',
                'refund_status_0' => $status,
                'refund_account_0' => 'REFUND_SOURCE_RECHARGE_FUNDS',
                'refund_recv_accout_0' => '招商银行信用卡0403',
                'refund_success_time_0' => date('Y-m-d H:i:s')
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'refund_query', $data, ['return_code' => 'SUCCESS'], $duration);

            // 返回XML格式响应
            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($responseData);

        } catch (Exception $e) {
            $this->logger->error("微信查询退款异常: " . $e->getMessage());
            return $this->generateWechatErrorXml('PARAM_ERROR', $e->getMessage());
        }
    }

    /**
     * 微信支付 - 支付结果通知
     * POST /wechat/pay/notify
     */
    public function payNotify()
    {
        $startTime = microtime(true);

        try {
            // 获取XML数据
            $xmlData = file_get_contents('php://input');
            $data = HttpHelper::xmlToArray($xmlData);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->service);

            // 生成成功响应
            $responseData = [
                'return_code' => 'SUCCESS',
                'return_msg' => 'OK'
            ];

            // 记录日志
            $duration = microtime(true) - $startTime;
            $this->logger->logThirdPartyCall($this->service, 'pay_notify', $data, ['return_code' => 'SUCCESS'], $duration);

            // 返回XML格式响应
            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($responseData);

        } catch (Exception $e) {
            $this->logger->error("微信支付通知异常: " . $e->getMessage());

            $errorData = [
                'return_code' => 'FAIL',
                'return_msg' => $e->getMessage()
            ];

            header('Content-Type: application/xml; charset=utf-8');
            return HttpHelper::arrayToXml($errorData);
        }
    }

    /**
     * 生成微信错误XML响应
     */
    private function generateWechatErrorXml($errorCode, $errorMsg)
    {
        $errorData = [
            'return_code' => 'FAIL',
            'return_msg' => $errorMsg,
            'result_code' => 'FAIL',
            'err_code' => $errorCode,
            'err_code_des' => $errorMsg
        ];

        header('Content-Type: application/xml; charset=utf-8');
        return HttpHelper::arrayToXml($errorData);
    }
}
