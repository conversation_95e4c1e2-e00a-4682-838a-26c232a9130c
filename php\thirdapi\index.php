<?php
/**
 * 第三方服务集成模拟返回数据服务 - 入口文件和路由分发
 * 根据第三方服务的API接口文档模拟接收和返回处理
 * 支持后续无缝切换到真实第三方服务API接口
 * 
 * 支持的第三方服务：
 * - 微信登录/支付
 * - 支付宝支付
 * - 短信服务
 * - 邮件服务
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置执行时间限制
set_time_limit(300); // 5分钟，足够处理第三方服务调用

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 自动加载类文件
spl_autoload_register(function ($className) {
    $directories = [
        __DIR__ . '/controllers/',
        __DIR__ . '/models/',
        __DIR__ . '/utils/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// 加载配置文件
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/routes.php';

// 初始化工具类
$logger = new Logger();
$errorHandler = new ErrorHandler();
$performanceMonitor = new PerformanceMonitor();

// 设置错误处理器
set_error_handler([$errorHandler, 'handleError']);
set_exception_handler([$errorHandler, 'handleException']);

// 开始性能监控
$performanceMonitor->start();

try {
    // 获取请求信息
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestPath = parse_url($requestUri, PHP_URL_PATH);
    
    // 移除基础路径前缀
    $basePath = '/thirdapi/';
    if (strpos($requestPath, $basePath) === 0) {
        $requestPath = substr($requestPath, strlen($basePath));
    }
    
    // 移除开头的斜杠
    $requestPath = ltrim($requestPath, '/');
    
    // 记录请求日志
    $logger->info("第三方服务API请求", [
        'method' => $requestMethod,
        'path' => $requestPath,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    // 路由匹配
    $routeFound = false;
    $response = null;
    
    foreach ($routes as $route) {
        if ($route['method'] === $requestMethod && $route['path'] === $requestPath) {
            $routeFound = true;
            
            // 实例化控制器
            $controllerClass = $route['controller'];
            if (!class_exists($controllerClass)) {
                throw new Exception("控制器类不存在: {$controllerClass}");
            }
            
            $controller = new $controllerClass();
            $action = $route['action'];
            
            if (!method_exists($controller, $action)) {
                throw new Exception("控制器方法不存在: {$controllerClass}::{$action}");
            }
            
            // 执行控制器方法
            $response = $controller->$action();
            break;
        }
    }
    
    // 如果没有找到路由
    if (!$routeFound) {
        $response = HttpHelper::errorResponse(
            'ROUTE_NOT_FOUND',
            "接口不存在: {$requestMethod} /{$requestPath}",
            [
                'available_routes' => array_map(function($route) {
                    return $route['method'] . ' /' . $route['path'] . ' - ' . $route['description'];
                }, $routes)
            ],
            404
        );
    }
    
    // 输出响应
    if (is_array($response)) {
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    } else {
        echo $response;
    }
    
} catch (Exception $e) {
    // 记录错误日志
    $logger->error("第三方服务API异常", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    // 返回错误响应
    $errorResponse = HttpHelper::errorResponse(
        'INTERNAL_ERROR',
        '服务内部错误',
        null,
        500
    );
    
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} finally {
    // 结束性能监控
    $performanceMonitor->end();
    
    // 记录性能指标
    $metrics = $performanceMonitor->getMetrics();
    $logger->info("第三方服务API性能指标", $metrics);
}
