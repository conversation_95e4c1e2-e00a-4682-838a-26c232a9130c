[2025-08-04 01:16:44] INFO: 第三方服务API请求 | Memory: 550.01 KB (Peak: 607.87 KB) | Request: 5795fe47 | IP: 127.0.0.1 | Context: {"method":"GET","path":"system\/health","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-04 01:16:44] INFO: 系统健康检查 | Memory: 610.27 KB (Peak: 658.21 KB) | Request: 5795fe47 | IP: 127.0.0.1 | Context: {"status":"healthy","duration_ms":27.61}
[2025-08-04 01:16:44] INFO: 第三方服务API性能指标 | Memory: 611.72 KB (Peak: 658.21 KB) | Request: 5795fe47 | IP: 127.0.0.1 | Context: {"execution_time_ms":39.46,"memory_usage_start":"547.57 KB","memory_usage_end":"609.05 KB","memory_usage_diff":"61.48 KB","memory_peak":"658.21 KB","checkpoints":{"end":{"time_from_start_ms":39.46,"time_from_previous_ms":39.46,"memory_usage":"609.05 KB","memory_diff":"61.48 KB","memory_peak":"658.21 KB"}}}
[2025-08-04 01:16:44] INFO: 第三方服务API请求 | Memory: 549.8 KB (Peak: 607.66 KB) | Request: d58af88f | IP: 127.0.0.1 | Context: {"method":"GET","path":"favicon.ico","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-04 01:16:44] INFO: 第三方服务API性能指标 | Memory: 585.27 KB (Peak: 633.33 KB) | Request: d58af88f | IP: 127.0.0.1 | Context: {"execution_time_ms":3.63,"memory_usage_start":"547.37 KB","memory_usage_end":"582.59 KB","memory_usage_diff":"35.23 KB","memory_peak":"633.33 KB","checkpoints":{"end":{"time_from_start_ms":3.63,"time_from_previous_ms":3.63,"memory_usage":"582.59 KB","memory_diff":"35.23 KB","memory_peak":"633.33 KB"}}}
[2025-08-04 01:16:54] INFO: 第三方服务API请求 | Memory: 551.82 KB (Peak: 609.67 KB) | Request: 19c3da6e | IP: 127.0.0.1 | Context: {"method":"GET","path":"wechat\/oauth\/authorize","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-04 01:16:54] INFO: 第三方服务调用: wechat.oauth_authorize | Memory: 689.98 KB (Peak: 855.83 KB) | Request: 19c3da6e | IP: 127.0.0.1 | Context: {"service":"wechat","action":"oauth_authorize","params":{"appid":"test_app_id","redirect_uri":"https:\/\/api.tiptop.cn\/callback","response_type":"code","scope":"snsapi_userinfo","state":"test_state"},"response_status":0,"duration_ms":299.34}
[2025-08-04 01:16:54] INFO: 第三方服务API性能指标 | Memory: 692.69 KB (Peak: 855.83 KB) | Request: 19c3da6e | IP: 127.0.0.1 | Context: {"execution_time_ms":306.86,"memory_usage_start":"549.38 KB","memory_usage_end":"690.02 KB","memory_usage_diff":"140.64 KB","memory_peak":"855.83 KB","checkpoints":{"end":{"time_from_start_ms":306.86,"time_from_previous_ms":306.86,"memory_usage":"690.02 KB","memory_diff":"140.64 KB","memory_peak":"855.83 KB"}}}
[2025-08-04 01:17:05] INFO: 第三方服务API请求 | Memory: 551.54 KB (Peak: 609.23 KB) | Request: 82fd5058 | IP: 127.0.0.1 | Context: {"method":"POST","path":"alipay\/trade\/create","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-04 01:17:05] WARN: 第三方服务调用: alipay.trade_create | Memory: 652.51 KB (Peak: 761.59 KB) | Request: 82fd5058 | IP: 127.0.0.1 | Context: {"service":"alipay","action":"trade_create","params":{"app_id":"2021001234567890","method":"alipay.trade.create","charset":"utf-8","sign_type":"RSA2","sign":"test_signature","timestamp":"2023-08-01 12:00:00","version":"1.0","biz_content":"{\"out_trade_no\":\"ORDER123456\",\"total_amount\":\"100.00\",\"subject\":\"测试商品\"}"},"response_status":"10000","duration_ms":254.81}
[2025-08-04 01:17:05] INFO: 第三方服务API性能指标 | Memory: 653.28 KB (Peak: 761.59 KB) | Request: 82fd5058 | IP: 127.0.0.1 | Context: {"execution_time_ms":260.74,"memory_usage_start":"549.09 KB","memory_usage_end":"650.61 KB","memory_usage_diff":"101.52 KB","memory_peak":"761.59 KB","checkpoints":{"end":{"time_from_start_ms":260.74,"time_from_previous_ms":260.74,"memory_usage":"650.61 KB","memory_diff":"101.52 KB","memory_peak":"761.59 KB"}}}
[2025-08-04 01:17:16] INFO: 第三方服务API请求 | Memory: 551.38 KB (Peak: 609.08 KB) | Request: 06254d4c | IP: 127.0.0.1 | Context: {"method":"POST","path":"sms\/aliyun\/send","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-04 01:17:16] INFO: 第三方服务调用: sms.aliyun_send | Memory: 621.79 KB (Peak: 679.96 KB) | Request: 06254d4c | IP: 127.0.0.1 | Context: {"service":"sms","action":"aliyun_send","params":{"PhoneNumbers":"13800138000","SignName":"AI视频创作工具","TemplateCode":"SMS_123456789","TemplateParam":"{\"code\":\"123456\"}"},"response_status":null,"duration_ms":4.59}
[2025-08-04 01:17:16] INFO: 第三方服务API性能指标 | Memory: 622.93 KB (Peak: 679.96 KB) | Request: 06254d4c | IP: 127.0.0.1 | Context: {"execution_time_ms":11.26,"memory_usage_start":"548.94 KB","memory_usage_end":"620.26 KB","memory_usage_diff":"71.32 KB","memory_peak":"679.96 KB","checkpoints":{"end":{"time_from_start_ms":11.26,"time_from_previous_ms":11.26,"memory_usage":"620.26 KB","memory_diff":"71.32 KB","memory_peak":"679.96 KB"}}}
[2025-08-04 01:17:27] INFO: 第三方服务API请求 | Memory: 549.98 KB (Peak: 607.84 KB) | Request: 3ea03539 | IP: 127.0.0.1 | Context: {"method":"GET","path":"system\/routes","ip":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-08-04 01:17:27] INFO: 获取API接口列表 | Memory: 619.86 KB (Peak: 658.18 KB) | Request: 3ea03539 | IP: 127.0.0.1 | Context: {"total_routes":30}
[2025-08-04 01:17:27] INFO: 第三方服务API性能指标 | Memory: 629.7 KB (Peak: 658.18 KB) | Request: 3ea03539 | IP: 127.0.0.1 | Context: {"execution_time_ms":8.38,"memory_usage_start":"547.54 KB","memory_usage_end":"627.02 KB","memory_usage_diff":"79.48 KB","memory_peak":"658.18 KB","checkpoints":{"end":{"time_from_start_ms":8.38,"time_from_previous_ms":8.38,"memory_usage":"627.02 KB","memory_diff":"79.48 KB","memory_peak":"658.18 KB"}}}
