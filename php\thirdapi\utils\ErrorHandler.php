<?php
/**
 * 错误处理类
 * 统一处理系统错误和异常
 */

class ErrorHandler
{
    private $logger;
    
    public function __construct()
    {
        $this->logger = new Logger();
    }
    
    /**
     * 处理PHP错误
     */
    public function handleError($severity, $message, $file, $line)
    {
        // 检查错误报告级别
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = $this->getErrorType($severity);
        
        $context = [
            'type' => $errorType,
            'severity' => $severity,
            'file' => $file,
            'line' => $line,
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
        ];
        
        $this->logger->error("PHP错误: {$message}", $context);
        
        // 对于致命错误，抛出异常
        if ($severity === E_ERROR || $severity === E_CORE_ERROR || $severity === E_COMPILE_ERROR) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        }
        
        return true;
    }
    
    /**
     * 处理未捕获的异常
     */
    public function handleException($exception)
    {
        $context = [
            'class' => get_class($exception),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ];
        
        $this->logger->error("未捕获异常: " . $exception->getMessage(), $context);
        
        // 输出错误响应
        $response = HttpHelper::errorResponse(
            'INTERNAL_ERROR',
            '服务内部错误',
            null,
            500
        );
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * 获取错误类型名称
     */
    private function getErrorType($severity)
    {
        $errorTypes = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED'
        ];
        
        return $errorTypes[$severity] ?? 'UNKNOWN';
    }
    
    /**
     * 记录业务异常
     */
    public function logBusinessException($exception, $context = [])
    {
        $logContext = array_merge([
            'class' => get_class($exception),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine()
        ], $context);
        
        $this->logger->warn("业务异常: " . $exception->getMessage(), $logContext);
    }
    
    /**
     * 记录第三方服务异常
     */
    public function logThirdPartyException($service, $exception, $context = [])
    {
        $logContext = array_merge([
            'service' => $service,
            'class' => get_class($exception),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine()
        ], $context);
        
        $this->logger->error("第三方服务异常: " . $exception->getMessage(), $logContext);
    }
}
