<?php
/**
 * HTTP工具类
 * 处理HTTP请求和响应相关功能
 */

class HttpHelper
{
    /**
     * 获取请求体数据
     */
    public static function getRequestBody()
    {
        $input = file_get_contents('php://input');
        
        // 尝试解析JSON
        if (!empty($input)) {
            $data = json_decode($input, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $data;
            }
        }
        
        // 如果不是JSON，返回POST数据
        return $_POST;
    }
    
    /**
     * 获取请求参数（GET和POST合并）
     */
    public static function getRequestParams()
    {
        $postData = self::getRequestBody();
        return array_merge($_GET, is_array($postData) ? $postData : []);
    }
    
    /**
     * 验证必需参数
     */
    public static function validateRequiredParams($data, $requiredParams)
    {
        $missingParams = [];
        
        foreach ($requiredParams as $param) {
            if (!isset($data[$param]) || $data[$param] === '' || $data[$param] === null) {
                $missingParams[] = $param;
            }
        }
        
        if (!empty($missingParams)) {
            throw new Exception("缺少必需参数: " . implode(', ', $missingParams));
        }
    }
    
    /**
     * 验证参数格式
     */
    public static function validateParamFormat($value, $type, $paramName)
    {
        switch ($type) {
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception("参数 {$paramName} 不是有效的邮箱格式");
                }
                break;
                
            case 'phone':
                if (!preg_match('/^1[3-9]\d{9}$/', $value)) {
                    throw new Exception("参数 {$paramName} 不是有效的手机号格式");
                }
                break;
                
            case 'url':
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    throw new Exception("参数 {$paramName} 不是有效的URL格式");
                }
                break;
                
            case 'numeric':
                if (!is_numeric($value)) {
                    throw new Exception("参数 {$paramName} 必须是数字");
                }
                break;
                
            case 'integer':
                if (!filter_var($value, FILTER_VALIDATE_INT)) {
                    throw new Exception("参数 {$paramName} 必须是整数");
                }
                break;
        }
    }
    
    /**
     * 生成成功响应
     */
    public static function successResponse($data = null, $message = '成功', $code = 0)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
            'data' => $data
        ];
        
        http_response_code(200);
        return $response;
    }
    
    /**
     * 生成错误响应
     */
    public static function errorResponse($errorCode, $message, $data = null, $httpCode = 400)
    {
        global $errorCodes;
        
        // 如果错误码在配置中存在，使用配置的信息
        if (isset($errorCodes[$errorCode])) {
            $code = $errorCodes[$errorCode]['code'];
            $message = $errorCodes[$errorCode]['message'];
        } else {
            $code = is_numeric($errorCode) ? $errorCode : 9999;
        }
        
        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
            'error' => $errorCode
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        http_response_code($httpCode);
        return $response;
    }
    
    /**
     * 模拟网络延迟
     */
    public static function simulateDelay($service)
    {
        global $thirdPartyConfig;
        
        if (isset($thirdPartyConfig[$service]['delay_range'])) {
            $range = $thirdPartyConfig[$service]['delay_range'];
            $delay = rand($range[0], $range[1]);
            usleep($delay * 1000); // 转换为微秒
        }
    }
    
    /**
     * 模拟成功率
     */
    public static function simulateSuccessRate($service)
    {
        global $thirdPartyConfig;
        
        if (isset($thirdPartyConfig[$service]['success_rate'])) {
            $successRate = $thirdPartyConfig[$service]['success_rate'];
            $random = rand(1, 100);
            return $random <= $successRate;
        }
        
        return true; // 默认100%成功
    }
    
    /**
     * 生成随机字符串
     */
    public static function generateRandomString($length = 32, $type = 'mixed')
    {
        switch ($type) {
            case 'numeric':
                $characters = '0123456789';
                break;
            case 'alpha':
                $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                break;
            case 'hex':
                $characters = '0123456789abcdef';
                break;
            default:
                $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        }
        
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $randomString;
    }
    
    /**
     * 生成订单号
     */
    public static function generateOrderNumber($prefix = '')
    {
        return $prefix . date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * 生成时间戳
     */
    public static function generateTimestamp()
    {
        return time();
    }
    
    /**
     * 生成nonce字符串
     */
    public static function generateNonce($length = 32)
    {
        return self::generateRandomString($length);
    }
    
    /**
     * 生成签名（简化版）
     */
    public static function generateSignature($params, $key)
    {
        ksort($params);
        $stringToBeSigned = '';
        foreach ($params as $k => $v) {
            if ($v !== '' && $k !== 'sign') {
                $stringToBeSigned .= $k . '=' . $v . '&';
            }
        }
        $stringToBeSigned .= 'key=' . $key;
        return strtoupper(md5($stringToBeSigned));
    }
    
    /**
     * 验证签名
     */
    public static function verifySignature($params, $key, $signature)
    {
        $calculatedSignature = self::generateSignature($params, $key);
        return $calculatedSignature === $signature;
    }
    
    /**
     * 获取客户端IP
     */
    public static function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * 获取User Agent
     */
    public static function getUserAgent()
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }
    
    /**
     * XML转数组
     */
    public static function xmlToArray($xml)
    {
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }
    
    /**
     * 数组转XML
     */
    public static function arrayToXml($array, $rootElement = 'xml')
    {
        $xml = new SimpleXMLElement("<{$rootElement}></{$rootElement}>");
        
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $subnode = $xml->addChild($key);
                self::arrayToXmlRecursive($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
        
        return $xml->asXML();
    }
    
    /**
     * 递归转换数组到XML
     */
    private static function arrayToXmlRecursive($array, $xml)
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $subnode = $xml->addChild($key);
                self::arrayToXmlRecursive($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }
}
