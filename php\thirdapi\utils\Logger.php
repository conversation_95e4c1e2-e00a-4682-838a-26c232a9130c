<?php
/**
 * 日志记录类
 * 提供统一的日志记录功能
 */

class Logger
{
    private $logPath;
    private $logLevel;
    private $maxFiles;
    
    // 日志级别常量
    const DEBUG = 1;
    const INFO = 2;
    const WARN = 3;
    const ERROR = 4;
    
    // 日志级别映射
    private $levelMap = [
        'DEBUG' => self::DEBUG,
        'INFO' => self::INFO,
        'WARN' => self::WARN,
        'ERROR' => self::ERROR
    ];
    
    public function __construct()
    {
        $this->logPath = defined('LOG_PATH') ? LOG_PATH : __DIR__ . '/../logs/';
        $this->logLevel = defined('LOG_LEVEL') ? $this->levelMap[LOG_LEVEL] : self::INFO;
        $this->maxFiles = defined('LOG_MAX_FILES') ? LOG_MAX_FILES : 30;
        
        // 确保日志目录存在
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
        
        // 清理过期日志文件
        $this->cleanOldLogs();
    }
    
    /**
     * 记录DEBUG级别日志
     */
    public function debug($message, $context = [])
    {
        $this->log(self::DEBUG, $message, $context);
    }
    
    /**
     * 记录INFO级别日志
     */
    public function info($message, $context = [])
    {
        $this->log(self::INFO, $message, $context);
    }
    
    /**
     * 记录WARN级别日志
     */
    public function warn($message, $context = [])
    {
        $this->log(self::WARN, $message, $context);
    }
    
    /**
     * 记录ERROR级别日志
     */
    public function error($message, $context = [])
    {
        $this->log(self::ERROR, $message, $context);
    }
    
    /**
     * 记录日志
     */
    private function log($level, $message, $context = [])
    {
        // 检查日志级别
        if ($level < $this->logLevel) {
            return;
        }
        
        // 获取级别名称
        $levelName = array_search($level, $this->levelMap);
        
        // 构建日志记录
        $logRecord = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $levelName,
            'message' => $message,
            'context' => $context,
            'memory_usage' => $this->formatBytes(memory_get_usage()),
            'memory_peak' => $this->formatBytes(memory_get_peak_usage()),
            'request_id' => $this->getRequestId(),
            'ip' => HttpHelper::getClientIp(),
            'user_agent' => HttpHelper::getUserAgent()
        ];
        
        // 格式化日志内容
        $logLine = $this->formatLogLine($logRecord);
        
        // 写入日志文件
        $this->writeToFile($logLine, $level);
    }
    
    /**
     * 格式化日志行
     */
    private function formatLogLine($record)
    {
        $contextStr = '';
        if (!empty($record['context'])) {
            $contextStr = ' | Context: ' . json_encode($record['context'], JSON_UNESCAPED_UNICODE);
        }
        
        return sprintf(
            "[%s] %s: %s | Memory: %s (Peak: %s) | Request: %s | IP: %s%s\n",
            $record['timestamp'],
            $record['level'],
            $record['message'],
            $record['memory_usage'],
            $record['memory_peak'],
            $record['request_id'],
            $record['ip'],
            $contextStr
        );
    }
    
    /**
     * 写入日志文件
     */
    private function writeToFile($logLine, $level)
    {
        // 根据级别确定文件名
        $filename = $level >= self::ERROR ? 'error' : 'thirdapi';
        $filename .= '-' . date('Y-m-d') . '.log';
        $filepath = $this->logPath . $filename;
        
        // 写入文件
        file_put_contents($filepath, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 获取请求ID
     */
    private function getRequestId()
    {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = substr(md5(uniqid(mt_rand(), true)), 0, 8);
        }
        
        return $requestId;
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 清理过期日志文件
     */
    private function cleanOldLogs()
    {
        $files = glob($this->logPath . '*.log');
        
        if (count($files) <= $this->maxFiles) {
            return;
        }
        
        // 按修改时间排序
        usort($files, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        
        // 删除最旧的文件
        $filesToDelete = array_slice($files, 0, count($files) - $this->maxFiles);
        foreach ($filesToDelete as $file) {
            unlink($file);
        }
    }
    
    /**
     * 记录API请求日志
     */
    public function logApiRequest($method, $path, $params = [], $response = null, $duration = 0)
    {
        $context = [
            'method' => $method,
            'path' => $path,
            'params' => $params,
            'response_code' => $response['code'] ?? null,
            'duration_ms' => round($duration * 1000, 2)
        ];
        
        $message = "API请求: {$method} /{$path}";
        
        if (isset($response['code']) && $response['code'] !== 0) {
            $this->warn($message, $context);
        } else {
            $this->info($message, $context);
        }
    }
    
    /**
     * 记录第三方服务调用日志
     */
    public function logThirdPartyCall($service, $action, $params = [], $response = null, $duration = 0)
    {
        $context = [
            'service' => $service,
            'action' => $action,
            'params' => $this->sanitizeParams($params),
            'response_status' => $response['code'] ?? null,
            'duration_ms' => round($duration * 1000, 2)
        ];
        
        $message = "第三方服务调用: {$service}.{$action}";
        
        if (isset($response['code']) && $response['code'] !== 0) {
            $this->warn($message, $context);
        } else {
            $this->info($message, $context);
        }
    }
    
    /**
     * 清理敏感参数
     */
    private function sanitizeParams($params)
    {
        $sensitiveKeys = [
            'password', 'passwd', 'pwd',
            'secret', 'key', 'token',
            'access_token', 'refresh_token',
            'app_secret', 'api_key',
            'private_key', 'signature'
        ];
        
        $sanitized = $params;
        
        foreach ($sensitiveKeys as $key) {
            if (isset($sanitized[$key])) {
                $value = $sanitized[$key];
                if (strlen($value) > 8) {
                    $sanitized[$key] = substr($value, 0, 4) . '****' . substr($value, -4);
                } else {
                    $sanitized[$key] = '****';
                }
            }
        }
        
        return $sanitized;
    }
    
    /**
     * 获取日志统计信息
     */
    public function getLogStats($date = null)
    {
        $date = $date ?: date('Y-m-d');
        $logFile = $this->logPath . "thirdapi-{$date}.log";
        $errorFile = $this->logPath . "error-{$date}.log";
        
        $stats = [
            'date' => $date,
            'total_requests' => 0,
            'error_count' => 0,
            'warn_count' => 0,
            'info_count' => 0,
            'debug_count' => 0,
            'file_size' => 0,
            'error_file_size' => 0
        ];
        
        // 统计主日志文件
        if (file_exists($logFile)) {
            $content = file_get_contents($logFile);
            $stats['file_size'] = filesize($logFile);
            $stats['total_requests'] = substr_count($content, 'API请求:');
            $stats['info_count'] = substr_count($content, '] INFO:');
            $stats['warn_count'] = substr_count($content, '] WARN:');
            $stats['debug_count'] = substr_count($content, '] DEBUG:');
        }
        
        // 统计错误日志文件
        if (file_exists($errorFile)) {
            $content = file_get_contents($errorFile);
            $stats['error_file_size'] = filesize($errorFile);
            $stats['error_count'] = substr_count($content, '] ERROR:');
        }
        
        return $stats;
    }
}
