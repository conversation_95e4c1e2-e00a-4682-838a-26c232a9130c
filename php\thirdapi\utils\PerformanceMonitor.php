<?php
/**
 * 性能监控类
 * 监控系统性能指标
 */

class PerformanceMonitor
{
    private $startTime;
    private $startMemory;
    private $checkpoints = [];
    
    /**
     * 开始监控
     */
    public function start()
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage();
        $this->checkpoints = [];
    }
    
    /**
     * 添加检查点
     */
    public function checkpoint($name)
    {
        $this->checkpoints[$name] = [
            'time' => microtime(true),
            'memory' => memory_get_usage(),
            'memory_peak' => memory_get_peak_usage()
        ];
    }
    
    /**
     * 结束监控
     */
    public function end()
    {
        $this->checkpoint('end');
    }
    
    /**
     * 获取性能指标
     */
    public function getMetrics()
    {
        $endTime = $this->checkpoints['end']['time'] ?? microtime(true);
        $endMemory = $this->checkpoints['end']['memory'] ?? memory_get_usage();
        
        $metrics = [
            'execution_time_ms' => round(($endTime - $this->startTime) * 1000, 2),
            'memory_usage_start' => $this->formatBytes($this->startMemory),
            'memory_usage_end' => $this->formatBytes($endMemory),
            'memory_usage_diff' => $this->formatBytes($endMemory - $this->startMemory),
            'memory_peak' => $this->formatBytes(memory_get_peak_usage()),
            'checkpoints' => []
        ];
        
        // 处理检查点
        $previousTime = $this->startTime;
        $previousMemory = $this->startMemory;
        
        foreach ($this->checkpoints as $name => $data) {
            $metrics['checkpoints'][$name] = [
                'time_from_start_ms' => round(($data['time'] - $this->startTime) * 1000, 2),
                'time_from_previous_ms' => round(($data['time'] - $previousTime) * 1000, 2),
                'memory_usage' => $this->formatBytes($data['memory']),
                'memory_diff' => $this->formatBytes($data['memory'] - $previousMemory),
                'memory_peak' => $this->formatBytes($data['memory_peak'])
            ];
            
            $previousTime = $data['time'];
            $previousMemory = $data['memory'];
        }
        
        return $metrics;
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 检查是否为慢请求
     */
    public function isSlowRequest()
    {
        $threshold = defined('SLOW_REQUEST_THRESHOLD') ? SLOW_REQUEST_THRESHOLD : 1000;
        $currentTime = microtime(true);
        $executionTime = ($currentTime - $this->startTime) * 1000;
        
        return $executionTime > $threshold;
    }
    
    /**
     * 获取系统资源使用情况
     */
    public function getSystemMetrics()
    {
        $metrics = [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'current_memory_usage' => $this->formatBytes(memory_get_usage()),
            'peak_memory_usage' => $this->formatBytes(memory_get_peak_usage()),
            'memory_usage_real' => $this->formatBytes(memory_get_usage(true)),
            'peak_memory_usage_real' => $this->formatBytes(memory_get_peak_usage(true))
        ];
        
        // 获取系统负载（仅Linux）
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $metrics['system_load'] = [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }
        
        // 获取磁盘使用情况
        $diskTotal = disk_total_space('.');
        $diskFree = disk_free_space('.');
        if ($diskTotal && $diskFree) {
            $metrics['disk_usage'] = [
                'total' => $this->formatBytes($diskTotal),
                'free' => $this->formatBytes($diskFree),
                'used' => $this->formatBytes($diskTotal - $diskFree),
                'usage_percent' => round((($diskTotal - $diskFree) / $diskTotal) * 100, 2)
            ];
        }
        
        return $metrics;
    }
}
