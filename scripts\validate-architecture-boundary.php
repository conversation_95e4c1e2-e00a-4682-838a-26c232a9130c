<?php
/**
 * 架构边界验证脚本
 * 
 * 🚨 基于 index-new.mdc 架构规范验证模拟服务边界
 * 
 * 验证内容：
 * 1. 确认所有控制器都有正确的架构边界注释
 * 2. 确认配置文件中的 no_real_requests 标记
 * 3. 确认没有真实的网络请求代码
 * 4. 确认模拟数据配置完整性
 */

class ArchitectureBoundaryValidator
{
    private $errors = [];
    private $warnings = [];
    
    public function validate()
    {
        echo "🚨 开始架构边界验证...\n\n";
        
        // 验证 aiapi 项目
        $this->validateAiApiProject();
        
        // 验证 thirdapi 项目
        $this->validateThirdApiProject();
        
        // 输出结果
        $this->outputResults();
    }
    
    /**
     * 验证 aiapi 项目
     */
    private function validateAiApiProject()
    {
        echo "📁 验证 aiapi 项目...\n";
        
        $controllersPath = 'php/aiapi/controllers';
        $configPath = 'php/aiapi/config/config.php';
        
        // 验证控制器
        $controllers = [
            'DeepSeekController.php',
            'LiblibController.php', 
            'KlingController.php',
            'MiniMaxController.php',
            'VolcengineController.php'
        ];
        
        foreach ($controllers as $controller) {
            $this->validateController($controllersPath . '/' . $controller, 'aiapi');
        }
        
        // 验证配置文件
        $this->validateConfig($configPath, 'aiapi');
    }
    
    /**
     * 验证 thirdapi 项目
     */
    private function validateThirdApiProject()
    {
        echo "📁 验证 thirdapi 项目...\n";
        
        $controllersPath = 'php/thirdapi/controllers';
        $configPath = 'php/thirdapi/config/config.php';
        
        // 验证控制器
        $controllers = [
            'WechatController.php',
            'AlipayController.php',
            'SmsController.php',
            'EmailController.php'
        ];
        
        foreach ($controllers as $controller) {
            $this->validateController($controllersPath . '/' . $controller, 'thirdapi');
        }
        
        // 验证配置文件
        $this->validateConfig($configPath, 'thirdapi');
    }
    
    /**
     * 验证控制器文件
     */
    private function validateController($filePath, $project)
    {
        if (!file_exists($filePath)) {
            $this->errors[] = "❌ 文件不存在: {$filePath}";
            return;
        }
        
        $content = file_get_contents($filePath);
        $fileName = basename($filePath);
        
        // 检查架构边界注释
        if (!$this->hasArchitectureBoundaryComment($content)) {
            $this->errors[] = "❌ {$fileName}: 缺少架构边界规范注释";
        } else {
            echo "✅ {$fileName}: 架构边界注释正确\n";
        }
        
        // 检查是否有真实网络请求代码
        if ($this->hasRealNetworkRequests($content)) {
            $this->errors[] = "❌ {$fileName}: 发现真实网络请求代码";
        } else {
            echo "✅ {$fileName}: 无真实网络请求代码\n";
        }
        
        // 检查构造函数是否使用新的配置结构
        if (!$this->hasCorrectConstructor($content, $project)) {
            $this->warnings[] = "⚠️ {$fileName}: 构造函数可能需要更新";
        } else {
            echo "✅ {$fileName}: 构造函数结构正确\n";
        }
    }
    
    /**
     * 验证配置文件
     */
    private function validateConfig($filePath, $project)
    {
        if (!file_exists($filePath)) {
            $this->errors[] = "❌ 配置文件不存在: {$filePath}";
            return;
        }
        
        $content = file_get_contents($filePath);
        
        // 检查架构边界注释
        if (!$this->hasArchitectureBoundaryComment($content)) {
            $this->errors[] = "❌ 配置文件: 缺少架构边界规范注释";
        } else {
            echo "✅ 配置文件: 架构边界注释正确\n";
        }
        
        // 检查 no_real_requests 标记
        if (!strpos($content, 'no_real_requests') !== false) {
            $this->warnings[] = "⚠️ 配置文件: 建议添加 no_real_requests 标记";
        } else {
            echo "✅ 配置文件: no_real_requests 标记存在\n";
        }
    }
    
    /**
     * 检查是否有架构边界注释
     */
    private function hasArchitectureBoundaryComment($content)
    {
        $patterns = [
            '/🚨 架构边界规范/',
            '/不会向真实.*平台发起任何网络请求/',
            '/仅进行模拟/',
            '/不产生任何真实费用/'
        ];
        
        foreach ($patterns as $pattern) {
            if (!preg_match($pattern, $content)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否有真实网络请求代码
     */
    private function hasRealNetworkRequests($content)
    {
        $suspiciousPatterns = [
            '/curl_exec\s*\(\s*\$/',
            '/file_get_contents\s*\(\s*["\']https?:\/\//',
            '/HttpClient::post\s*\(\s*["\']https?:\/\/(?!.*tiptop\.cn)/',
            '/guzzle.*->request/',
            '/wp_remote_post\s*\(\s*["\']https?:\/\/(?!.*tiptop\.cn)/'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查构造函数是否正确
     */
    private function hasCorrectConstructor($content, $project)
    {
        $configVar = $project === 'aiapi' ? 'aiapi_config' : 'thirdapi_config';
        
        return strpos($content, "global \${$configVar}") !== false &&
               strpos($content, '$this->config') !== false &&
               strpos($content, '$this->mockData') !== false &&
               strpos($content, '$this->errorCodes') !== false;
    }
    
    /**
     * 输出验证结果
     */
    private function outputResults()
    {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "🎯 架构边界验证结果\n";
        echo str_repeat("=", 50) . "\n\n";
        
        if (empty($this->errors) && empty($this->warnings)) {
            echo "🎉 验证通过！所有文件都符合架构边界规范。\n\n";
            echo "✅ 确认所有模拟服务都不会向真实平台发起请求\n";
            echo "✅ 确认所有配置都有正确的边界标记\n";
            echo "✅ 确认所有控制器都有架构边界注释\n";
        } else {
            if (!empty($this->errors)) {
                echo "❌ 发现 " . count($this->errors) . " 个错误：\n";
                foreach ($this->errors as $error) {
                    echo "   {$error}\n";
                }
                echo "\n";
            }
            
            if (!empty($this->warnings)) {
                echo "⚠️ 发现 " . count($this->warnings) . " 个警告：\n";
                foreach ($this->warnings as $warning) {
                    echo "   {$warning}\n";
                }
                echo "\n";
            }
        }
        
        echo "📋 验证完成时间: " . date('Y-m-d H:i:s') . "\n";
        echo "📋 基于架构规范: index-new.mdc\n";
    }
}

// 执行验证
$validator = new ArchitectureBoundaryValidator();
$validator->validate();
