<?php
/**
 * 环境切换机制架构验证脚本
 * 
 * 🚨 基于 index-new.mdc 架构规范验证环境切换机制的正确实现
 * 
 * 验证内容：
 * 1. 确认环境切换配置在工具API接口服务中（php/api/config/ai.php）
 * 2. 确认模拟服务不包含环境切换逻辑（php/aiapi 和 php/thirdapi）
 * 3. 确认服务客户端正确实现环境切换
 * 4. 确认环境变量配置完整性
 */

class EnvironmentSwitchingValidator
{
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function validate()
    {
        echo "🚨 开始环境切换机制架构验证...\n\n";
        
        // 验证工具API接口服务的环境切换配置
        $this->validateToolApiConfig();
        
        // 验证模拟服务配置（确认不包含环境切换逻辑）
        $this->validateMockServicesConfig();
        
        // 验证服务客户端实现
        $this->validateServiceClients();
        
        // 验证环境变量配置
        $this->validateEnvironmentConfig();
        
        // 输出结果
        $this->outputResults();
    }
    
    /**
     * 验证工具API接口服务的环境切换配置
     */
    private function validateToolApiConfig()
    {
        echo "📁 验证工具API接口服务环境切换配置...\n";
        
        $configPath = 'php/api/config/ai.php';
        
        if (!file_exists($configPath)) {
            $this->errors[] = "❌ 工具API配置文件不存在: {$configPath}";
            return;
        }
        
        $content = file_get_contents($configPath);
        
        // 检查环境切换配置
        $requiredConfigs = [
            'service_mode' => "env('AI_SERVICE_MODE'",
            'mock_service' => 'mock_service',
            'real_service' => 'real_service',
            'platforms' => 'platforms',
            'third_party_config' => 'third_party_config'
        ];
        
        foreach ($requiredConfigs as $key => $pattern) {
            if (strpos($content, $pattern) === false) {
                $this->errors[] = "❌ 工具API配置缺少: {$key}";
            } else {
                $this->success[] = "✅ 工具API配置包含: {$key}";
            }
        }
        
        // 检查平台配置结构
        $platforms = ['deepseek', 'liblib', 'kling', 'minimax', 'volcengine'];
        foreach ($platforms as $platform) {
            if (strpos($content, "'{$platform}'") !== false) {
                if (strpos($content, 'mock_endpoint') !== false && strpos($content, 'real_api') !== false) {
                    $this->success[] = "✅ {$platform} 平台配置结构正确";
                } else {
                    $this->warnings[] = "⚠️ {$platform} 平台配置可能不完整";
                }
            }
        }
        
        echo "✅ 工具API配置验证完成\n\n";
    }
    
    /**
     * 验证模拟服务配置（确认不包含环境切换逻辑）
     */
    private function validateMockServicesConfig()
    {
        echo "📁 验证模拟服务配置（确认不包含环境切换逻辑）...\n";
        
        $mockServices = [
            'aiapi' => 'php/aiapi/config/config.php',
            'thirdapi' => 'php/thirdapi/config/config.php'
        ];
        
        foreach ($mockServices as $service => $configPath) {
            if (!file_exists($configPath)) {
                $this->errors[] = "❌ {$service} 配置文件不存在: {$configPath}";
                continue;
            }
            
            $content = file_get_contents($configPath);
            
            // 检查是否包含环境切换逻辑（不应该包含）
            $environmentSwitchingPatterns = [
                'AI_SERVICE_MODE',
                'THIRD_PARTY_MODE',
                'service_mode',
                'env.*MODE'
            ];
            
            $hasEnvironmentSwitching = false;
            foreach ($environmentSwitchingPatterns as $pattern) {
                if (preg_match("/{$pattern}/i", $content)) {
                    $hasEnvironmentSwitching = true;
                    break;
                }
            }
            
            if ($hasEnvironmentSwitching) {
                $this->errors[] = "❌ {$service} 配置包含环境切换逻辑（应该移除）";
            } else {
                $this->success[] = "✅ {$service} 配置正确，不包含环境切换逻辑";
            }
            
            // 检查是否包含模拟配置
            if (strpos($content, 'mock_config') !== false || strpos($content, 'no_real_requests') !== false) {
                $this->success[] = "✅ {$service} 包含正确的模拟配置";
            } else {
                $this->warnings[] = "⚠️ {$service} 可能缺少模拟配置";
            }
        }
        
        echo "✅ 模拟服务配置验证完成\n\n";
    }
    
    /**
     * 验证服务客户端实现
     */
    private function validateServiceClients()
    {
        echo "📁 验证服务客户端实现...\n";
        
        $clients = [
            'AiServiceClient' => 'php/api/app/Services/AiServiceClient.php',
            'ThirdPartyServiceClient' => 'php/api/app/Services/ThirdPartyServiceClient.php'
        ];
        
        foreach ($clients as $clientName => $clientPath) {
            if (!file_exists($clientPath)) {
                $this->errors[] = "❌ {$clientName} 不存在: {$clientPath}";
                continue;
            }
            
            $content = file_get_contents($clientPath);
            
            // 检查必要的方法
            $requiredMethods = [
                'call',
                'getServiceMode',
                'isMockMode',
                'validatePlatformConfig'
            ];
            
            foreach ($requiredMethods as $method) {
                if (strpos($content, "function {$method}") !== false || strpos($content, "static function {$method}") !== false) {
                    $this->success[] = "✅ {$clientName} 包含方法: {$method}";
                } else {
                    $this->errors[] = "❌ {$clientName} 缺少方法: {$method}";
                }
            }
            
            // 检查环境切换逻辑
            if (strpos($content, 'Config::get') !== false && strpos($content, 'service_mode') !== false) {
                $this->success[] = "✅ {$clientName} 包含环境切换逻辑";
            } else {
                $this->errors[] = "❌ {$clientName} 缺少环境切换逻辑";
            }
        }
        
        echo "✅ 服务客户端验证完成\n\n";
    }
    
    /**
     * 验证环境变量配置
     */
    private function validateEnvironmentConfig()
    {
        echo "📁 验证环境变量配置...\n";
        
        $envExamplePath = 'php/api/.env.example';
        
        if (!file_exists($envExamplePath)) {
            $this->errors[] = "❌ 环境变量示例文件不存在: {$envExamplePath}";
            return;
        }
        
        $content = file_get_contents($envExamplePath);
        
        // 检查必要的环境变量
        $requiredEnvVars = [
            'AI_SERVICE_MODE',
            'THIRD_PARTY_MODE',
            'AI_MOCK_URL',
            'THIRD_PARTY_MOCK_URL',
            'DEEPSEEK_API_KEY',
            'WECHAT_APP_ID',
            'ALIPAY_APP_ID'
        ];
        
        foreach ($requiredEnvVars as $envVar) {
            if (strpos($content, $envVar) !== false) {
                $this->success[] = "✅ 环境变量配置包含: {$envVar}";
            } else {
                $this->warnings[] = "⚠️ 环境变量配置可能缺少: {$envVar}";
            }
        }
        
        echo "✅ 环境变量配置验证完成\n\n";
    }
    
    /**
     * 输出验证结果
     */
    private function outputResults()
    {
        echo str_repeat("=", 60) . "\n";
        echo "🎯 环境切换机制架构验证结果\n";
        echo str_repeat("=", 60) . "\n\n";
        
        // 输出成功项
        if (!empty($this->success)) {
            echo "✅ 验证通过项 (" . count($this->success) . " 项):\n";
            foreach ($this->success as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }
        
        // 输出警告项
        if (!empty($this->warnings)) {
            echo "⚠️ 警告项 (" . count($this->warnings) . " 项):\n";
            foreach ($this->warnings as $warning) {
                echo "   {$warning}\n";
            }
            echo "\n";
        }
        
        // 输出错误项
        if (!empty($this->errors)) {
            echo "❌ 错误项 (" . count($this->errors) . " 项):\n";
            foreach ($this->errors as $error) {
                echo "   {$error}\n";
            }
            echo "\n";
        }
        
        // 总结
        if (empty($this->errors)) {
            echo "🎉 架构验证通过！环境切换机制实现正确。\n\n";
            echo "📋 架构确认:\n";
            echo "   ✅ 环境切换配置在工具API接口服务中实现\n";
            echo "   ✅ 模拟服务不包含环境切换逻辑\n";
            echo "   ✅ 服务客户端正确实现环境切换\n";
            echo "   ✅ 环境变量配置完整\n";
        } else {
            echo "❌ 架构验证失败，请修复上述错误。\n";
        }
        
        echo "\n📋 验证完成时间: " . date('Y-m-d H:i:s') . "\n";
        echo "📋 基于架构规范: index-new.mdc\n";
    }
}

// 执行验证
$validator = new EnvironmentSwitchingValidator();
$validator->validate();
