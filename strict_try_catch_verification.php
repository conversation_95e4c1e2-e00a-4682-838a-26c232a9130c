<?php

/**
 * 严格验证控制器文件的try-catch架构
 * 100%准确性检查
 */

$controllerDir = 'php/api/app/Http/Controllers/Api';
$results = [];
$detailedLog = [];

// 获取所有PHP文件
$files = glob($controllerDir . '/*.php');
sort($files);

echo "开始严格验证 " . count($files) . " 个控制器文件...\n";
echo "每个文件都会进行详细分析...\n\n";

foreach ($files as $file) {
    $fileName = basename($file);
    echo "正在详细检查: $fileName\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  ❌ 错误: 无法读取文件\n";
        continue;
    }
    
    // 移除注释以避免干扰
    $contentWithoutComments = preg_replace('/\/\*.*?\*\/|\/\/.*$/ms', '', $content);
    
    // 查找所有public function的精确位置
    $pattern = '/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{/';
    preg_match_all($pattern, $contentWithoutComments, $matches, PREG_OFFSET_CAPTURE);
    
    $methodsWithoutTry = [];
    $totalMethods = 0;
    $methodDetails = [];
    
    for ($i = 0; $i < count($matches[0]); $i++) {
        $methodName = $matches[1][$i][0];
        $functionStart = $matches[0][$i][1] + strlen($matches[0][$i][0]);
        
        // 跳过构造函数
        if ($methodName === '__construct') {
            echo "  ⏭️  跳过构造函数: $methodName\n";
            continue;
        }
        
        $totalMethods++;
        
        // 精确提取函数体内容
        $braceCount = 1;
        $pos = $functionStart;
        $functionBody = '';
        
        while ($pos < strlen($contentWithoutComments) && $braceCount > 0) {
            $char = $contentWithoutComments[$pos];
            if ($char === '{') {
                $braceCount++;
            } elseif ($char === '}') {
                $braceCount--;
            }
            
            if ($braceCount > 0) {
                $functionBody .= $char;
            }
            $pos++;
        }
        
        // 分析函数体的第一行
        $lines = explode("\n", trim($functionBody));
        $firstNonEmptyLine = '';
        $lineNumber = 0;
        
        foreach ($lines as $index => $line) {
            $trimmedLine = trim($line);
            if (!empty($trimmedLine)) {
                $firstNonEmptyLine = $trimmedLine;
                $lineNumber = $index + 1;
                break;
            }
        }
        
        // 检查是否以try {开始
        $hasTryStart = preg_match('/^try\s*\{/', $firstNonEmptyLine);
        
        $methodDetails[] = [
            'name' => $methodName,
            'first_line' => $firstNonEmptyLine,
            'has_try' => $hasTryStart,
            'line_number' => $lineNumber
        ];
        
        if (!$hasTryStart) {
            $methodsWithoutTry[] = $methodName;
            echo "  ❌ $methodName(): 第一行不是try { -> '$firstNonEmptyLine'\n";
        } else {
            echo "  ✅ $methodName(): 正确以try {开始\n";
        }
    }
    
    $detailedLog[$fileName] = [
        'total_methods' => $totalMethods,
        'methods_without_try' => $methodsWithoutTry,
        'method_details' => $methodDetails
    ];
    
    if (!empty($methodsWithoutTry)) {
        $results[] = [
            'file' => $fileName,
            'methods' => $methodsWithoutTry,
            'total' => $totalMethods
        ];
    }
    
    echo "  📊 总计: $totalMethods 个public方法，" . count($methodsWithoutTry) . " 个不符合要求\n\n";
}

echo str_repeat("=", 80) . "\n";
echo "🔍 严格验证完成！详细结果如下：\n";
echo str_repeat("=", 80) . "\n\n";

if (empty($results)) {
    echo "✅ 验证通过：所有控制器文件的public function方法都正确以'try {'开始！\n\n";
} else {
    echo "❌ 发现问题：以下方法不符合try-catch架构要求：\n\n";
    $counter = 1;
    foreach ($results as $result) {
        echo "### {$counter}. {$result['file']} ({$result['total']}个方法)\n";
        foreach ($result['methods'] as $method) {
            echo "- [ ] `{$method}()`\n";
        }
        echo "\n";
        $counter++;
    }
}

// 统计信息
$totalMethodsCount = array_sum(array_column($detailedLog, 'total_methods'));
$totalProblematicMethods = array_sum(array_map(function($log) {
    return count($log['methods_without_try']);
}, $detailedLog));

echo str_repeat("=", 80) . "\n";
echo "📊 最终统计\n";
echo str_repeat("=", 80) . "\n";
echo "扫描文件总数: " . count($files) . "\n";
echo "public方法总数: " . $totalMethodsCount . "\n";
echo "问题方法总数: " . $totalProblematicMethods . "\n";
echo "问题文件总数: " . count($results) . "\n";
echo "合规率: " . round(($totalMethodsCount - $totalProblematicMethods) / $totalMethodsCount * 100, 2) . "%\n";

?>
