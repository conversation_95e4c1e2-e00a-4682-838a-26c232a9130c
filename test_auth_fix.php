<?php

/**
 * 测试认证修复 - 验证路由中间件问题是否解决
 */

echo "🔧 测试认证修复...\n\n";

// 模拟HTTP请求来测试路由
$testUrl = 'http://localhost/tool_api/php/api/public/index.php/py-api/user-growth/profile';
$token = 'nQ2PapFzEiDkQdcDnGg10A6jm8edtnZrml3s1nMDREvb9';

echo "🎯 测试目标: $testUrl\n";
echo "🔑 使用Token: $token\n\n";

// 设置请求头
$headers = [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json',
    'Accept: application/json',
    'User-Agent: API-Test-Script'
];

// 初始化cURL
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $testUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_VERBOSE => false
]);

echo "🚀 发送API请求...\n";

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$info = curl_getinfo($ch);

curl_close($ch);

echo "📊 请求结果:\n";
echo "   HTTP状态码: $httpCode\n";
echo "   响应时间: " . round($info['total_time'], 3) . "秒\n";

if ($error) {
    echo "   ❌ cURL错误: $error\n";
} else {
    echo "   📄 响应长度: " . strlen($response) . " 字节\n";
    
    // 尝试解析响应
    if ($httpCode == 200) {
        echo "   ✅ HTTP状态正常\n";
        
        $jsonData = json_decode($response, true);
        if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
            echo "   ✅ JSON解析成功\n";
            
            if (isset($jsonData['code']) && $jsonData['code'] == 200) {
                echo "   🎉 API调用成功！\n";
                echo "   📋 返回数据:\n";
                
                if (isset($jsonData['data'])) {
                    $data = $jsonData['data'];
                    echo "      - 用户ID: " . ($data['user_id'] ?? '未知') . "\n";
                    echo "      - 等级: " . ($data['level'] ?? '未知') . "\n";
                    echo "      - 经验值: " . ($data['experience'] ?? '未知') . "\n";
                    echo "      - 称号: " . ($data['title'] ?? '未知') . "\n";
                }
            } else {
                echo "   ❌ API返回错误\n";
                echo "   错误码: " . ($jsonData['code'] ?? '未知') . "\n";
                echo "   错误信息: " . ($jsonData['message'] ?? '未知') . "\n";
            }
        } else {
            echo "   ❌ JSON解析失败\n";
            echo "   响应内容: " . substr($response, 0, 500) . "\n";
        }
    } else {
        echo "   ❌ HTTP状态异常\n";
        
        // 尝试解析错误响应
        $jsonData = json_decode($response, true);
        if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
            echo "   错误详情:\n";
            if (isset($jsonData['message'])) {
                echo "      消息: " . $jsonData['message'] . "\n";
            }
            if (isset($jsonData['exception'])) {
                echo "      异常类型: " . $jsonData['exception'] . "\n";
            }
            if (isset($jsonData['file']) && isset($jsonData['line'])) {
                echo "      错误位置: " . basename($jsonData['file']) . ":" . $jsonData['line'] . "\n";
            }
        } else {
            echo "   原始响应: " . substr($response, 0, 500) . "\n";
        }
    }
}

echo "\n" . str_repeat("=", 60) . "\n";

if ($httpCode == 200) {
    echo "✅ 认证中间件问题已修复！\n";
    echo "🎯 用户成长API现在可以正常工作了。\n";
} else {
    echo "❌ 仍有问题需要解决\n";
    echo "🔍 建议检查:\n";
    echo "   1. 服务器配置\n";
    echo "   2. Token有效性\n";
    echo "   3. 数据库连接\n";
    echo "   4. 最新错误日志\n";
}

?>
