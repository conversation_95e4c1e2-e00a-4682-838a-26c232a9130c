<?php

/**
 * 测试两种API认证方式
 */

echo "🔐 测试API认证方式...\n\n";

$token = '64ed38eb11f6294c47127d59979ce5be1dceb46b128a7bdc58';
$baseUrl = 'http://localhost/tool_api/php/api/public/index.php/py-api/user-growth/profile';

echo "🔑 使用Token: $token\n\n";

// 测试方式1：Bearer Token
echo "📋 方式1：Bearer Token认证\n";
echo str_repeat("-", 50) . "\n";

$headers1 = [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json',
    'Accept: application/json'
];

$ch1 = curl_init();
curl_setopt_array($ch1, [
    CURLOPT_URL => $baseUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => $headers1,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false
]);

$response1 = curl_exec($ch1);
$httpCode1 = curl_getinfo($ch1, CURLINFO_HTTP_CODE);
$error1 = curl_error($ch1);
curl_close($ch1);

echo "HTTP状态码: $httpCode1\n";
if ($error1) {
    echo "❌ cURL错误: $error1\n";
} else {
    $data1 = json_decode($response1, true);
    if ($httpCode1 == 200 && $data1 && $data1['code'] == 200) {
        echo "✅ Bearer Token认证成功！\n";
        echo "用户ID: " . ($data1['data']['user_id'] ?? '未知') . "\n";
        echo "等级: " . ($data1['data']['level'] ?? '未知') . "\n";
    } else {
        echo "❌ Bearer Token认证失败\n";
        if ($data1) {
            echo "错误: " . ($data1['message'] ?? '未知错误') . "\n";
        }
    }
}

echo "\n";

// 测试方式2：URL参数
echo "📋 方式2：URL参数认证\n";
echo str_repeat("-", 50) . "\n";

$urlWithToken = $baseUrl . '?token=' . $token;

$headers2 = [
    'Content-Type: application/json',
    'Accept: application/json'
];

$ch2 = curl_init();
curl_setopt_array($ch2, [
    CURLOPT_URL => $urlWithToken,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => $headers2,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false
]);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
$error2 = curl_error($ch2);
curl_close($ch2);

echo "HTTP状态码: $httpCode2\n";
if ($error2) {
    echo "❌ cURL错误: $error2\n";
} else {
    $data2 = json_decode($response2, true);
    if ($httpCode2 == 200 && $data2 && $data2['code'] == 200) {
        echo "✅ URL参数认证成功！\n";
        echo "用户ID: " . ($data2['data']['user_id'] ?? '未知') . "\n";
        echo "等级: " . ($data2['data']['level'] ?? '未知') . "\n";
    } else {
        echo "❌ URL参数认证失败\n";
        if ($data2) {
            echo "错误: " . ($data2['message'] ?? '未知错误') . "\n";
        }
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 测试总结:\n";

if ($httpCode1 == 200) {
    echo "✅ Bearer Token方式: 成功\n";
} else {
    echo "❌ Bearer Token方式: 失败 (HTTP $httpCode1)\n";
}

if ($httpCode2 == 200) {
    echo "✅ URL参数方式: 成功\n";
} else {
    echo "❌ URL参数方式: 失败 (HTTP $httpCode2)\n";
}

echo "\n💡 建议:\n";
if ($httpCode1 == 200 || $httpCode2 == 200) {
    echo "🎉 至少有一种认证方式成功，用户成长API修复完成！\n";
    echo "📋 可以使用成功的认证方式进行后续测试\n";
} else {
    echo "🔍 两种认证方式都失败，可能需要检查:\n";
    echo "   1. Web服务器是否已重启\n";
    echo "   2. Token是否正确存储在Redis中\n";
    echo "   3. 认证中间件是否正常工作\n";
}

?>
