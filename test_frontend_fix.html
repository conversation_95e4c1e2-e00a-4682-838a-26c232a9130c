<!DOCTYPE html>
<html>
<head>
    <title>测试前端修复</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>测试前端Authorization修复</h1>
    
    <div id="test-form">
        <h3>模拟接口测试表单</h3>
        <form id="form5" action="/py-api/auth/verify" method="GET">
            <div>
                <label>Authorization:</label>
                <input type="text" name="header_Authorization" value="Bearer DmVB8IA2dPqJZBkzB8BGyXwrmaL1URxV23kulVm5kP7Lj">
            </div>
            <div>
                <label>普通参数:</label>
                <input type="text" name="test_param" value="test_value">
            </div>
            <button type="button" onclick="testSubmit()">测试提交</button>
        </form>
    </div>
    
    <div id="result">
        <h3>测试结果</h3>
        <pre id="output"></pre>
    </div>

    <script>
        // 模拟authMode变量
        var authMode = 1;
        
        // 修复后的submitApiForm函数
        function submitApiForm(index) {
            var form = $('#form' + index);
            var method = form.attr('method');
            var action = form.attr('action');
            var apiUrl = 'https://api.tiptop.cn';
            var fullUrl = apiUrl + action;

            // 收集表单数据和请求头
            var formData = {};
            var headers = {
                'Content-Type': 'application/json'
            };

            form.find('input[type="text"]').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();
                if (value) {
                    // 检查是否是header字段
                    if (name.startsWith('header_')) {
                        // 提取header名称并添加到headers对象
                        var headerName = name.substring(7); // 移除'header_'前缀
                        headers[headerName] = value;
                    } else {
                        // 普通表单数据
                        formData[name] = value;
                    }
                }
            });

            // 如果没有接口级Authorization且authMode=1，则使用全局Authorization
            if (!headers['Authorization'] && authMode === 1) {
                var auth = $('#Authorization').val();
                if (auth) {
                    headers['Authorization'] = auth;
                }
            }

            // 显示收集到的数据
            var output = {
                url: fullUrl,
                method: method,
                headers: headers,
                formData: formData
            };
            
            $('#output').text(JSON.stringify(output, null, 2));
            
            console.log('测试结果:', output);
            return output;
        }
        
        function testSubmit() {
            submitApiForm(5);
        }
        
        // 页面加载完成后自动测试
        $(document).ready(function() {
            console.log('页面加载完成，开始测试...');
            testSubmit();
        });
    </script>
</body>
</html>
