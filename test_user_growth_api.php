<?php

/**
 * 测试用户成长API
 */

echo "🧪 测试用户成长API...\n\n";

// 测试API端点
$apiUrl = 'http://localhost/tool_api/php/api/public/index.php/py-api/user-growth/profile';
$token = 'nQ2PapFzEiDkQdcDnGg10A6jm8edtnZrml3s1nMDREvb9';

echo "📡 API端点: $apiUrl\n";
echo "🔑 测试Token: $token\n\n";

// 设置请求头
$headers = [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json',
    'Accept: application/json'
];

// 初始化cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

echo "🚀 发送API请求...\n";

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "📊 响应结果:\n";
echo "   HTTP状态码: $httpCode\n";

if ($error) {
    echo "   ❌ cURL错误: $error\n";
} else {
    echo "   📄 响应内容:\n";
    
    // 尝试解析JSON
    $jsonData = json_decode($response, true);
    if ($jsonData) {
        echo "   ✅ JSON解析成功\n";
        echo "   📋 响应数据:\n";
        echo json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        // 分析响应
        if (isset($jsonData['code'])) {
            $code = $jsonData['code'];
            $message = $jsonData['message'] ?? '无消息';
            
            if ($code == 200) {
                echo "\n   🎉 API调用成功！\n";
                if (isset($jsonData['data'])) {
                    $data = $jsonData['data'];
                    echo "   👤 用户ID: " . ($data['user_id'] ?? '未知') . "\n";
                    echo "   📊 等级: " . ($data['level'] ?? '未知') . "\n";
                    echo "   ⭐ 经验值: " . ($data['experience'] ?? '未知') . "\n";
                    echo "   📈 等级进度: " . ($data['level_progress'] ?? '未知') . "%\n";
                    echo "   🏆 称号: " . ($data['title'] ?? '未知') . "\n";
                }
            } else {
                echo "\n   ❌ API返回错误\n";
                echo "   错误码: $code\n";
                echo "   错误信息: $message\n";
            }
        }
    } else {
        echo "   ❌ JSON解析失败\n";
        echo "   原始响应: $response\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";

if ($httpCode == 200 && isset($jsonData['code']) && $jsonData['code'] == 200) {
    echo "✅ 用户成长API测试通过！\n";
    echo "🎯 问题已解决，API正常工作。\n";
} else {
    echo "❌ 用户成长API仍有问题\n";
    echo "🔍 建议检查:\n";
    echo "   1. 服务器是否正常运行\n";
    echo "   2. Token是否有效\n";
    echo "   3. 数据库连接是否正常\n";
    echo "   4. Redis连接是否正常\n";
    echo "   5. 查看最新的错误日志\n";
}

?>
