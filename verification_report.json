{"scan_time": "2025-08-03 00:22:14", "total_files": 41, "total_methods": 253, "problem_methods": 0, "compliance_rate": 100, "all_methods": [{"file": "AdController.php", "method": "ad_store", "line": 47, "first_line": "try {", "has_try": 1}, {"file": "AdController.php", "method": "ad_update", "line": 120, "first_line": "try {", "has_try": 1}, {"file": "AdController.php", "method": "store", "line": 183, "first_line": "try {", "has_try": 1}, {"file": "AdController.php", "method": "update", "line": 236, "first_line": "try {", "has_try": 1}, {"file": "AiGenerationController.php", "method": "generateText", "line": 57, "first_line": "try {", "has_try": 1}, {"file": "AiGenerationController.php", "method": "getTaskStatus", "line": 163, "first_line": "try {", "has_try": 1}, {"file": "AiGenerationController.php", "method": "getUserTasks", "line": 232, "first_line": "try {", "has_try": 1}, {"file": "AiGenerationController.php", "method": "retryTask", "line": 299, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "available", "line": 98, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "detail", "line": 192, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "test", "line": 255, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "usageStats", "line": 343, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "favorite", "line": 408, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "favorites", "line": 483, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "list", "line": 532, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "switch", "line": 573, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "selectOptimalPlatform", "line": 628, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "checkPlatformHealth", "line": 669, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "getAllPlatformsHealth", "line": 694, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "getPlatformStats", "line": 720, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "platformComparison", "line": 781, "first_line": "try {", "has_try": 1}, {"file": "AiModelController.php", "method": "businessPlatforms", "line": 860, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "index", "line": 61, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "show", "line": 138, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "stats", "line": 200, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "cancel", "line": 256, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "retry", "line": 314, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "batchStatus", "line": 388, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "recovery", "line": 452, "first_line": "try {", "has_try": 1}, {"file": "AiTaskController.php", "method": "timeoutConfig", "line": 502, "first_line": "try {", "has_try": 1}, {"file": "AnalyticsController.php", "method": "getUserBehavior", "line": 68, "first_line": "try {", "has_try": 1}, {"file": "AnalyticsController.php", "method": "getSystemUsage", "line": 182, "first_line": "try {", "has_try": 1}, {"file": "AnalyticsController.php", "method": "getAiPerformance", "line": 283, "first_line": "try {", "has_try": 1}, {"file": "AnalyticsController.php", "method": "getUserRetention", "line": 380, "first_line": "try {", "has_try": 1}, {"file": "AnalyticsController.php", "method": "getRevenue", "line": 479, "first_line": "try {", "has_try": 1}, {"file": "AnalyticsController.php", "method": "generateCustomReport", "line": 551, "first_line": "try {", "has_try": 1}, {"file": "AssetController.php", "method": "list", "line": 35, "first_line": "try {", "has_try": 1}, {"file": "AssetController.php", "method": "upload", "line": 75, "first_line": "try {", "has_try": 1}, {"file": "AssetController.php", "method": "show", "line": 136, "first_line": "try {", "has_try": 1}, {"file": "AssetController.php", "method": "delete", "line": 174, "first_line": "try {", "has_try": 1}, {"file": "AudioController.php", "method": "mix", "line": 47, "first_line": "try {", "has_try": 1}, {"file": "AudioController.php", "method": "getMixStatus", "line": 138, "first_line": "try {", "has_try": 1}, {"file": "AudioController.php", "method": "enhance", "line": 189, "first_line": "try {", "has_try": 1}, {"file": "AudioController.php", "method": "getEnhanceStatus", "line": 280, "first_line": "try {", "has_try": 1}, {"file": "AuthController.php", "method": "register", "line": 41, "first_line": "try {", "has_try": 1}, {"file": "AuthController.php", "method": "login", "line": 108, "first_line": "try {", "has_try": 1}, {"file": "AuthController.php", "method": "logout", "line": 159, "first_line": "try {", "has_try": 1}, {"file": "AuthController.php", "method": "forgotPassword", "line": 203, "first_line": "try {", "has_try": 1}, {"file": "AuthController.php", "method": "resetPassword", "line": 253, "first_line": "try {", "has_try": 1}, {"file": "AuthController.php", "method": "verify", "line": 308, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "generateImages", "line": 48, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "synthesizeVoices", "line": 131, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "generateMusic", "line": 213, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "getBatchStatus", "line": 303, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "cancelBatch", "line": 350, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "generateResources", "line": 403, "first_line": "try {", "has_try": 1}, {"file": "BatchController.php", "method": "getResourcesStatus", "line": 487, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "getStats", "line": 76, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "clearCache", "line": 133, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "warmupCache", "line": 206, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "get<PERSON><PERSON><PERSON>", "line": 299, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "getValue", "line": 378, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "setValue", "line": 451, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "deleteKeys", "line": 534, "first_line": "try {", "has_try": 1}, {"file": "CacheController.php", "method": "getConfig", "line": 634, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "getCategories", "line": 54, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "getLibrary", "line": 134, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "getCharacterDetail", "line": 212, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "generate", "line": 285, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "getRecommendations", "line": 379, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "bindCharacter", "line": 418, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "getMyBindings", "line": 479, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "updateBinding", "line": 525, "first_line": "try {", "has_try": 1}, {"file": "CharacterController.php", "method": "unbindCharacter", "line": 596, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "index", "line": 61, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "getPublicConfig", "line": 140, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "update", "line": 182, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "batchUpdate", "line": 250, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "reset", "line": 322, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "history", "line": 394, "first_line": "try {", "has_try": 1}, {"file": "ConfigController.php", "method": "validateConfig", "line": 455, "first_line": "try {", "has_try": 1}, {"file": "CreditsController.php", "method": "checkCredits", "line": 52, "first_line": "try {", "has_try": 1}, {"file": "CreditsController.php", "method": "freezeCredits", "line": 131, "first_line": "try {", "has_try": 1}, {"file": "CreditsController.php", "method": "refundCredits", "line": 212, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "list", "line": 74, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "retry", "line": 154, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "statistics", "line": 218, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "createLink", "line": 288, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "secureDownload", "line": 355, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "batchDownload", "line": 402, "first_line": "try {", "has_try": 1}, {"file": "DownloadController.php", "method": "cleanup", "line": 481, "first_line": "try {", "has_try": 1}, {"file": "FileController.php", "method": "upload", "line": 52, "first_line": "try {", "has_try": 1}, {"file": "FileController.php", "method": "getFiles", "line": 140, "first_line": "try {", "has_try": 1}, {"file": "FileController.php", "method": "getFileDetail", "line": 206, "first_line": "try {", "has_try": 1}, {"file": "FileController.php", "method": "deleteFile", "line": 254, "first_line": "try {", "has_try": 1}, {"file": "FileController.php", "method": "downloadFile", "line": 303, "first_line": "try {", "has_try": 1}, {"file": "ImageController.php", "method": "generate", "line": 58, "first_line": "try {", "has_try": 1}, {"file": "ImageController.php", "method": "getStatus", "line": 154, "first_line": "try {", "has_try": 1}, {"file": "ImageController.php", "method": "getResult", "line": 213, "first_line": "try {", "has_try": 1}, {"file": "ImageController.php", "method": "batchGenerate", "line": 265, "first_line": "try {", "has_try": 1}, {"file": "LogController.php", "method": "systemLogs", "line": 76, "first_line": "try {", "has_try": 1}, {"file": "LogController.php", "method": "userActionLogs", "line": 185, "first_line": "try {", "has_try": 1}, {"file": "LogController.php", "method": "aiCallLogs", "line": 300, "first_line": "try {", "has_try": 1}, {"file": "LogController.php", "method": "errorLogs", "line": 411, "first_line": "try {", "has_try": 1}, {"file": "LogController.php", "method": "resolveError", "line": 487, "first_line": "try {", "has_try": 1}, {"file": "LogController.php", "method": "exportLogs", "line": 550, "first_line": "try {", "has_try": 1}, {"file": "MusicController.php", "method": "generate", "line": 54, "first_line": "try {", "has_try": 1}, {"file": "MusicController.php", "method": "getStatus", "line": 153, "first_line": "try {", "has_try": 1}, {"file": "MusicController.php", "method": "getResult", "line": 215, "first_line": "try {", "has_try": 1}, {"file": "MusicController.php", "method": "batchGenerate", "line": 267, "first_line": "try {", "has_try": 1}, {"file": "NotificationController.php", "method": "index", "line": 61, "first_line": "try {", "has_try": 1}, {"file": "NotificationController.php", "method": "mark<PERSON><PERSON><PERSON>", "line": 123, "first_line": "try {", "has_try": 1}, {"file": "NotificationController.php", "method": "markAllAsRead", "line": 184, "first_line": "try {", "has_try": 1}, {"file": "NotificationController.php", "method": "destroy", "line": 237, "first_line": "try {", "has_try": 1}, {"file": "NotificationController.php", "method": "stats", "line": 296, "first_line": "try {", "has_try": 1}, {"file": "NotificationController.php", "method": "send", "line": 346, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "getUserPermissions", "line": 73, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "checkPermission", "line": 142, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "getRoles", "line": 238, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "assignRole", "line": 297, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "grantPermissions", "line": 384, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "revokePermissions", "line": 470, "first_line": "try {", "has_try": 1}, {"file": "PermissionController.php", "method": "getPermissionHistory", "line": 567, "first_line": "try {", "has_try": 1}, {"file": "PointsController.php", "method": "balance", "line": 49, "first_line": "try {", "has_try": 1}, {"file": "PointsController.php", "method": "recharge", "line": 101, "first_line": "try {", "has_try": 1}, {"file": "PointsController.php", "method": "transactions", "line": 182, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "createWithStory", "line": 53, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "confirmTitle", "line": 141, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "myProjects", "line": 224, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "detail", "line": 316, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "list", "line": 372, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "create", "line": 392, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "show", "line": 436, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "update", "line": 456, "first_line": "try {", "has_try": 1}, {"file": "ProjectController.php", "method": "delete", "line": 504, "first_line": "try {", "has_try": 1}, {"file": "ProjectManagementController.php", "method": "createTask", "line": 56, "first_line": "try {", "has_try": 1}, {"file": "ProjectManagementController.php", "method": "collaborate", "line": 145, "first_line": "try {", "has_try": 1}, {"file": "ProjectManagementController.php", "method": "getProgress", "line": 228, "first_line": "try {", "has_try": 1}, {"file": "ProjectManagementController.php", "method": "assignResources", "line": 295, "first_line": "try {", "has_try": 1}, {"file": "ProjectManagementController.php", "method": "getStatistics", "line": 372, "first_line": "try {", "has_try": 1}, {"file": "ProjectManagementController.php", "method": "getMilestones", "line": 444, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "publish", "line": 59, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "getStatus", "line": 157, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "update", "line": 213, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "delete", "line": 276, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "unpublish", "line": 325, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "myPublications", "line": 399, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "plaza", "line": 501, "first_line": "try {", "has_try": 1}, {"file": "PublicationController.php", "method": "detail", "line": 593, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "content", "line": 79, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "users", "line": 178, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "topics", "line": 269, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "feedback", "line": 334, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "preferences", "line": 437, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "updatePreferences", "line": 500, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "analytics", "line": 601, "first_line": "try {", "has_try": 1}, {"file": "RecommendationController.php", "method": "personalized", "line": 648, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "generate", "line": 60, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "getStatus", "line": 185, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "list", "line": 252, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "delete", "line": 320, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "getDownloadInfo", "line": 359, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "confirmDownload", "line": 403, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "myResources", "line": 456, "first_line": "try {", "has_try": 1}, {"file": "ResourceController.php", "method": "updateStatus", "line": 495, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "submit", "line": 51, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "getStatus", "line": 137, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "appeal", "line": 191, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "myReviews", "line": 288, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "queueStatus", "line": 375, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "guidelines", "line": 460, "first_line": "try {", "has_try": 1}, {"file": "ReviewController.php", "method": "<PERSON><PERSON><PERSON><PERSON>", "line": 513, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "follow", "line": 50, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "follows", "line": 157, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "like", "line": 222, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "comment", "line": 308, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "comments", "line": 427, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "share", "line": 496, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "feed", "line": 601, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "notifications", "line": 692, "first_line": "try {", "has_try": 1}, {"file": "SocialController.php", "method": "markNotificationsRead", "line": 758, "first_line": "try {", "has_try": 1}, {"file": "SoundController.php", "method": "generate", "line": 54, "first_line": "try {", "has_try": 1}, {"file": "SoundController.php", "method": "getStatus", "line": 158, "first_line": "try {", "has_try": 1}, {"file": "SoundController.php", "method": "getResult", "line": 220, "first_line": "try {", "has_try": 1}, {"file": "SoundController.php", "method": "batchGenerate", "line": 272, "first_line": "try {", "has_try": 1}, {"file": "StoryController.php", "method": "generate", "line": 72, "first_line": "try {", "has_try": 1}, {"file": "StoryController.php", "method": "getStatus", "line": 163, "first_line": "try {", "has_try": 1}, {"file": "StyleController.php", "method": "list", "line": 64, "first_line": "try {", "has_try": 1}, {"file": "StyleController.php", "method": "detail", "line": 141, "first_line": "try {", "has_try": 1}, {"file": "StyleController.php", "method": "popular", "line": 202, "first_line": "try {", "has_try": 1}, {"file": "StyleController.php", "method": "create", "line": 231, "first_line": "try {", "has_try": 1}, {"file": "TaskManagementController.php", "method": "cancelTask", "line": 49, "first_line": "try {", "has_try": 1}, {"file": "TaskManagementController.php", "method": "retryTask", "line": 125, "first_line": "try {", "has_try": 1}, {"file": "TaskManagementController.php", "method": "getBatchStatus", "line": 210, "first_line": "try {", "has_try": 1}, {"file": "TaskManagementController.php", "method": "getTimeoutConfig", "line": 282, "first_line": "try {", "has_try": 1}, {"file": "TaskManagementController.php", "method": "getRecoveryStatus", "line": 328, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "create", "line": 70, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "use", "line": 177, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "marketplace", "line": 290, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "myTemplates", "line": 382, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "detail", "line": 486, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "update", "line": 543, "first_line": "try {", "has_try": 1}, {"file": "TemplateController.php", "method": "delete", "line": 604, "first_line": "try {", "has_try": 1}, {"file": "UserController.php", "method": "profile", "line": 58, "first_line": "try {", "has_try": 1}, {"file": "UserController.php", "method": "updateProfile", "line": 103, "first_line": "try {", "has_try": 1}, {"file": "UserController.php", "method": "updatePreferences", "line": 187, "first_line": "try {", "has_try": 1}, {"file": "UserController.php", "method": "getPreferences", "line": 269, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "profile", "line": 75, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "leaderboard", "line": 139, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "completeAchievement", "line": 214, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "dailyTasks", "line": 293, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "completeDailyTask", "line": 352, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "history", "line": 441, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "statistics", "line": 532, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "setGoals", "line": 602, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "recommendations", "line": 682, "first_line": "try {", "has_try": 1}, {"file": "UserGrowthController.php", "method": "milestones", "line": 718, "first_line": "try {", "has_try": 1}, {"file": "VersionController.php", "method": "create", "line": 54, "first_line": "try {", "has_try": 1}, {"file": "VersionController.php", "method": "list", "line": 157, "first_line": "try {", "has_try": 1}, {"file": "VersionController.php", "method": "show", "line": 242, "first_line": "try {", "has_try": 1}, {"file": "VersionController.php", "method": "setCurrent", "line": 292, "first_line": "try {", "has_try": 1}, {"file": "VersionController.php", "method": "delete", "line": 341, "first_line": "try {", "has_try": 1}, {"file": "VersionController.php", "method": "compare", "line": 411, "first_line": "try {", "has_try": 1}, {"file": "VideoController.php", "method": "generate", "line": 58, "first_line": "try {", "has_try": 1}, {"file": "VideoController.php", "method": "getStatus", "line": 158, "first_line": "try {", "has_try": 1}, {"file": "VideoController.php", "method": "getResult", "line": 220, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "synthesize", "line": 57, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "getStatus", "line": 159, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "batchSynthesize", "line": 211, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "clone", "line": 291, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "getCloneStatus", "line": 383, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "custom", "line": 437, "first_line": "try {", "has_try": 1}, {"file": "VoiceController.php", "method": "getCustomStatus", "line": 538, "first_line": "try {", "has_try": 1}, {"file": "WebSocketController.php", "method": "authenticate", "line": 50, "first_line": "try {", "has_try": 1}, {"file": "WebSocketController.php", "method": "getSessions", "line": 136, "first_line": "try {", "has_try": 1}, {"file": "WebSocketController.php", "method": "disconnect", "line": 188, "first_line": "try {", "has_try": 1}, {"file": "WebSocketController.php", "method": "getStatus", "line": 257, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "publishWork", "line": 43, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "update", "line": 146, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "delete", "line": 233, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "myWorks", "line": 292, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "gallery", "line": 333, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "getShareLink", "line": 365, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "like", "line": 431, "first_line": "try {", "has_try": 1}, {"file": "WorkPublishController.php", "method": "trending", "line": 498, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "create", "line": 73, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "index", "line": 177, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "show", "line": 278, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "execute", "line": 337, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "getExecutionStatus", "line": 429, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "provideStepInput", "line": 480, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "cancelExecution", "line": 552, "first_line": "try {", "has_try": 1}, {"file": "WorkflowController.php", "method": "getExecutionHistory", "line": 641, "first_line": "try {", "has_try": 1}], "problems": []}