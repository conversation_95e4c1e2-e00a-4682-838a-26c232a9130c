<?php

/**
 * 验证登录获得的Token
 */

echo "🔍 验证登录获得的Token...\n\n";

$loginToken = 'DmVB8IA2dPqJZBkzB8BGyXwrmaL1URxV23kulVm5kP7Lj';

try {
    require_once 'php/api/vendor/autoload.php';
    
    // 加载环境变量
    $dotenv = Dotenv\Dotenv::createImmutable('php/api');
    $dotenv->load();
    
    echo "✅ 环境加载成功\n";
    echo "🔑 验证Token: $loginToken\n\n";
    
    // 连接Redis检查token
    $redis = new Redis();
    $redisHost = $_ENV['REDIS_HOST'] ?? '127.0.0.1';
    $redisPort = $_ENV['REDIS_PORT'] ?? 6379;
    
    if ($redis->connect($redisHost, $redisPort)) {
        echo "✅ Redis连接成功\n";
        
        // 检查不同的token键格式
        $tokenFormats = [
            "user:token:$loginToken",  // 使用token作为键
            "user:token:1",            // 使用用户ID作为键
            "token:$loginToken",       // 简化格式
            "auth:token:$loginToken",  // 认证格式
            "api:token:$loginToken"    // API格式
        ];
        
        echo "🔍 检查不同的Token键格式:\n";
        foreach ($tokenFormats as $key) {
            $exists = $redis->exists($key);
            echo "   $key: " . ($exists ? "存在" : "不存在") . "\n";
            
            if ($exists) {
                $data = $redis->get($key);
                echo "     数据: $data\n";
                $ttl = $redis->ttl($key);
                echo "     TTL: " . ($ttl > 0 ? $ttl . "秒" : "永久") . "\n";
            }
        }
        
        // 查找所有包含这个token的键
        echo "\n🔍 搜索包含token的所有键:\n";
        $allKeys = $redis->keys('*');
        $foundKeys = [];
        
        foreach ($allKeys as $key) {
            if (strpos($key, $loginToken) !== false || strpos($redis->get($key), $loginToken) !== false) {
                $foundKeys[] = $key;
                echo "   找到相关键: $key\n";
                echo "     值: " . $redis->get($key) . "\n";
            }
        }
        
        if (empty($foundKeys)) {
            echo "   ❌ 没有找到包含此token的键\n";
        }
        
        // 检查用户ID=1的token
        echo "\n🔍 检查用户ID=1的token存储:\n";
        $userTokenKey = "user:token:1";
        if ($redis->exists($userTokenKey)) {
            $storedToken = $redis->get($userTokenKey);
            echo "   存储的token: $storedToken\n";
            echo "   登录token: $loginToken\n";
            echo "   匹配状态: " . ($storedToken === $loginToken ? "匹配" : "不匹配") . "\n";
            
            // 如果不匹配，尝试MD5比较
            if ($storedToken !== $loginToken) {
                $md5Token = md5($loginToken);
                echo "   MD5比较: " . ($storedToken === $md5Token ? "MD5匹配" : "MD5不匹配") . "\n";
            }
        } else {
            echo "   ❌ 用户ID=1没有token存储\n";
        }
        
        $redis->close();
    } else {
        echo "❌ Redis连接失败\n";
    }
    
    // 测试直接API调用
    echo "\n🧪 测试直接API调用:\n";
    $apiUrl = 'http://localhost/tool_api/php/api/public/index.php/py-api/user-growth/profile';
    
    $headers = [
        'Authorization: Bearer ' . $loginToken,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "   HTTP状态码: $httpCode\n";
    if ($error) {
        echo "   ❌ cURL错误: $error\n";
    } else {
        $data = json_decode($response, true);
        if ($data) {
            echo "   响应: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "   原始响应: " . substr($response, 0, 200) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 验证失败: " . $e->getMessage() . "\n";
}

echo "\n💡 分析结论:\n";
echo "1. 如果token不在Redis中，说明登录和成长API使用不同的认证系统\n";
echo "2. 如果token存在但格式不匹配，需要调整存储格式\n";
echo "3. 可能需要在登录后手动将token存储到正确的Redis键中\n";

?>
